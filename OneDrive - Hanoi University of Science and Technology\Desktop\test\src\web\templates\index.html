<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI Malware Detection System</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <!-- Custom CSS -->
    <link href="{{ url_for('static', filename='css/style.css') }}" rel="stylesheet">
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
        <div class="container">
            <a class="navbar-brand" href="#">
                <i class="fas fa-shield-alt me-2"></i>
                AI Malware Detection
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav ms-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="#upload">Kiểm tra File</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#about">Giới thiệu</a>
                    </li>
                    <li class="nav-item">
                        <span class="nav-link" id="status-indicator">
                            <i class="fas fa-circle text-warning"></i> Đang kiểm tra...
                        </span>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Hero Section -->
    <section class="hero-section bg-primary text-white py-5">
        <div class="container text-center">
            <h1 class="display-4 mb-4">
                <i class="fas fa-robot me-3"></i>
                Hệ thống Phát hiện Malware bằng AI
            </h1>
            <p class="lead mb-4">
                Sử dụng Machine Learning để phân tích và phát hiện phần mềm độc hại một cách thông minh và chính xác
            </p>
            <a href="#upload" class="btn btn-light btn-lg">
                <i class="fas fa-upload me-2"></i>
                Bắt đầu kiểm tra
            </a>
        </div>
    </section>

    <!-- Upload Section -->
    <section id="upload" class="py-5">
        <div class="container">
            <div class="row justify-content-center">
                <div class="col-lg-8">
                    <div class="card shadow">
                        <div class="card-header bg-primary text-white">
                            <h3 class="card-title mb-0">
                                <i class="fas fa-file-upload me-2"></i>
                                Upload File để Kiểm tra
                            </h3>
                        </div>
                        <div class="card-body">
                            <!-- Upload Form -->
                            <form id="uploadForm" enctype="multipart/form-data">
                                <div class="mb-4">
                                    <label for="fileInput" class="form-label">Chọn file PE (*.exe, *.dll, *.sys, ...)</label>
                                    <input type="file" class="form-control" id="fileInput" name="file" 
                                           accept=".exe,.dll,.sys,.scr,.com,.pif" required>
                                    <div class="form-text">
                                        Kích thước tối đa: 50MB. Chỉ chấp nhận file PE (Windows executables).
                                    </div>
                                </div>
                                
                                <div class="d-grid">
                                    <button type="submit" class="btn btn-primary btn-lg" id="analyzeBtn">
                                        <i class="fas fa-search me-2"></i>
                                        Phân tích File
                                    </button>
                                </div>
                            </form>

                            <!-- Progress Bar -->
                            <div id="progressContainer" class="mt-4" style="display: none;">
                                <div class="progress">
                                    <div class="progress-bar progress-bar-striped progress-bar-animated" 
                                         role="progressbar" style="width: 0%"></div>
                                </div>
                                <div class="text-center mt-2">
                                    <small class="text-muted">Đang phân tích file...</small>
                                </div>
                            </div>

                            <!-- Alert Messages -->
                            <div id="alertContainer" class="mt-4"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Results Section -->
    <section id="results" class="py-5 bg-light" style="display: none;">
        <div class="container">
            <div class="row">
                <div class="col-lg-8 mx-auto">
                    <div class="card shadow">
                        <div class="card-header">
                            <h3 class="card-title mb-0">
                                <i class="fas fa-chart-line me-2"></i>
                                Kết quả Phân tích
                            </h3>
                        </div>
                        <div class="card-body" id="resultsContent">
                            <!-- Results will be populated here -->
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- About Section -->
    <section id="about" class="py-5">
        <div class="container">
            <div class="row">
                <div class="col-lg-8 mx-auto text-center">
                    <h2 class="mb-4">Giới thiệu về Hệ thống</h2>
                    <p class="lead mb-4">
                        Hệ thống AI Malware Detection sử dụng các thuật toán Machine Learning tiên tiến 
                        để phân tích và phát hiện phần mềm độc hại.
                    </p>
                </div>
            </div>
            
            <div class="row mt-5">
                <div class="col-md-4 text-center mb-4">
                    <div class="feature-box">
                        <i class="fas fa-brain fa-3x text-primary mb-3"></i>
                        <h4>Machine Learning</h4>
                        <p>Sử dụng Random Forest và SVM để phân loại chính xác</p>
                    </div>
                </div>
                <div class="col-md-4 text-center mb-4">
                    <div class="feature-box">
                        <i class="fas fa-cogs fa-3x text-primary mb-3"></i>
                        <h4>Trích xuất Đặc trưng</h4>
                        <p>Phân tích PE header, sections, imports và entropy</p>
                    </div>
                </div>
                <div class="col-md-4 text-center mb-4">
                    <div class="feature-box">
                        <i class="fas fa-tachometer-alt fa-3x text-primary mb-3"></i>
                        <h4>Phân tích Nhanh</h4>
                        <p>Kết quả trong vài giây với độ chính xác cao</p>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="bg-dark text-white py-4">
        <div class="container text-center">
            <p class="mb-0">
                &copy; 2025 AI Malware Detection System. 
                Phát triển bằng Python, Flask và Machine Learning.
            </p>
        </div>
    </footer>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <!-- Custom JS -->
    <script src="{{ url_for('static', filename='js/main.js') }}"></script>
</body>
</html>
