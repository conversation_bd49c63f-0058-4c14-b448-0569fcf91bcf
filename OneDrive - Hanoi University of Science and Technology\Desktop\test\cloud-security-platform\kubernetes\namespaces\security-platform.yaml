apiVersion: v1
kind: Namespace
metadata:
  name: security-platform
  labels:
    name: security-platform
    app.kubernetes.io/name: security-platform
    app.kubernetes.io/version: "1.0.0"
    app.kubernetes.io/component: namespace
    app.kubernetes.io/part-of: cloud-security-platform
  annotations:
    description: "Namespace for Cloud Security Platform services"
---
apiVersion: v1
kind: ResourceQuota
metadata:
  name: security-platform-quota
  namespace: security-platform
spec:
  hard:
    requests.cpu: "10"
    requests.memory: 20Gi
    limits.cpu: "20"
    limits.memory: 40Gi
    persistentvolumeclaims: "10"
    services: "20"
    secrets: "20"
    configmaps: "20"
---
apiVersion: v1
kind: LimitRange
metadata:
  name: security-platform-limits
  namespace: security-platform
spec:
  limits:
  - default:
      cpu: "1000m"
      memory: "2Gi"
    defaultRequest:
      cpu: "200m"
      memory: "512Mi"
    type: Container
  - default:
      storage: "10Gi"
    type: PersistentVolumeClaim
