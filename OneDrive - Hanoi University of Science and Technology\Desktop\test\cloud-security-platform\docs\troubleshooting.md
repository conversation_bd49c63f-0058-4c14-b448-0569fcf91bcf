# Hướng dẫn Khắc phục Sự cố Cloud Security Platform

Tài liệu này cung cấp hướng dẫn khắc phục các sự cố thường gặp khi vận hành Cloud Security Platform.

## <PERSON><PERSON><PERSON> lục

1. [Sự cố triển khai](#sự-cố-triển-khai)
2. [Sự cố ứng dụng](#sự-cố-ứng-dụng)
3. [Sự cố cơ sở dữ liệu](#sự-cố-cơ-sở-dữ-liệu)
4. [Sự cố mạng](#sự-cố-mạng)
5. [Sự cố hiệu suất](#sự-cố-hiệu-suất)
6. [Sự cố bảo mật](#sự-cố-bảo-mật)
7. [Công cụ chẩn đoán](#công-cụ-chẩn-đoán)

## Sự cố triển khai

### Pod không khởi động được

**<PERSON>ệu chứng**: Pod ở trạng thái `Pending`, `<PERSON><PERSON>oopBackOff`, hoặc `ImagePullBackOff`

**Chẩ<PERSON> đo<PERSON>**:
```bash
# Kiểm tra trạng thái pod
kubectl get pods -n security-platform

# Xem chi tiết pod
kubectl describe pod <pod-name> -n security-platform

# Xem logs
kubectl logs <pod-name> -n security-platform
```

**Giải pháp**:

1. **ImagePullBackOff**:
```bash
# Kiểm tra image có tồn tại không
gcloud container images list --repository=gcr.io/YOUR_PROJECT_ID

# Kiểm tra quyền truy cập Container Registry
gcloud auth configure-docker
```

2. **CrashLoopBackOff**:
```bash
# Xem logs chi tiết
kubectl logs <pod-name> -n security-platform --previous

# Kiểm tra resource limits
kubectl describe pod <pod-name> -n security-platform | grep -A 5 "Limits"
```

3. **Pending**:
```bash
# Kiểm tra node resources
kubectl describe nodes

# Kiểm tra PVC
kubectl get pvc -n security-platform
```

### Service không accessible

**Triệu chứng**: Không thể truy cập service từ bên ngoài

**Chẩn đoán**:
```bash
# Kiểm tra service
kubectl get svc -n security-platform

# Kiểm tra endpoints
kubectl get endpoints -n security-platform

# Kiểm tra ingress
kubectl get ingress -n security-platform
```

**Giải pháp**:
```bash
# Kiểm tra selector của service
kubectl describe svc <service-name> -n security-platform

# Test kết nối internal
kubectl run test-pod --image=busybox --rm -it -- wget -qO- http://<service-name>:port

# Kiểm tra firewall rules
gcloud compute firewall-rules list
```

## Sự cố ứng dụng

### Malware Detection Service lỗi

**Triệu chứng**: API trả về lỗi 500, không thể upload file

**Chẩn đoán**:
```bash
# Xem logs service
kubectl logs -f deployment/malware-detection -n security-platform

# Kiểm tra health endpoint
kubectl exec -it <pod-name> -n security-platform -- curl http://localhost:5000/api/health
```

**Giải pháp**:

1. **Model không load được**:
```bash
# Kiểm tra models trong Cloud Storage
gsutil ls gs://YOUR_PROJECT_ID-models/

# Kiểm tra quyền truy cập Storage
kubectl describe pod <pod-name> -n security-platform | grep -A 10 "Environment"
```

2. **Memory issues**:
```bash
# Tăng memory limit
kubectl patch deployment malware-detection -n security-platform -p '{"spec":{"template":{"spec":{"containers":[{"name":"malware-detection","resources":{"limits":{"memory":"4Gi"}}}]}}}}'
```

### Network IDS Service lỗi

**Triệu chứng**: Không capture được packets, detection không hoạt động

**Chẩn đoán**:
```bash
# Kiểm tra network capabilities
kubectl exec -it <pod-name> -n security-platform -- ip link show

# Kiểm tra logs
kubectl logs -f deployment/network-ids -n security-platform
```

**Giải pháp**:

1. **Network permissions**:
```bash
# Kiểm tra security context
kubectl get pod <pod-name> -n security-platform -o yaml | grep -A 10 securityContext

# Cập nhật capabilities
kubectl patch deployment network-ids -n security-platform -p '{"spec":{"template":{"spec":{"containers":[{"name":"network-ids","securityContext":{"capabilities":{"add":["NET_ADMIN","NET_RAW"]}}}]}}}}'
```

### Dashboard không hiển thị dữ liệu

**Triệu chứng**: Dashboard trống, không có charts

**Chẩn đoán**:
```bash
# Kiểm tra API connectivity
kubectl exec -it <dashboard-pod> -n security-platform -- curl http://malware-detection-service:5000/api/status

# Kiểm tra logs
kubectl logs -f deployment/security-dashboard -n security-platform
```

**Giải pháp**:
```bash
# Restart dashboard
kubectl rollout restart deployment/security-dashboard -n security-platform

# Kiểm tra service discovery
kubectl get endpoints -n security-platform
```

## Sự cố cơ sở dữ liệu

### Không kết nối được Cloud SQL

**Triệu chứng**: Database connection errors

**Chẩn đoán**:
```bash
# Kiểm tra Cloud SQL proxy
kubectl logs <pod-name> -c cloudsql-proxy -n security-platform

# Test connection
kubectl exec -it <pod-name> -n security-platform -- nc -zv 127.0.0.1 5432
```

**Giải pháp**:

1. **Proxy configuration**:
```bash
# Kiểm tra connection name
gcloud sql instances describe security-platform-db

# Cập nhật proxy command
kubectl patch deployment malware-detection -n security-platform -p '{"spec":{"template":{"spec":{"containers":[{"name":"cloudsql-proxy","command":["/cloud_sql_proxy","-instances=YOUR_PROJECT_ID:asia-southeast1:security-platform-db=tcp:5432"]}]}}}}'
```

2. **Service account permissions**:
```bash
# Kiểm tra IAM roles
gcloud projects get-iam-policy YOUR_PROJECT_ID --flatten="bindings[].members" --filter="bindings.members:*cloudsql*"

# Thêm role nếu thiếu
gcloud projects add-iam-policy-binding YOUR_PROJECT_ID --member="serviceAccount:cloudsql-sa@YOUR_PROJECT_ID.iam.gserviceaccount.com" --role="roles/cloudsql.client"
```

### Database performance issues

**Triệu chứng**: Queries chậm, timeouts

**Chẩn đoán**:
```bash
# Kiểm tra Cloud SQL metrics
gcloud sql operations list --instance=security-platform-db

# Xem slow queries
gcloud sql instances describe security-platform-db
```

**Giải pháp**:
```bash
# Scale up database
gcloud sql instances patch security-platform-db --tier=db-custom-4-8192

# Optimize connections
kubectl patch deployment malware-detection -n security-platform -p '{"spec":{"template":{"spec":{"containers":[{"name":"malware-detection","env":[{"name":"DB_POOL_SIZE","value":"10"}]}]}}}}'
```

## Sự cố mạng

### Load Balancer không hoạt động

**Triệu chứng**: Không thể truy cập từ internet

**Chẩn đoán**:
```bash
# Kiểm tra ingress
kubectl describe ingress security-platform-ingress -n security-platform

# Kiểm tra backend health
gcloud compute backend-services get-health <backend-service-name> --global
```

**Giải pháp**:
```bash
# Kiểm tra health check
kubectl get backendconfig -n security-platform

# Cập nhật health check path
kubectl patch backendconfig security-platform-backend-config -n security-platform -p '{"spec":{"healthCheck":{"requestPath":"/health"}}}'
```

### SSL Certificate issues

**Triệu chứng**: HTTPS không hoạt động, certificate errors

**Chẩn đoán**:
```bash
# Kiểm tra managed certificate
kubectl get managedcertificate -n security-platform

# Xem chi tiết certificate
kubectl describe managedcertificate security-platform-ssl-cert -n security-platform
```

**Giải pháp**:
```bash
# Kiểm tra DNS pointing
nslookup security-platform.example.com

# Recreate certificate nếu cần
kubectl delete managedcertificate security-platform-ssl-cert -n security-platform
kubectl apply -f kubernetes/ingress/ssl-certificate.yaml
```

## Sự cố hiệu suất

### High CPU/Memory usage

**Triệu chứng**: Pods sử dụng quá nhiều resources

**Chẩn đoán**:
```bash
# Kiểm tra resource usage
kubectl top pods -n security-platform

# Xem metrics chi tiết
kubectl describe hpa -n security-platform
```

**Giải pháp**:
```bash
# Scale up manually
kubectl scale deployment malware-detection --replicas=5 -n security-platform

# Tăng resource limits
kubectl patch deployment malware-detection -n security-platform -p '{"spec":{"template":{"spec":{"containers":[{"name":"malware-detection","resources":{"limits":{"cpu":"2000m","memory":"4Gi"}}}]}}}}'
```

### Slow response times

**Triệu chứng**: API responses chậm

**Chẩn đoán**:
```bash
# Test response time
time curl -X GET http://<external-ip>/api/malware/status

# Kiểm tra database performance
kubectl exec -it <pod-name> -n security-platform -- psql -h 127.0.0.1 -U security_user -d security_platform -c "SELECT * FROM pg_stat_activity;"
```

**Giải pháp**:
```bash
# Enable caching
kubectl patch deployment security-dashboard -n security-platform -p '{"spec":{"template":{"spec":{"containers":[{"name":"security-dashboard","env":[{"name":"ENABLE_CACHE","value":"true"}]}]}}}}'

# Optimize database queries
kubectl exec -it <pod-name> -n security-platform -- psql -h 127.0.0.1 -U security_user -d security_platform -c "ANALYZE;"
```

## Sự cố bảo mật

### Authentication failures

**Triệu chứng**: 401/403 errors

**Chẩn đoán**:
```bash
# Kiểm tra service account
kubectl get serviceaccount -n security-platform

# Kiểm tra RBAC
kubectl auth can-i get pods --as=system:serviceaccount:security-platform:security-platform-sa -n security-platform
```

**Giải pháp**:
```bash
# Recreate service account binding
kubectl delete rolebinding security-platform-rolebinding -n security-platform
kubectl apply -f kubernetes/rbac/service-accounts.yaml
```

### Network policy blocking traffic

**Triệu chứng**: Services không thể communicate

**Chẩn đoán**:
```bash
# Kiểm tra network policies
kubectl get networkpolicy -n security-platform

# Test connectivity
kubectl exec -it <pod-name> -n security-platform -- nc -zv <target-service> <port>
```

**Giải pháp**:
```bash
# Temporarily disable network policy
kubectl delete networkpolicy security-platform-network-policy -n security-platform

# Update policy rules
kubectl apply -f kubernetes/security/network-policies.yaml
```

## Công cụ chẩn đoán

### Scripts hữu ích

1. **Health check script**:
```bash
#!/bin/bash
# health-check.sh

echo "=== Pod Status ==="
kubectl get pods -n security-platform

echo "=== Service Status ==="
kubectl get svc -n security-platform

echo "=== Ingress Status ==="
kubectl get ingress -n security-platform

echo "=== PVC Status ==="
kubectl get pvc -n security-platform

echo "=== Recent Events ==="
kubectl get events -n security-platform --sort-by='.lastTimestamp' | tail -10
```

2. **Log collection script**:
```bash
#!/bin/bash
# collect-logs.sh

mkdir -p logs/$(date +%Y%m%d_%H%M%S)
cd logs/$(date +%Y%m%d_%H%M%S)

# Collect pod logs
for pod in $(kubectl get pods -n security-platform -o name); do
    kubectl logs $pod -n security-platform > ${pod#pod/}.log
done

# Collect describe output
kubectl describe pods -n security-platform > pods-describe.txt
kubectl describe svc -n security-platform > services-describe.txt
kubectl describe ingress -n security-platform > ingress-describe.txt

echo "Logs collected in logs/$(date +%Y%m%d_%H%M%S)/"
```

### Monitoring queries

1. **Prometheus queries**:
```promql
# High error rate
rate(flask_http_request_exceptions_total[5m]) > 0.1

# High response time
histogram_quantile(0.95, rate(flask_http_request_duration_seconds_bucket[5m])) > 2

# Pod restarts
rate(kube_pod_container_status_restarts_total[15m]) > 0
```

2. **Grafana alerts**:
- Service down alerts
- High resource usage alerts
- Database connection alerts
- SSL certificate expiry alerts

### Emergency procedures

1. **Complete system restart**:
```bash
# Restart all deployments
kubectl rollout restart deployment -n security-platform

# Wait for rollout
kubectl rollout status deployment/malware-detection -n security-platform
kubectl rollout status deployment/network-ids -n security-platform
kubectl rollout status deployment/security-dashboard -n security-platform
```

2. **Emergency scale down**:
```bash
# Scale down to minimum
kubectl scale deployment malware-detection --replicas=1 -n security-platform
kubectl scale deployment network-ids --replicas=1 -n security-platform
kubectl scale deployment security-dashboard --replicas=1 -n security-platform
```

3. **Database emergency access**:
```bash
# Direct database access
kubectl run db-client --image=postgres:14 --rm -it -- psql -h <cloud-sql-ip> -U security_user -d security_platform
```

## Liên hệ hỗ trợ

- **Email**: <EMAIL>
- **Slack**: #security-platform
- **On-call**: +84-xxx-xxx-xxxx
- **Documentation**: https://docs.security-platform.com
