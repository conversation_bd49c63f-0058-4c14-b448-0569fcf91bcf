<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Network IDS Dashboard</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    
    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    
    <!-- Custom CSS -->
    <link href="{{ url_for('static', filename='css/dashboard.css') }}" rel="stylesheet">
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
        <div class="container-fluid">
            <a class="navbar-brand" href="#">
                <i class="fas fa-shield-alt"></i>
                Network IDS Dashboard
            </a>
            
            <div class="navbar-nav ms-auto">
                <div class="nav-item">
                    <span class="navbar-text">
                        <i class="fas fa-circle text-success" id="status-indicator"></i>
                        <span id="status-text">Connecting...</span>
                    </span>
                </div>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <div class="container-fluid mt-3">
        <!-- Control Panel -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5><i class="fas fa-cogs"></i> Control Panel</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="interface-select" class="form-label">Network Interface:</label>
                                    <select class="form-select" id="interface-select">
                                        <option value="">Loading...</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">Monitoring Control:</label>
                                    <div>
                                        <button class="btn btn-success me-2" id="start-monitoring">
                                            <i class="fas fa-play"></i> Start Monitoring
                                        </button>
                                        <button class="btn btn-danger" id="stop-monitoring" disabled>
                                            <i class="fas fa-stop"></i> Stop Monitoring
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Statistics Cards -->
        <div class="row mb-4">
            <div class="col-md-3">
                <div class="card bg-primary text-white">
                    <div class="card-body">
                        <div class="d-flex justify-content-between">
                            <div>
                                <h4 id="total-packets">0</h4>
                                <p class="mb-0">Total Packets</p>
                            </div>
                            <div class="align-self-center">
                                <i class="fas fa-network-wired fa-2x"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="col-md-3">
                <div class="card bg-warning text-white">
                    <div class="card-body">
                        <div class="d-flex justify-content-between">
                            <div>
                                <h4 id="active-alerts">0</h4>
                                <p class="mb-0">Active Alerts</p>
                            </div>
                            <div class="align-self-center">
                                <i class="fas fa-exclamation-triangle fa-2x"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="col-md-3">
                <div class="card bg-danger text-white">
                    <div class="card-body">
                        <div class="d-flex justify-content-between">
                            <div>
                                <h4 id="anomalies-detected">0</h4>
                                <p class="mb-0">Anomalies</p>
                            </div>
                            <div class="align-self-center">
                                <i class="fas fa-bug fa-2x"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="col-md-3">
                <div class="card bg-info text-white">
                    <div class="card-body">
                        <div class="d-flex justify-content-between">
                            <div>
                                <h4 id="intrusions-detected">0</h4>
                                <p class="mb-0">Intrusions</p>
                            </div>
                            <div class="align-self-center">
                                <i class="fas fa-user-ninja fa-2x"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Charts Row -->
        <div class="row mb-4">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5><i class="fas fa-chart-line"></i> Traffic Volume</h5>
                    </div>
                    <div class="card-body">
                        <canvas id="traffic-chart" height="200"></canvas>
                    </div>
                </div>
            </div>
            
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5><i class="fas fa-chart-pie"></i> Protocol Distribution</h5>
                    </div>
                    <div class="card-body">
                        <canvas id="protocol-chart" height="200"></canvas>
                    </div>
                </div>
            </div>
        </div>

        <!-- Alerts and Top Talkers -->
        <div class="row mb-4">
            <div class="col-md-8">
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5><i class="fas fa-bell"></i> Recent Alerts</h5>
                        <div>
                            <select class="form-select form-select-sm" id="alert-severity-filter">
                                <option value="">All Severities</option>
                                <option value="critical">Critical</option>
                                <option value="high">High</option>
                                <option value="medium">Medium</option>
                                <option value="low">Low</option>
                            </select>
                        </div>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-striped table-sm">
                                <thead>
                                    <tr>
                                        <th>Time</th>
                                        <th>Type</th>
                                        <th>Severity</th>
                                        <th>Source IP</th>
                                        <th>Message</th>
                                        <th>Action</th>
                                    </tr>
                                </thead>
                                <tbody id="alerts-table-body">
                                    <tr>
                                        <td colspan="6" class="text-center">No alerts</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="col-md-4">
                <div class="card">
                    <div class="card-header">
                        <h5><i class="fas fa-users"></i> Top Talkers</h5>
                    </div>
                    <div class="card-body">
                        <div id="top-talkers-list">
                            <p class="text-muted">No data available</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Models Status -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5><i class="fas fa-brain"></i> ML Models Status</h5>
                        <button class="btn btn-primary btn-sm" id="train-models">
                            <i class="fas fa-cog"></i> Train Models
                        </button>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="d-flex align-items-center mb-2">
                                    <i class="fas fa-circle text-secondary me-2" id="anomaly-model-status"></i>
                                    <span>Anomaly Detection Model</span>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="d-flex align-items-center mb-2">
                                    <i class="fas fa-circle text-secondary me-2" id="intrusion-model-status"></i>
                                    <span>Intrusion Classification Model</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Training Modal -->
    <div class="modal fade" id="training-modal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Train ML Models</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="model-type" class="form-label">Model Type:</label>
                        <select class="form-select" id="model-type">
                            <option value="both">Both Models</option>
                            <option value="anomaly">Anomaly Detection Only</option>
                            <option value="intrusion">Intrusion Classification Only</option>
                        </select>
                    </div>
                    <div class="mb-3">
                        <label for="algorithm" class="form-label">Algorithm (Optional):</label>
                        <input type="text" class="form-control" id="algorithm" placeholder="e.g., random_forest">
                    </div>
                    <div id="training-progress" class="d-none">
                        <div class="progress">
                            <div class="progress-bar progress-bar-striped progress-bar-animated" 
                                 role="progressbar" style="width: 100%"></div>
                        </div>
                        <p class="mt-2 text-center">Training in progress...</p>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="button" class="btn btn-primary" id="start-training">Start Training</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- Custom JS -->
    <script src="{{ url_for('static', filename='js/dashboard.js') }}"></script>
</body>
</html>
