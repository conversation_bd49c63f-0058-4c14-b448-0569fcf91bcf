# Cloud Storage and Database Configuration
# Cloud SQL, Cloud Storage buckets, and related resources

# Random password for database
resource "random_password" "db_password" {
  length  = 16
  special = true
}

# Cloud SQL Instance
resource "google_sql_database_instance" "main" {
  name             = "${var.project_name}-db"
  database_version = "POSTGRES_14"
  region          = var.region
  
  settings {
    tier                        = var.db_tier
    availability_type          = "REGIONAL"
    disk_type                  = "PD_SSD"
    disk_size                  = var.db_disk_size
    disk_autoresize           = true
    disk_autoresize_limit     = 100
    
    backup_configuration {
      enabled                        = true
      start_time                    = "02:00"
      location                      = var.region
      point_in_time_recovery_enabled = true
      transaction_log_retention_days = 7
      backup_retention_settings {
        retained_backups = 30
        retention_unit   = "COUNT"
      }
    }
    
    ip_configuration {
      ipv4_enabled                                  = false
      private_network                              = google_compute_network.vpc.id
      enable_private_path_for_google_cloud_services = true
    }
    
    database_flags {
      name  = "log_checkpoints"
      value = "on"
    }
    
    database_flags {
      name  = "log_connections"
      value = "on"
    }
    
    database_flags {
      name  = "log_disconnections"
      value = "on"
    }
    
    database_flags {
      name  = "log_lock_waits"
      value = "on"
    }
    
    maintenance_window {
      day          = 7
      hour         = 2
      update_track = "stable"
    }
    
    insights_config {
      query_insights_enabled  = true
      query_string_length    = 1024
      record_application_tags = true
      record_client_address  = true
    }
  }
  
  deletion_protection = var.environment == "production" ? true : false
  
  depends_on = [
    google_service_networking_connection.private_vpc_connection,
    google_project_service.required_apis
  ]
}

# Private VPC connection for Cloud SQL
resource "google_compute_global_address" "private_ip_address" {
  name          = "${var.project_name}-private-ip"
  purpose       = "VPC_PEERING"
  address_type  = "INTERNAL"
  prefix_length = 16
  network       = google_compute_network.vpc.id
}

resource "google_service_networking_connection" "private_vpc_connection" {
  network                 = google_compute_network.vpc.id
  service                 = "servicenetworking.googleapis.com"
  reserved_peering_ranges = [google_compute_global_address.private_ip_address.name]
}

# Database
resource "google_sql_database" "database" {
  name     = var.db_name
  instance = google_sql_database_instance.main.name
}

# Database user
resource "google_sql_user" "users" {
  name     = var.db_user
  instance = google_sql_database_instance.main.name
  password = random_password.db_password.result
}

# Cloud Storage Buckets
resource "google_storage_bucket" "models_bucket" {
  name          = "${var.project_id}-models"
  location      = var.region
  force_destroy = var.environment != "production"
  
  uniform_bucket_level_access = true
  
  versioning {
    enabled = true
  }
  
  lifecycle_rule {
    condition {
      age = 90
    }
    action {
      type = "Delete"
    }
  }
  
  lifecycle_rule {
    condition {
      age = 30
      num_newer_versions = 3
    }
    action {
      type = "Delete"
    }
  }
  
  labels = local.common_labels
}

resource "google_storage_bucket" "logs_bucket" {
  name          = "${var.project_id}-logs"
  location      = var.region
  force_destroy = var.environment != "production"
  
  uniform_bucket_level_access = true
  
  lifecycle_rule {
    condition {
      age = 30
    }
    action {
      type = "Delete"
    }
  }
  
  labels = local.common_labels
}

resource "google_storage_bucket" "uploads_bucket" {
  name          = "${var.project_id}-uploads"
  location      = var.region
  force_destroy = var.environment != "production"
  
  uniform_bucket_level_access = true
  
  versioning {
    enabled = true
  }
  
  lifecycle_rule {
    condition {
      age = 7
    }
    action {
      type = "Delete"
    }
  }
  
  labels = local.common_labels
}

resource "google_storage_bucket" "backups_bucket" {
  name          = "${var.project_id}-backups"
  location      = var.region
  force_destroy = var.environment != "production"
  
  uniform_bucket_level_access = true
  
  versioning {
    enabled = true
  }
  
  lifecycle_rule {
    condition {
      age = 365
    }
    action {
      type = "Delete"
    }
  }
  
  lifecycle_rule {
    condition {
      age = 30
      num_newer_versions = 5
    }
    action {
      type = "Delete"
    }
  }
  
  labels = local.common_labels
}

# Service Account for Cloud Storage access
resource "google_service_account" "storage_service_account" {
  account_id   = "${var.project_name}-storage-sa"
  display_name = "Storage Service Account"
  description  = "Service account for Cloud Storage access"
}

# IAM bindings for storage service account
resource "google_storage_bucket_iam_member" "models_bucket_access" {
  bucket = google_storage_bucket.models_bucket.name
  role   = "roles/storage.objectAdmin"
  member = "serviceAccount:${google_service_account.storage_service_account.email}"
}

resource "google_storage_bucket_iam_member" "logs_bucket_access" {
  bucket = google_storage_bucket.logs_bucket.name
  role   = "roles/storage.objectAdmin"
  member = "serviceAccount:${google_service_account.storage_service_account.email}"
}

resource "google_storage_bucket_iam_member" "uploads_bucket_access" {
  bucket = google_storage_bucket.uploads_bucket.name
  role   = "roles/storage.objectAdmin"
  member = "serviceAccount:${google_service_account.storage_service_account.email}"
}

resource "google_storage_bucket_iam_member" "backups_bucket_access" {
  bucket = google_storage_bucket.backups_bucket.name
  role   = "roles/storage.objectAdmin"
  member = "serviceAccount:${google_service_account.storage_service_account.email}"
}

# Service Account for Cloud SQL proxy
resource "google_service_account" "cloudsql_service_account" {
  account_id   = "${var.project_name}-cloudsql-sa"
  display_name = "Cloud SQL Proxy Service Account"
  description  = "Service account for Cloud SQL proxy"
}

resource "google_project_iam_member" "cloudsql_service_account_role" {
  project = var.project_id
  role    = "roles/cloudsql.client"
  member  = "serviceAccount:${google_service_account.cloudsql_service_account.email}"
}

# Secret Manager secrets
resource "google_secret_manager_secret" "db_password" {
  secret_id = "${var.project_name}-db-password"
  
  replication {
    automatic = true
  }
  
  labels = local.common_labels
}

resource "google_secret_manager_secret_version" "db_password" {
  secret      = google_secret_manager_secret.db_password.id
  secret_data = random_password.db_password.result
}

resource "google_secret_manager_secret" "app_secrets" {
  secret_id = "${var.project_name}-app-secrets"
  
  replication {
    automatic = true
  }
  
  labels = local.common_labels
}

resource "google_secret_manager_secret_version" "app_secrets" {
  secret = google_secret_manager_secret.app_secrets.id
  secret_data = jsonencode({
    malware_secret_key  = random_password.malware_secret_key.result
    ids_secret_key      = random_password.ids_secret_key.result
    dashboard_secret_key = random_password.dashboard_secret_key.result
    api_key            = random_password.api_key.result
    jwt_secret         = random_password.jwt_secret.result
  })
}

# Random passwords for application secrets
resource "random_password" "malware_secret_key" {
  length  = 32
  special = true
}

resource "random_password" "ids_secret_key" {
  length  = 32
  special = true
}

resource "random_password" "dashboard_secret_key" {
  length  = 32
  special = true
}

resource "random_password" "api_key" {
  length  = 32
  special = false
}

resource "random_password" "jwt_secret" {
  length  = 32
  special = true
}

# IAM for Secret Manager access
resource "google_secret_manager_secret_iam_member" "db_password_access" {
  secret_id = google_secret_manager_secret.db_password.secret_id
  role      = "roles/secretmanager.secretAccessor"
  member    = "serviceAccount:${google_service_account.gke_service_account.email}"
}

resource "google_secret_manager_secret_iam_member" "app_secrets_access" {
  secret_id = google_secret_manager_secret.app_secrets.secret_id
  role      = "roles/secretmanager.secretAccessor"
  member    = "serviceAccount:${google_service_account.gke_service_account.email}"
}
