# Network IDS Service Dockerfile
FROM python:3.9-slim

# Set working directory
WORKDIR /app

# Set environment variables
ENV PYTHONPATH=/app
ENV PYTHONUNBUFFERED=1
ENV FLASK_APP=src.api.ids_api
ENV FLASK_ENV=production

# Install system dependencies including network tools
RUN apt-get update && apt-get install -y \
    gcc \
    g++ \
    libc6-dev \
    libffi-dev \
    libssl-dev \
    libpcap-dev \
    tcpdump \
    net-tools \
    curl \
    && rm -rf /var/lib/apt/lists/*

# Create non-root user
RUN groupadd -r appuser && useradd -r -g appuser appuser

# Copy requirements first for better caching
COPY requirements.txt .

# Install Python dependencies
RUN pip install --no-cache-dir -r requirements.txt

# Copy application code
COPY src/ ./src/
COPY config.py .
COPY *.py .

# Create necessary directories
RUN mkdir -p data/models data/pcap data/datasets logs \
    && chown -R appuser:appuser /app

# Copy entrypoint script
COPY docker/network-ids/entrypoint.sh /entrypoint.sh
RUN chmod +x /entrypoint.sh

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \
    CMD curl -f http://localhost:5001/api/status || exit 1

# Note: For packet capture, container needs to run with NET_ADMIN capability
# This will be configured in Kubernetes deployment

# Switch to non-root user (but packet capture may need root privileges)
USER appuser

# Expose port
EXPOSE 5001

# Set entrypoint
ENTRYPOINT ["/entrypoint.sh"]

# Default command
CMD ["python", "src/api/ids_api.py"]
