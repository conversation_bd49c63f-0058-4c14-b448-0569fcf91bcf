"""
Training Script cho Network IDS Models
Script để train anomaly detection và intrusion classification models
"""

import sys
import os
import argparse
import logging
import pandas as pd
import numpy as np
from pathlib import Path
from datetime import datetime
import requests
import gzip
import shutil

# Add parent directory to path
sys.path.append(str(Path(__file__).parent.parent.parent))

from ml_models.anomaly_detector import AnomalyDetector
from ml_models.intrusion_classifier import IntrusionClassifier
import config


def setup_logging():
    """Setup logging configuration"""
    log_dir = config.LOGS_DIR
    log_dir.mkdir(parents=True, exist_ok=True)
    
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(log_dir / 'training.log'),
            logging.StreamHandler(sys.stdout)
        ]
    )


def download_kdd_dataset():
    """
    Download KDD Cup 99 dataset
    
    Returns:
        str: Path to downloaded dataset
    """
    logger = logging.getLogger(__name__)
    
    dataset_config = config.DATASET_CONFIG['kdd_cup_99']
    local_path = dataset_config['local_path']
    
    if local_path.exists():
        logger.info(f"Dataset đã tồn tại: {local_path}")
        return str(local_path)
    
    try:
        logger.info("Downloading KDD Cup 99 dataset...")
        
        # Download compressed file
        url = dataset_config['url']
        compressed_path = local_path.with_suffix('.gz')
        
        response = requests.get(url, stream=True)
        response.raise_for_status()
        
        with open(compressed_path, 'wb') as f:
            for chunk in response.iter_content(chunk_size=8192):
                f.write(chunk)
        
        # Decompress
        with gzip.open(compressed_path, 'rb') as f_in:
            with open(local_path, 'wb') as f_out:
                shutil.copyfileobj(f_in, f_out)
        
        # Remove compressed file
        compressed_path.unlink()
        
        logger.info(f"Dataset downloaded: {local_path}")
        return str(local_path)
        
    except Exception as e:
        logger.error(f"Lỗi khi download dataset: {e}")
        return None


def create_sample_dataset():
    """
    Tạo sample dataset cho testing
    
    Returns:
        pd.DataFrame: Sample dataset
    """
    logger = logging.getLogger(__name__)
    logger.info("Tạo sample dataset...")
    
    np.random.seed(42)
    
    # Network traffic features
    features = [
        'duration', 'protocol_type', 'service', 'flag', 'src_bytes', 'dst_bytes',
        'land', 'wrong_fragment', 'urgent', 'hot', 'num_failed_logins',
        'logged_in', 'num_compromised', 'root_shell', 'su_attempted',
        'num_root', 'num_file_creations', 'num_shells', 'num_access_files',
        'num_outbound_cmds', 'is_host_login', 'is_guest_login', 'count',
        'srv_count', 'serror_rate', 'srv_serror_rate', 'rerror_rate',
        'srv_rerror_rate', 'same_srv_rate', 'diff_srv_rate',
        'srv_diff_host_rate', 'dst_host_count', 'dst_host_srv_count',
        'dst_host_same_srv_rate', 'dst_host_diff_srv_rate',
        'dst_host_same_src_port_rate', 'dst_host_srv_diff_host_rate',
        'dst_host_serror_rate', 'dst_host_srv_serror_rate',
        'dst_host_rerror_rate', 'dst_host_srv_rerror_rate'
    ]
    
    n_samples = 5000
    data = []
    
    # Normal traffic (60%)
    n_normal = int(n_samples * 0.6)
    for _ in range(n_normal):
        sample = {
            'duration': np.random.exponential(10),
            'src_bytes': np.random.lognormal(8, 2),
            'dst_bytes': np.random.lognormal(6, 1.5),
            'count': np.random.poisson(5),
            'srv_count': np.random.poisson(3),
            'serror_rate': np.random.beta(1, 10),
            'rerror_rate': np.random.beta(1, 20),
            'same_srv_rate': np.random.beta(8, 2),
            'diff_srv_rate': np.random.beta(1, 5),
            'dst_host_count': np.random.poisson(10),
            'dst_host_srv_count': np.random.poisson(5),
            'label': 'normal'
        }
        
        # Fill remaining features with random values
        for feature in features:
            if feature not in sample:
                sample[feature] = np.random.normal(0, 1)
        
        data.append(sample)
    
    # DoS attacks (20%)
    n_dos = int(n_samples * 0.2)
    for _ in range(n_dos):
        sample = {
            'duration': np.random.exponential(1),  # Short duration
            'src_bytes': np.random.lognormal(4, 1),  # Small packets
            'dst_bytes': 0,  # No response
            'count': np.random.poisson(50),  # High count
            'srv_count': np.random.poisson(50),
            'serror_rate': np.random.beta(5, 1),  # High error rate
            'rerror_rate': np.random.beta(1, 1),
            'same_srv_rate': np.random.beta(10, 1),  # Same service
            'diff_srv_rate': np.random.beta(1, 10),
            'dst_host_count': np.random.poisson(100),  # High count
            'dst_host_srv_count': np.random.poisson(100),
            'label': 'dos'
        }
        
        for feature in features:
            if feature not in sample:
                sample[feature] = np.random.normal(1, 0.5)
        
        data.append(sample)
    
    # Probe attacks (10%)
    n_probe = int(n_samples * 0.1)
    for _ in range(n_probe):
        sample = {
            'duration': np.random.exponential(5),
            'src_bytes': np.random.lognormal(5, 1),
            'dst_bytes': np.random.lognormal(3, 1),
            'count': np.random.poisson(20),
            'srv_count': np.random.poisson(1),  # Different services
            'serror_rate': np.random.beta(2, 3),
            'rerror_rate': np.random.beta(3, 2),
            'same_srv_rate': np.random.beta(1, 5),  # Different services
            'diff_srv_rate': np.random.beta(5, 1),
            'dst_host_count': np.random.poisson(50),
            'dst_host_srv_count': np.random.poisson(10),
            'label': 'probe'
        }
        
        for feature in features:
            if feature not in sample:
                sample[feature] = np.random.normal(-0.5, 0.8)
        
        data.append(sample)
    
    # R2L attacks (5%)
    n_r2l = int(n_samples * 0.05)
    for _ in range(n_r2l):
        sample = {
            'duration': np.random.exponential(20),
            'src_bytes': np.random.lognormal(7, 1.5),
            'dst_bytes': np.random.lognormal(5, 1),
            'count': np.random.poisson(3),
            'srv_count': np.random.poisson(2),
            'serror_rate': np.random.beta(1, 5),
            'rerror_rate': np.random.beta(1, 10),
            'same_srv_rate': np.random.beta(3, 2),
            'diff_srv_rate': np.random.beta(2, 3),
            'dst_host_count': np.random.poisson(5),
            'dst_host_srv_count': np.random.poisson(3),
            'label': 'r2l'
        }
        
        for feature in features:
            if feature not in sample:
                sample[feature] = np.random.normal(0.5, 1.2)
        
        data.append(sample)
    
    # U2R attacks (5%)
    n_u2r = n_samples - n_normal - n_dos - n_probe - n_r2l
    for _ in range(n_u2r):
        sample = {
            'duration': np.random.exponential(15),
            'src_bytes': np.random.lognormal(6, 1),
            'dst_bytes': np.random.lognormal(4, 1),
            'count': np.random.poisson(2),
            'srv_count': np.random.poisson(1),
            'serror_rate': np.random.beta(1, 8),
            'rerror_rate': np.random.beta(1, 15),
            'same_srv_rate': np.random.beta(2, 3),
            'diff_srv_rate': np.random.beta(3, 2),
            'dst_host_count': np.random.poisson(3),
            'dst_host_srv_count': np.random.poisson(2),
            'label': 'u2r'
        }
        
        for feature in features:
            if feature not in sample:
                sample[feature] = np.random.normal(-0.2, 0.9)
        
        data.append(sample)
    
    df = pd.DataFrame(data)
    
    # Save dataset
    dataset_path = config.DATASETS_DIR / 'sample_network_data.csv'
    df.to_csv(dataset_path, index=False)
    
    logger.info(f"Sample dataset đã tạo: {dataset_path}")
    logger.info(f"Dataset shape: {df.shape}")
    logger.info(f"Label distribution:\n{df['label'].value_counts()}")
    
    return df


def train_anomaly_model(dataset_path, algorithm='isolation_forest'):
    """
    Train anomaly detection model
    
    Args:
        dataset_path (str): Path to dataset
        algorithm (str): Algorithm to use
        
    Returns:
        AnomalyDetector: Trained model
    """
    logger = logging.getLogger(__name__)
    logger.info(f"Training anomaly detection model: {algorithm}")
    
    # Load data
    df = pd.read_csv(dataset_path)
    
    # For anomaly detection, treat only 'normal' as normal, rest as anomalies
    df['anomaly_label'] = df['label'].apply(lambda x: 'normal' if x == 'normal' else 'anomaly')
    
    # Initialize detector
    detector = AnomalyDetector(algorithm)
    
    # Prepare data
    X, y = detector.prepare_data(df, 'anomaly_label')
    
    if X is None:
        logger.error("Không thể chuẩn bị dữ liệu")
        return None
    
    # Train model
    results = detector.train(X, y)
    
    if results:
        logger.info("Anomaly detection training results:")
        logger.info(f"  Algorithm: {results['algorithm']}")
        logger.info(f"  Training time: {results['training_time']:.2f}s")
        logger.info(f"  Training samples: {results['training_samples']}")
        
        if 'accuracy' in results:
            logger.info(f"  Accuracy: {results['accuracy']:.4f}")
        
        # Save model
        model_path = config.MODEL_DIR / f"{algorithm}_anomaly_model.joblib"
        scaler_path = config.MODEL_DIR / f"{algorithm}_anomaly_scaler.joblib"
        
        detector.save_model(str(model_path), str(scaler_path))
        
        return detector
    
    return None


def train_intrusion_model(dataset_path, algorithm='random_forest'):
    """
    Train intrusion classification model
    
    Args:
        dataset_path (str): Path to dataset
        algorithm (str): Algorithm to use
        
    Returns:
        IntrusionClassifier: Trained model
    """
    logger = logging.getLogger(__name__)
    logger.info(f"Training intrusion classification model: {algorithm}")
    
    # Load data
    df = pd.read_csv(dataset_path)
    
    # Initialize classifier
    classifier = IntrusionClassifier(algorithm)
    
    # Prepare data
    X, y = classifier.prepare_data(df, 'label')
    
    if X is None:
        logger.error("Không thể chuẩn bị dữ liệu")
        return None
    
    # Train model
    results = classifier.train(X, y)
    
    if results:
        logger.info("Intrusion classification training results:")
        logger.info(f"  Algorithm: {results['algorithm']}")
        logger.info(f"  Training time: {results['training_time']:.2f}s")
        logger.info(f"  Test accuracy: {results['test_accuracy']:.4f}")
        logger.info(f"  F1-score: {results['f1_score']:.4f}")
        
        # Save model
        model_path = config.MODEL_DIR / f"{algorithm}_intrusion_model.joblib"
        scaler_path = config.MODEL_DIR / f"{algorithm}_intrusion_scaler.joblib"
        
        classifier.save_model(str(model_path), str(scaler_path))
        
        return classifier
    
    return None


def main():
    """Main function"""
    parser = argparse.ArgumentParser(description='Train Network IDS Models')
    parser.add_argument('--model', type=str, choices=['anomaly', 'intrusion', 'both'],
                       default='both', help='Model type to train')
    parser.add_argument('--algorithm', type=str, help='Algorithm to use')
    parser.add_argument('--dataset', type=str, help='Path to dataset')
    parser.add_argument('--create-sample', action='store_true',
                       help='Create sample dataset')
    parser.add_argument('--download-kdd', action='store_true',
                       help='Download KDD Cup 99 dataset')
    
    args = parser.parse_args()
    
    # Setup
    setup_logging()
    config.create_directories()
    
    logger = logging.getLogger(__name__)
    
    try:
        # Create sample dataset if requested
        if args.create_sample:
            create_sample_dataset()
            return
        
        # Download KDD dataset if requested
        if args.download_kdd:
            download_kdd_dataset()
            return
        
        # Determine dataset path
        if args.dataset:
            dataset_path = args.dataset
        else:
            # Use sample dataset
            dataset_path = config.DATASETS_DIR / 'sample_network_data.csv'
            if not Path(dataset_path).exists():
                logger.info("Sample dataset không tồn tại, tạo mới...")
                create_sample_dataset()
        
        # Train models
        if args.model in ['anomaly', 'both']:
            algorithm = args.algorithm or 'isolation_forest'
            detector = train_anomaly_model(dataset_path, algorithm)
            
            if detector:
                logger.info("✅ Anomaly detection model trained successfully!")
            else:
                logger.error("❌ Anomaly detection training failed!")
        
        if args.model in ['intrusion', 'both']:
            algorithm = args.algorithm or 'random_forest'
            classifier = train_intrusion_model(dataset_path, algorithm)
            
            if classifier:
                logger.info("✅ Intrusion classification model trained successfully!")
            else:
                logger.error("❌ Intrusion classification training failed!")
        
        logger.info("🎉 Training completed!")
        
    except Exception as e:
        logger.error(f"💥 Error during training: {e}")
        raise


if __name__ == "__main__":
    main()
