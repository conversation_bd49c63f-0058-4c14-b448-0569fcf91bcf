"""
Integrated Security Dashboard Application
Dashboard tích hợp cho cả AI Malware Detection và Network IDS
"""

import os
import sys
import requests
import json
from datetime import datetime
from flask import Flask, render_template, jsonify, request
from flask_cors import CORS
import logging

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Initialize Flask app
app = Flask(__name__, 
           static_folder='static',
           template_folder='templates')
app.config['SECRET_KEY'] = os.environ.get('SECRET_KEY', 'dashboard-secret-key')
CORS(app)

# Service URLs
MALWARE_API_URL = os.environ.get('MALWARE_API_URL', 'http://malware-detection:5000')
IDS_API_URL = os.environ.get('IDS_API_URL', 'http://network-ids:5001')

# Request timeout
REQUEST_TIMEOUT = int(os.environ.get('REQUEST_TIMEOUT', '10'))


@app.route('/')
def index():
    """Main dashboard page"""
    return render_template('dashboard.html')


@app.route('/health')
def health():
    """Health check endpoint"""
    return jsonify({
        'status': 'healthy',
        'timestamp': datetime.now().isoformat(),
        'service': 'security-dashboard'
    })


@app.route('/api/system/status')
def system_status():
    """Get overall system status"""
    try:
        status = {
            'timestamp': datetime.now().isoformat(),
            'services': {}
        }
        
        # Check Malware Detection Service
        try:
            response = requests.get(f'{MALWARE_API_URL}/api/status', timeout=REQUEST_TIMEOUT)
            if response.status_code == 200:
                status['services']['malware_detection'] = {
                    'status': 'online',
                    'data': response.json()
                }
            else:
                status['services']['malware_detection'] = {
                    'status': 'error',
                    'error': f'HTTP {response.status_code}'
                }
        except Exception as e:
            status['services']['malware_detection'] = {
                'status': 'offline',
                'error': str(e)
            }
        
        # Check Network IDS Service
        try:
            response = requests.get(f'{IDS_API_URL}/api/status', timeout=REQUEST_TIMEOUT)
            if response.status_code == 200:
                status['services']['network_ids'] = {
                    'status': 'online',
                    'data': response.json()
                }
            else:
                status['services']['network_ids'] = {
                    'status': 'error',
                    'error': f'HTTP {response.status_code}'
                }
        except Exception as e:
            status['services']['network_ids'] = {
                'status': 'offline',
                'error': str(e)
            }
        
        # Determine overall status
        online_services = sum(1 for service in status['services'].values() 
                            if service['status'] == 'online')
        total_services = len(status['services'])
        
        if online_services == total_services:
            status['overall_status'] = 'healthy'
        elif online_services > 0:
            status['overall_status'] = 'degraded'
        else:
            status['overall_status'] = 'unhealthy'
        
        return jsonify(status)
        
    except Exception as e:
        logger.error(f"Error getting system status: {e}")
        return jsonify({
            'error': str(e),
            'timestamp': datetime.now().isoformat()
        }), 500


@app.route('/api/malware/statistics')
def malware_statistics():
    """Get malware detection statistics"""
    try:
        response = requests.get(f'{MALWARE_API_URL}/api/statistics', timeout=REQUEST_TIMEOUT)
        if response.status_code == 200:
            return jsonify(response.json())
        else:
            return jsonify({'error': f'Malware service error: HTTP {response.status_code}'}), response.status_code
    except Exception as e:
        logger.error(f"Error getting malware statistics: {e}")
        return jsonify({'error': str(e)}), 500


@app.route('/api/ids/monitoring/stats')
def ids_monitoring_stats():
    """Get Network IDS monitoring statistics"""
    try:
        response = requests.get(f'{IDS_API_URL}/api/monitoring/stats', timeout=REQUEST_TIMEOUT)
        if response.status_code == 200:
            return jsonify(response.json())
        else:
            return jsonify({'error': f'IDS service error: HTTP {response.status_code}'}), response.status_code
    except Exception as e:
        logger.error(f"Error getting IDS statistics: {e}")
        return jsonify({'error': str(e)}), 500


@app.route('/api/ids/alerts')
def ids_alerts():
    """Get Network IDS alerts"""
    try:
        # Forward query parameters
        params = request.args.to_dict()
        response = requests.get(f'{IDS_API_URL}/api/alerts', params=params, timeout=REQUEST_TIMEOUT)
        if response.status_code == 200:
            return jsonify(response.json())
        else:
            return jsonify({'error': f'IDS service error: HTTP {response.status_code}'}), response.status_code
    except Exception as e:
        logger.error(f"Error getting IDS alerts: {e}")
        return jsonify({'error': str(e)}), 500


@app.route('/api/integrated/dashboard')
def integrated_dashboard():
    """Get integrated dashboard data"""
    try:
        dashboard_data = {
            'timestamp': datetime.now().isoformat(),
            'malware_detection': {},
            'network_ids': {},
            'summary': {}
        }
        
        # Get malware detection data
        try:
            response = requests.get(f'{MALWARE_API_URL}/api/statistics', timeout=REQUEST_TIMEOUT)
            if response.status_code == 200:
                dashboard_data['malware_detection'] = response.json()
        except Exception as e:
            logger.warning(f"Could not get malware data: {e}")
            dashboard_data['malware_detection'] = {'error': str(e)}
        
        # Get Network IDS data
        try:
            response = requests.get(f'{IDS_API_URL}/api/monitoring/stats', timeout=REQUEST_TIMEOUT)
            if response.status_code == 200:
                dashboard_data['network_ids'] = response.json()
        except Exception as e:
            logger.warning(f"Could not get IDS data: {e}")
            dashboard_data['network_ids'] = {'error': str(e)}
        
        # Create summary
        malware_data = dashboard_data['malware_detection']
        ids_data = dashboard_data['network_ids']
        
        dashboard_data['summary'] = {
            'total_files_analyzed': malware_data.get('total_analyzed', 0),
            'malware_detected': malware_data.get('malware_count', 0),
            'network_packets': ids_data.get('network', {}).get('total_packets', 0),
            'network_alerts': len(ids_data.get('alerts', {}).get('recent_alerts', [])),
            'anomalies_detected': ids_data.get('detection', {}).get('anomalies_detected', 0),
            'intrusions_detected': ids_data.get('detection', {}).get('intrusions_detected', 0)
        }
        
        return jsonify(dashboard_data)
        
    except Exception as e:
        logger.error(f"Error getting integrated dashboard data: {e}")
        return jsonify({'error': str(e)}), 500


@app.route('/api/malware/upload', methods=['POST'])
def malware_upload():
    """Proxy malware file upload to malware detection service"""
    try:
        # Forward the file upload request
        files = {}
        data = {}
        
        # Handle file upload
        if 'file' in request.files:
            file = request.files['file']
            files['file'] = (file.filename, file.stream, file.content_type)
        
        # Handle form data
        for key, value in request.form.items():
            data[key] = value
        
        response = requests.post(
            f'{MALWARE_API_URL}/api/upload',
            files=files,
            data=data,
            timeout=REQUEST_TIMEOUT * 3  # Longer timeout for file upload
        )
        
        return jsonify(response.json()), response.status_code
        
    except Exception as e:
        logger.error(f"Error uploading file to malware service: {e}")
        return jsonify({'error': str(e)}), 500


@app.route('/api/ids/monitoring/start', methods=['POST'])
def start_ids_monitoring():
    """Start Network IDS monitoring"""
    try:
        data = request.get_json() or {}
        response = requests.post(
            f'{IDS_API_URL}/api/monitoring/start',
            json=data,
            timeout=REQUEST_TIMEOUT
        )
        return jsonify(response.json()), response.status_code
    except Exception as e:
        logger.error(f"Error starting IDS monitoring: {e}")
        return jsonify({'error': str(e)}), 500


@app.route('/api/ids/monitoring/stop', methods=['POST'])
def stop_ids_monitoring():
    """Stop Network IDS monitoring"""
    try:
        response = requests.post(f'{IDS_API_URL}/api/monitoring/stop', timeout=REQUEST_TIMEOUT)
        return jsonify(response.json()), response.status_code
    except Exception as e:
        logger.error(f"Error stopping IDS monitoring: {e}")
        return jsonify({'error': str(e)}), 500


@app.route('/api/threat-intelligence')
def threat_intelligence():
    """Get integrated threat intelligence report"""
    try:
        # This would integrate data from both services
        # For now, return a placeholder
        report = {
            'timestamp': datetime.now().isoformat(),
            'threat_level': 'medium',
            'active_threats': 5,
            'blocked_threats': 23,
            'recommendations': [
                'Update malware signatures',
                'Review network access policies',
                'Monitor suspicious IPs'
            ]
        }
        return jsonify(report)
    except Exception as e:
        logger.error(f"Error getting threat intelligence: {e}")
        return jsonify({'error': str(e)}), 500


@app.errorhandler(404)
def not_found(error):
    """404 error handler"""
    return jsonify({
        'error': 'Endpoint not found',
        'timestamp': datetime.now().isoformat()
    }), 404


@app.errorhandler(500)
def internal_error(error):
    """500 error handler"""
    return jsonify({
        'error': 'Internal server error',
        'timestamp': datetime.now().isoformat()
    }), 500


def main():
    """Main function"""
    host = os.environ.get('FLASK_HOST', '0.0.0.0')
    port = int(os.environ.get('FLASK_PORT', '8080'))
    debug = os.environ.get('FLASK_DEBUG', 'false').lower() == 'true'
    
    logger.info(f"Starting Integrated Security Dashboard on {host}:{port}")
    logger.info(f"Malware API URL: {MALWARE_API_URL}")
    logger.info(f"IDS API URL: {IDS_API_URL}")
    
    app.run(host=host, port=port, debug=debug, threaded=True)


if __name__ == '__main__':
    main()
