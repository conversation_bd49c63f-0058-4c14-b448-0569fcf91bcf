"""
Unit Tests for PE Analyzer
Tests cho module phân tích file PE
"""

import unittest
import os
import sys
from pathlib import Path
import tempfile
import shutil

# Add src to path
sys.path.append(str(Path(__file__).parent.parent / 'src'))

from feature_extraction.pe_analyzer import PEAnalyzer


class TestPEAnalyzer(unittest.TestCase):
    """Test cases cho PEAnalyzer class"""
    
    def setUp(self):
        """Setup test environment"""
        self.analyzer = PEAnalyzer()
        self.test_files_dir = Path(__file__).parent / 'test_files'
        self.test_files_dir.mkdir(exist_ok=True)
        
        # Sample PE file path (Windows notepad)
        self.sample_pe_file = "C:\\Windows\\System32\\notepad.exe"
        
    def test_analyzer_initialization(self):
        """Test analyzer initialization"""
        self.assertIsInstance(self.analyzer, PEAnalyzer)
        self.assertEqual(self.analyzer.features, {})
    
    def test_get_feature_names(self):
        """Test getting feature names"""
        feature_names = PEAnalyzer.get_feature_names()
        
        self.assertIsInstance(feature_names, list)
        self.assertGreater(len(feature_names), 0)
        
        # Check for expected features
        expected_features = [
            'file_size', 'is_dll', 'is_exe', 'number_of_sections',
            'file_entropy', 'import_dll_count'
        ]
        
        for feature in expected_features:
            self.assertIn(feature, feature_names)
    
    def test_analyze_nonexistent_file(self):
        """Test analyzing non-existent file"""
        result = self.analyzer.analyze_file("nonexistent_file.exe")
        self.assertIsNone(result)
    
    def test_analyze_valid_pe_file(self):
        """Test analyzing valid PE file"""
        if not os.path.exists(self.sample_pe_file):
            self.skipTest(f"Sample PE file not found: {self.sample_pe_file}")
        
        result = self.analyzer.analyze_file(self.sample_pe_file)
        
        self.assertIsNotNone(result)
        self.assertIsInstance(result, dict)
        
        # Check for required features
        required_features = [
            'file_size', 'md5', 'sha1', 'sha256',
            'is_exe', 'number_of_sections', 'file_entropy'
        ]
        
        for feature in required_features:
            self.assertIn(feature, result)
        
        # Check data types
        self.assertIsInstance(result['file_size'], int)
        self.assertIsInstance(result['md5'], str)
        self.assertIsInstance(result['is_exe'], bool)
        self.assertIsInstance(result['file_entropy'], float)
    
    def test_get_feature_vector(self):
        """Test getting feature vector"""
        if not os.path.exists(self.sample_pe_file):
            self.skipTest(f"Sample PE file not found: {self.sample_pe_file}")
        
        # Analyze file first
        self.analyzer.analyze_file(self.sample_pe_file)
        
        # Get feature vector
        feature_vector = self.analyzer.get_feature_vector()
        
        self.assertIsNotNone(feature_vector)
        self.assertIsInstance(feature_vector, list)
        
        # Check vector length matches feature names
        feature_names = PEAnalyzer.get_feature_names()
        self.assertEqual(len(feature_vector), len(feature_names))
        
        # Check all values are numeric
        for value in feature_vector:
            self.assertIsInstance(value, (int, float))
    
    def test_calculate_entropy(self):
        """Test entropy calculation"""
        # Test with known data
        test_data = b"AAAA"  # Low entropy
        entropy = self.analyzer._calculate_entropy(test_data)
        self.assertIsInstance(entropy, float)
        self.assertGreaterEqual(entropy, 0)
        self.assertLessEqual(entropy, 8)  # Max entropy for bytes
        
        # Empty data should return 0
        empty_entropy = self.analyzer._calculate_entropy(b"")
        self.assertEqual(empty_entropy, 0)
    
    def test_feature_extraction_components(self):
        """Test individual feature extraction components"""
        if not os.path.exists(self.sample_pe_file):
            self.skipTest(f"Sample PE file not found: {self.sample_pe_file}")
        
        # This is more of an integration test
        result = self.analyzer.analyze_file(self.sample_pe_file)
        
        # Test basic features
        self.assertGreater(result['file_size'], 0)
        self.assertEqual(len(result['md5']), 32)
        self.assertEqual(len(result['sha256']), 64)
        
        # Test PE-specific features
        self.assertIsInstance(result['number_of_sections'], int)
        self.assertGreaterEqual(result['number_of_sections'], 0)
        
        # Test entropy
        self.assertGreater(result['file_entropy'], 0)
        self.assertLess(result['file_entropy'], 8)


class TestPEAnalyzerEdgeCases(unittest.TestCase):
    """Test edge cases and error handling"""
    
    def setUp(self):
        self.analyzer = PEAnalyzer()
    
    def test_invalid_file_path(self):
        """Test with invalid file paths"""
        invalid_paths = [
            "",
            None,
            "/invalid/path/file.exe",
            "C:\\invalid\\path\\file.exe"
        ]
        
        for path in invalid_paths:
            if path is not None:
                result = self.analyzer.analyze_file(path)
                self.assertIsNone(result)
    
    def test_non_pe_file(self):
        """Test with non-PE file"""
        # Create a temporary text file
        with tempfile.NamedTemporaryFile(mode='w', suffix='.txt', delete=False) as f:
            f.write("This is not a PE file")
            temp_file = f.name
        
        try:
            result = self.analyzer.analyze_file(temp_file)
            # Should return None for non-PE files
            self.assertIsNone(result)
        finally:
            os.unlink(temp_file)
    
    def test_empty_features(self):
        """Test get_feature_vector with empty features"""
        # Without analyzing any file
        vector = self.analyzer.get_feature_vector()
        self.assertIsNone(vector)


class TestPEAnalyzerPerformance(unittest.TestCase):
    """Performance tests for PE Analyzer"""
    
    def setUp(self):
        self.analyzer = PEAnalyzer()
        self.sample_pe_file = "C:\\Windows\\System32\\notepad.exe"
    
    def test_analysis_performance(self):
        """Test analysis performance"""
        if not os.path.exists(self.sample_pe_file):
            self.skipTest(f"Sample PE file not found: {self.sample_pe_file}")
        
        import time
        
        start_time = time.time()
        result = self.analyzer.analyze_file(self.sample_pe_file)
        end_time = time.time()
        
        analysis_time = end_time - start_time
        
        self.assertIsNotNone(result)
        self.assertLess(analysis_time, 5.0)  # Should complete within 5 seconds
        
        print(f"Analysis completed in {analysis_time:.3f} seconds")


if __name__ == '__main__':
    # Create test suite
    suite = unittest.TestSuite()
    
    # Add test cases
    suite.addTest(unittest.makeSuite(TestPEAnalyzer))
    suite.addTest(unittest.makeSuite(TestPEAnalyzerEdgeCases))
    suite.addTest(unittest.makeSuite(TestPEAnalyzerPerformance))
    
    # Run tests
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(suite)
    
    # Print summary
    print(f"\nTests run: {result.testsRun}")
    print(f"Failures: {len(result.failures)}")
    print(f"Errors: {len(result.errors)}")
    
    if result.failures:
        print("\nFailures:")
        for test, traceback in result.failures:
            print(f"- {test}: {traceback}")
    
    if result.errors:
        print("\nErrors:")
        for test, traceback in result.errors:
            print(f"- {test}: {traceback}")
    
    # Exit with appropriate code
    sys.exit(0 if result.wasSuccessful() else 1)
