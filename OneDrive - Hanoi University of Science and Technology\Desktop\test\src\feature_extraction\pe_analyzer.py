"""
PE File Analyzer
Module phân tích file PE và trích xuất đặc trưng
"""

import os
import math
import hashlib
import pefile
import numpy as np
from collections import Counter
from pathlib import Path
import re


class PEAnalyzer:
    """Class phân tích file PE và trích xuất đặc trưng"""
    
    def __init__(self):
        self.features = {}
        
    def analyze_file(self, file_path):
        """
        Phân tích file PE và trích xuất tất cả đặc trưng
        
        Args:
            file_path (str): Đường dẫn đến file PE
            
        Returns:
            dict: Dictionary chứa các đặc trưng đã trích xuất
        """
        try:
            self.features = {}
            
            # Kiểm tra file tồn tại
            if not os.path.exists(file_path):
                raise FileNotFoundError(f"File không tồn tại: {file_path}")
            
            # Load PE file
            pe = pefile.PE(file_path)
            
            # Trích xuất các đặc trưng cơ bản
            self._extract_basic_features(pe, file_path)
            
            # Trích xuất đặc trưng từ header
            self._extract_header_features(pe)
            
            # Trích xuất đặc trưng từ sections
            self._extract_section_features(pe)
            
            # Trích xuất đặc trưng từ imports
            self._extract_import_features(pe)
            
            # Trích xuất đặc trưng entropy
            self._extract_entropy_features(file_path)
            
            # Trích xuất đặc trưng từ strings
            self._extract_string_features(file_path)
            
            pe.close()
            
            return self.features
            
        except Exception as e:
            print(f"Lỗi khi phân tích file {file_path}: {str(e)}")
            return None
    
    def _extract_basic_features(self, pe, file_path):
        """Trích xuất đặc trưng cơ bản"""
        # File size
        self.features['file_size'] = os.path.getsize(file_path)
        
        # File hashes
        with open(file_path, 'rb') as f:
            data = f.read()
            self.features['md5'] = hashlib.md5(data).hexdigest()
            self.features['sha1'] = hashlib.sha1(data).hexdigest()
            self.features['sha256'] = hashlib.sha256(data).hexdigest()
        
        # PE type
        self.features['is_dll'] = pe.is_dll()
        self.features['is_exe'] = pe.is_exe()
        self.features['is_driver'] = pe.is_driver()
        
    def _extract_header_features(self, pe):
        """Trích xuất đặc trưng từ PE header"""
        # DOS header
        self.features['e_magic'] = pe.DOS_HEADER.e_magic
        self.features['e_lfanew'] = pe.DOS_HEADER.e_lfanew
        
        # NT headers
        self.features['machine'] = pe.FILE_HEADER.Machine
        self.features['number_of_sections'] = pe.FILE_HEADER.NumberOfSections
        self.features['time_date_stamp'] = pe.FILE_HEADER.TimeDateStamp
        self.features['characteristics'] = pe.FILE_HEADER.Characteristics
        
        # Optional header
        if hasattr(pe, 'OPTIONAL_HEADER'):
            self.features['magic'] = pe.OPTIONAL_HEADER.Magic
            self.features['major_linker_version'] = pe.OPTIONAL_HEADER.MajorLinkerVersion
            self.features['minor_linker_version'] = pe.OPTIONAL_HEADER.MinorLinkerVersion
            self.features['size_of_code'] = pe.OPTIONAL_HEADER.SizeOfCode
            self.features['size_of_initialized_data'] = pe.OPTIONAL_HEADER.SizeOfInitializedData
            self.features['size_of_uninitialized_data'] = pe.OPTIONAL_HEADER.SizeOfUninitializedData
            self.features['address_of_entry_point'] = pe.OPTIONAL_HEADER.AddressOfEntryPoint
            self.features['base_of_code'] = pe.OPTIONAL_HEADER.BaseOfCode
            self.features['image_base'] = pe.OPTIONAL_HEADER.ImageBase
            self.features['section_alignment'] = pe.OPTIONAL_HEADER.SectionAlignment
            self.features['file_alignment'] = pe.OPTIONAL_HEADER.FileAlignment
            self.features['size_of_image'] = pe.OPTIONAL_HEADER.SizeOfImage
            self.features['size_of_headers'] = pe.OPTIONAL_HEADER.SizeOfHeaders
            self.features['checksum'] = pe.OPTIONAL_HEADER.CheckSum
            self.features['subsystem'] = pe.OPTIONAL_HEADER.Subsystem
            self.features['dll_characteristics'] = pe.OPTIONAL_HEADER.DllCharacteristics
    
    def _extract_section_features(self, pe):
        """Trích xuất đặc trưng từ sections"""
        sections_info = []
        
        for section in pe.sections:
            section_info = {
                'name': section.Name.decode('utf-8', errors='ignore').strip('\x00'),
                'virtual_size': section.Misc_VirtualSize,
                'virtual_address': section.VirtualAddress,
                'size_of_raw_data': section.SizeOfRawData,
                'pointer_to_raw_data': section.PointerToRawData,
                'characteristics': section.Characteristics,
                'entropy': self._calculate_entropy(section.get_data())
            }
            sections_info.append(section_info)
        
        self.features['sections'] = sections_info
        self.features['section_count'] = len(sections_info)
        
        # Thống kê về sections
        if sections_info:
            entropies = [s['entropy'] for s in sections_info]
            self.features['avg_section_entropy'] = np.mean(entropies)
            self.features['max_section_entropy'] = np.max(entropies)
            self.features['min_section_entropy'] = np.min(entropies)
    
    def _extract_import_features(self, pe):
        """Trích xuất đặc trưng từ import table"""
        imports = []
        import_count = 0
        
        try:
            if hasattr(pe, 'DIRECTORY_ENTRY_IMPORT'):
                for entry in pe.DIRECTORY_ENTRY_IMPORT:
                    dll_name = entry.dll.decode('utf-8', errors='ignore')
                    dll_imports = []
                    
                    for imp in entry.imports:
                        if imp.name:
                            func_name = imp.name.decode('utf-8', errors='ignore')
                            dll_imports.append(func_name)
                            import_count += 1
                    
                    imports.append({
                        'dll': dll_name,
                        'functions': dll_imports,
                        'function_count': len(dll_imports)
                    })
        except Exception as e:
            print(f"Lỗi khi trích xuất imports: {e}")
        
        self.features['imports'] = imports
        self.features['import_dll_count'] = len(imports)
        self.features['import_function_count'] = import_count
        
        # Suspicious imports
        suspicious_imports = [
            'CreateRemoteThread', 'WriteProcessMemory', 'VirtualAllocEx',
            'SetWindowsHookEx', 'GetProcAddress', 'LoadLibrary',
            'RegCreateKey', 'RegSetValue', 'CreateFile', 'WriteFile'
        ]
        
        suspicious_count = 0
        all_functions = []
        for imp in imports:
            all_functions.extend(imp['functions'])
        
        for func in all_functions:
            if any(sus in func for sus in suspicious_imports):
                suspicious_count += 1
        
        self.features['suspicious_import_count'] = suspicious_count
    
    def _extract_entropy_features(self, file_path):
        """Trích xuất đặc trưng entropy"""
        with open(file_path, 'rb') as f:
            data = f.read()
        
        self.features['file_entropy'] = self._calculate_entropy(data)
    
    def _extract_string_features(self, file_path):
        """Trích xuất đặc trưng từ strings"""
        with open(file_path, 'rb') as f:
            data = f.read()
        
        # Tìm ASCII strings
        ascii_strings = re.findall(b'[ -~]{4,}', data)
        unicode_strings = re.findall(b'(?:[ -~]\x00){4,}', data)
        
        self.features['ascii_string_count'] = len(ascii_strings)
        self.features['unicode_string_count'] = len(unicode_strings)
        
        # Suspicious strings
        suspicious_patterns = [
            b'cmd.exe', b'powershell', b'regedit', b'taskkill',
            b'http://', b'https://', b'ftp://', b'.exe', b'.dll',
            b'temp', b'system32', b'registry', b'mutex'
        ]
        
        suspicious_string_count = 0
        all_strings = ascii_strings + unicode_strings
        
        for string in all_strings:
            for pattern in suspicious_patterns:
                if pattern.lower() in string.lower():
                    suspicious_string_count += 1
                    break
        
        self.features['suspicious_string_count'] = suspicious_string_count
    
    def _calculate_entropy(self, data):
        """Tính entropy của dữ liệu"""
        if not data:
            return 0
        
        # Đếm tần suất các byte
        byte_counts = Counter(data)
        data_len = len(data)
        
        # Tính entropy
        entropy = 0
        for count in byte_counts.values():
            probability = count / data_len
            if probability > 0:
                entropy -= probability * math.log2(probability)
        
        return entropy
    
    def get_feature_vector(self):
        """
        Chuyển đổi features thành vector số để sử dụng cho ML
        
        Returns:
            list: Feature vector
        """
        if not self.features:
            return None
        
        # Chọn các features quan trọng cho ML
        feature_vector = [
            self.features.get('file_size', 0),
            int(self.features.get('is_dll', False)),
            int(self.features.get('is_exe', False)),
            int(self.features.get('is_driver', False)),
            self.features.get('number_of_sections', 0),
            self.features.get('size_of_code', 0),
            self.features.get('size_of_initialized_data', 0),
            self.features.get('size_of_uninitialized_data', 0),
            self.features.get('address_of_entry_point', 0),
            self.features.get('section_count', 0),
            self.features.get('avg_section_entropy', 0),
            self.features.get('max_section_entropy', 0),
            self.features.get('min_section_entropy', 0),
            self.features.get('import_dll_count', 0),
            self.features.get('import_function_count', 0),
            self.features.get('suspicious_import_count', 0),
            self.features.get('file_entropy', 0),
            self.features.get('ascii_string_count', 0),
            self.features.get('unicode_string_count', 0),
            self.features.get('suspicious_string_count', 0)
        ]
        
        return feature_vector


    @staticmethod
    def get_feature_names():
        """
        Trả về tên các features trong feature vector

        Returns:
            list: Danh sách tên features
        """
        return [
            'file_size', 'is_dll', 'is_exe', 'is_driver', 'number_of_sections',
            'size_of_code', 'size_of_initialized_data', 'size_of_uninitialized_data',
            'address_of_entry_point', 'section_count', 'avg_section_entropy',
            'max_section_entropy', 'min_section_entropy', 'import_dll_count',
            'import_function_count', 'suspicious_import_count', 'file_entropy',
            'ascii_string_count', 'unicode_string_count', 'suspicious_string_count'
        ]


if __name__ == "__main__":
    # Test với file mẫu
    analyzer = PEAnalyzer()

    # Thay đổi đường dẫn này thành file PE thực tế để test
    test_file = "C:\\Windows\\System32\\notepad.exe"

    if os.path.exists(test_file):
        features = analyzer.analyze_file(test_file)
        if features:
            print("Đặc trưng đã trích xuất:")
            for key, value in features.items():
                if key != 'sections' and key != 'imports':  # Skip complex objects
                    print(f"{key}: {value}")

            print("\nFeature vector:")
            vector = analyzer.get_feature_vector()
            print(vector)

            print("\nFeature names:")
            print(PEAnalyzer.get_feature_names())
    else:
        print(f"File test không tồn tại: {test_file}")
