# Network Analysis
scapy==2.5.0
pyshark==0.6
netfilterqueue==0.8.1
psutil==5.9.5

# Machine Learning
scikit-learn==1.3.0
tensorflow==2.13.0
numpy==1.24.3
pandas==1.5.3
matplotlib==3.7.1
seaborn==0.12.2

# Deep Learning
keras==2.13.1
torch==2.0.1
torchvision==0.15.2

# Web Framework
Flask==2.3.2
Flask-CORS==4.0.0
Flask-SocketIO==5.3.4
python-socketio==5.8.0

# Database
SQLAlchemy==2.0.19
Flask-SQLAlchemy==3.0.5

# Data Processing
scipy==1.11.1
joblib==1.3.1
tqdm==4.65.0

# Network Utilities
requests==2.31.0
urllib3==2.0.4
dnspython==2.4.2

# Visualization
plotly==5.15.0
dash==2.11.1
bokeh==3.2.1

# Security
cryptography==41.0.3
bcrypt==4.0.1

# Configuration
pyyaml==6.0.1
configparser==5.3.0

# Logging
loguru==0.7.0
colorlog==6.7.0

# Time handling
python-dateutil==2.8.2
pytz==2023.3

# System monitoring
netifaces==0.11.0
speedtest-cli==2.1.3

# Alert system
smtplib2==0.2.1
slack-sdk==3.21.3
twilio==8.5.0

# File handling
pathlib2==2.3.7
watchdog==3.0.0

# Testing
pytest==7.4.0
pytest-cov==4.1.0
pytest-mock==3.11.1

# Development tools
black==23.7.0
flake8==6.0.0
mypy==1.4.1

# Performance
cython==3.0.0
numba==0.57.1

# Optional: GPU support (uncomment if needed)
# tensorflow-gpu==2.13.0
# torch-gpu==2.0.1
