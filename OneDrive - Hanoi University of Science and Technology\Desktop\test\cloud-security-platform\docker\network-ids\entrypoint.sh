#!/bin/bash
set -e

echo "🚀 Starting Network IDS Service..."

# Wait for database if DATABASE_URL is provided
if [ ! -z "$DATABASE_URL" ]; then
    echo "⏳ Waiting for database connection..."
    python -c "
import time
import sys
import os
from sqlalchemy import create_engine
from sqlalchemy.exc import OperationalError

database_url = os.environ.get('DATABASE_URL')
if database_url:
    engine = create_engine(database_url)
    for i in range(30):
        try:
            engine.execute('SELECT 1')
            print('✅ Database connection successful')
            break
        except OperationalError:
            print(f'⏳ Waiting for database... ({i+1}/30)')
            time.sleep(2)
    else:
        print('❌ Database connection failed')
        sys.exit(1)
"
fi

# Check network capabilities
echo "🔍 Checking network capabilities..."
if [ -w /proc/sys/net ]; then
    echo "✅ Network capabilities available"
else
    echo "⚠️  Limited network capabilities - packet capture may not work"
fi

# List available network interfaces
echo "🌐 Available network interfaces:"
python -c "
try:
    import psutil
    for interface, addrs in psutil.net_if_addrs().items():
        for addr in addrs:
            if addr.family == 2:  # IPv4
                print(f'  - {interface}: {addr.address}')
                break
except Exception as e:
    print(f'  Error listing interfaces: {e}')
"

# Check if models exist, if not create sample data and train
echo "🧠 Checking ML models..."
python -c "
import os
from pathlib import Path

model_dir = Path('data/models')
if not model_dir.exists() or not list(model_dir.glob('*.joblib')):
    print('⚠️  No models found, creating sample data and training...')
    try:
        from src.ml_models.train_models import main as train_main
        import sys
        sys.argv = ['train_models.py', '--create-sample']
        train_main()
        
        # Train both models
        sys.argv = ['train_models.py', '--model', 'both']
        train_main()
        print('✅ Models trained successfully')
    except Exception as e:
        print(f'❌ Model training failed: {e}')
else:
    print('✅ Models found')
"

# Initialize configuration
echo "⚙️  Initializing configuration..."
python -c "
try:
    import config
    config.create_directories()
    print('✅ Configuration initialized')
except Exception as e:
    print(f'⚠️  Configuration initialization: {e}')
"

# Set default values for environment variables
export FLASK_HOST=${FLASK_HOST:-0.0.0.0}
export FLASK_PORT=${FLASK_PORT:-5001}
export FLASK_DEBUG=${FLASK_DEBUG:-false}

# Network interface configuration
export NETWORK_INTERFACE=${NETWORK_INTERFACE:-eth0}

echo "🌐 Starting Flask application on ${FLASK_HOST}:${FLASK_PORT}"
echo "🔧 Debug mode: ${FLASK_DEBUG}"
echo "🌐 Network interface: ${NETWORK_INTERFACE}"

# Execute the main command
exec "$@"
