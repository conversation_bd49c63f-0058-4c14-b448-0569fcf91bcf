# 🌐 Network Intrusion Detection System (IDS)

Hệ thống phát hiện xâm nhập mạng sử dụng Machine Learning và Deep Learning, tích hợp với AI Malware Detection System.

![Python](https://img.shields.io/badge/Python-3.8+-blue.svg)
![Scapy](https://img.shields.io/badge/Scapy-2.5+-green.svg)
![TensorFlow](https://img.shields.io/badge/TensorFlow-2.13+-orange.svg)
![License](https://img.shields.io/badge/License-MIT-yellow.svg)

## ✨ Tính năng chính

- 🔍 **Real-time Traffic Analysis**: Phân tích traffic mạng thời gian thực
- 🤖 **ML-based Detection**: Sử dụng Random Forest, SVM, Neural Networks
- 🚨 **Anomaly Detection**: Phát hiện bất thường trong traffic patterns
- 📊 **Protocol Analysis**: Phân tích TCP, UDP, ICMP, HTTP, DNS
- 🌐 **Network Monitoring**: Monitor bandwidth, connections, ports
- 📈 **Real-time Dashboard**: Dashboard hiển thị alerts và statistics
- 🔗 **Integration**: Tích hợp với AI Malware Detection System
- 📝 **Logging & Alerts**: Hệ thống cảnh báo và logging chi tiết

## 🏗️ Cấu trúc dự án

```
network-ids/
├── src/
│   ├── traffic_analyzer/       # Module phân tích traffic
│   │   ├── packet_capture.py  # Capture packets
│   │   ├── feature_extractor.py # Trích xuất features
│   │   └── protocol_analyzer.py # Phân tích protocols
│   ├── ml_models/             # ML models cho IDS
│   │   ├── anomaly_detector.py # Anomaly detection
│   │   ├── intrusion_classifier.py # Intrusion classification
│   │   └── train_models.py    # Training scripts
│   ├── monitoring/            # Real-time monitoring
│   │   ├── network_monitor.py # Network monitoring
│   │   ├── alert_system.py    # Alert system
│   │   └── real_time_detector.py # Real-time detection
│   ├── api/                   # API endpoints
│   │   ├── ids_api.py        # IDS API
│   │   └── integration_api.py # Integration với malware detection
│   └── dashboard/             # Web dashboard
│       ├── templates/         # HTML templates
│       └── static/           # CSS, JS files
├── data/
│   ├── pcap/                 # PCAP files
│   ├── datasets/             # Training datasets
│   └── models/               # Trained models
├── tests/                    # Unit tests
├── docs/                     # Documentation
└── logs/                     # Log files
```

## 🚀 Quick Start

### 1. Cài đặt

```bash
cd network-ids

# Cài đặt dependencies
pip install -r requirements.txt

# Cài đặt Npcap (Windows) hoặc libpcap (Linux)
# Windows: Tải từ https://npcap.com/
# Linux: sudo apt-get install libpcap-dev
```

### 2. Cấu hình

```bash
# Tạo thư mục cần thiết
python config.py

# Cấu hình network interface
python src/traffic_analyzer/packet_capture.py --list-interfaces
```

### 3. Train Models

```bash
# Tạo sample dataset
python src/ml_models/train_models.py --create-sample

# Train anomaly detection model
python src/ml_models/train_models.py --model anomaly

# Train intrusion classification model
python src/ml_models/train_models.py --model intrusion
```

### 4. Chạy hệ thống

```bash
# Khởi động monitoring
python src/monitoring/network_monitor.py

# Khởi động API server
python src/api/ids_api.py

# Truy cập dashboard: http://localhost:5001
```

## 🔧 Tính năng Detection

### Attack Types Detected
- **DoS/DDoS**: Denial of Service attacks
- **Port Scanning**: Nmap, masscan detection
- **Brute Force**: SSH, FTP, HTTP brute force
- **SQL Injection**: Web application attacks
- **Malware Communication**: C&C traffic detection
- **Data Exfiltration**: Unusual data transfer patterns
- **Network Reconnaissance**: Network mapping attempts

### ML Algorithms Used
- **Random Forest**: Classification và feature importance
- **SVM**: Support Vector Machine cho binary classification
- **Isolation Forest**: Anomaly detection
- **LSTM**: Sequential pattern analysis
- **Autoencoder**: Unsupervised anomaly detection

## 📊 Features Extracted

### Network Layer Features
- Packet size distribution
- Inter-arrival times
- Protocol distribution
- Flow duration
- Bytes per second

### Transport Layer Features
- TCP flags analysis
- Port numbers
- Connection states
- Window sizes
- Sequence numbers

### Application Layer Features
- HTTP methods và status codes
- DNS query types
- Payload entropy
- String patterns
- Header analysis

## 🔗 Integration với AI Malware Detection

### API Integration
```python
# Gửi suspicious files từ network traffic đến malware detector
import requests

def check_suspicious_file(file_data):
    response = requests.post(
        'http://localhost:5000/api/upload',
        files={'file': file_data}
    )
    return response.json()
```

### Shared Database
- Chung database để correlate network events với malware detection
- Cross-reference IP addresses và file hashes
- Timeline analysis của attacks

### Unified Dashboard
- Dashboard tích hợp hiển thị cả network intrusions và malware detections
- Correlation analysis
- Incident response workflow

## 🛠️ Công nghệ sử dụng

### Network Analysis
- **Scapy**: Packet manipulation và analysis
- **PyShark**: Wireshark integration
- **Netfilterqueue**: Real-time packet filtering
- **psutil**: System và network monitoring

### Machine Learning
- **scikit-learn**: Traditional ML algorithms
- **TensorFlow/Keras**: Deep Learning models
- **pandas**: Data manipulation
- **numpy**: Numerical computing

### Web & API
- **Flask**: Web framework
- **Socket.IO**: Real-time communication
- **Chart.js**: Data visualization
- **Bootstrap**: UI framework

## 📈 Performance

- **Packet Processing**: 10,000+ packets/second
- **Real-time Detection**: < 100ms latency
- **Memory Usage**: < 1GB RAM
- **CPU Usage**: < 20% on modern systems
- **Detection Accuracy**: 95%+ trên test datasets

## 🔒 Security Features

- **Encrypted Communication**: TLS/SSL cho API calls
- **Access Control**: Role-based authentication
- **Audit Logging**: Comprehensive audit trails
- **Data Privacy**: Anonymization của sensitive data
- **Secure Storage**: Encrypted storage cho models và logs

## 🚨 Alert System

### Alert Types
- **Critical**: Confirmed attacks requiring immediate action
- **High**: Suspicious activities needing investigation
- **Medium**: Anomalies worth monitoring
- **Low**: Informational events

### Notification Channels
- **Email**: SMTP notifications
- **Slack**: Webhook integration
- **SMS**: Twilio integration
- **Dashboard**: Real-time web notifications

## 📝 Configuration

```python
# config.py
NETWORK_CONFIG = {
    'interface': 'eth0',  # Network interface to monitor
    'promiscuous_mode': True,
    'buffer_size': 65536,
    'timeout': 1000
}

ML_CONFIG = {
    'model_update_interval': 3600,  # 1 hour
    'anomaly_threshold': 0.8,
    'batch_size': 1000,
    'feature_window': 60  # seconds
}

ALERT_CONFIG = {
    'email_enabled': True,
    'slack_webhook': 'https://hooks.slack.com/...',
    'alert_cooldown': 300  # 5 minutes
}
```

## 🧪 Testing

```bash
# Chạy unit tests
python -m pytest tests/

# Test với sample PCAP files
python tests/test_traffic_analyzer.py

# Performance testing
python tests/performance_test.py

# Integration testing với malware detection
python tests/test_integration.py
```

## 📚 Documentation

- **[Installation Guide](docs/INSTALLATION.md)**: Hướng dẫn cài đặt chi tiết
- **[User Manual](docs/USER_MANUAL.md)**: Hướng dẫn sử dụng
- **[API Documentation](docs/API.md)**: Tài liệu API
- **[Integration Guide](docs/INTEGRATION.md)**: Tích hợp với malware detection

## 🤝 Contributing

1. Fork repository
2. Tạo feature branch
3. Commit changes
4. Push to branch
5. Create Pull Request

## 📄 License

MIT License - xem file LICENSE để biết chi tiết.

## 👥 Authors

- **Network Security Team** - *Initial work*

## 🙏 Acknowledgments

- **Scapy**: Network packet manipulation
- **scikit-learn**: Machine Learning algorithms
- **TensorFlow**: Deep Learning framework
- **Flask**: Web application framework

---

⭐ **Nếu project này hữu ích, hãy cho chúng tôi một star!** ⭐
