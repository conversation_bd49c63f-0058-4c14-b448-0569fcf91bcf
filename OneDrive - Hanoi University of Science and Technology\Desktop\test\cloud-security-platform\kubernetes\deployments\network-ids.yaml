apiVersion: apps/v1
kind: Deployment
metadata:
  name: network-ids
  namespace: security-platform
  labels:
    app: network-ids
    component: api
    version: v1
spec:
  replicas: 3
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxUnavailable: 1
      maxSurge: 1
  selector:
    matchLabels:
      app: network-ids
  template:
    metadata:
      labels:
        app: network-ids
        component: api
        version: v1
      annotations:
        prometheus.io/scrape: "true"
        prometheus.io/port: "9090"
        prometheus.io/path: "/metrics"
    spec:
      serviceAccountName: security-platform-sa
      securityContext:
        runAsNonRoot: true
        runAsUser: 1000
        fsGroup: 1000
      containers:
      - name: network-ids
        image: gcr.io/security-platform-prod/network-ids:latest
        imagePullPolicy: Always
        ports:
        - containerPort: 5001
          name: http
          protocol: TCP
        - containerPort: 9090
          name: metrics
          protocol: TCP
        env:
        - name: DATABASE_URL
          value: "postgresql://$(DB_USER):$(DB_PASSWORD)@127.0.0.1:5432/$(DB_NAME)"
        envFrom:
        - configMapRef:
            name: network-ids-config
        - configMapRef:
            name: shared-config
        - secretRef:
            name: database-secrets
        - secretRef:
            name: app-secrets
        - secretRef:
            name: cloud-secrets
        resources:
          requests:
            cpu: 500m
            memory: 1Gi
          limits:
            cpu: 2000m
            memory: 4Gi
        livenessProbe:
          httpGet:
            path: /api/status
            port: 5001
          initialDelaySeconds: 60
          periodSeconds: 30
          timeoutSeconds: 10
          failureThreshold: 3
        readinessProbe:
          httpGet:
            path: /api/status
            port: 5001
          initialDelaySeconds: 30
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 3
        volumeMounts:
        - name: models-storage
          mountPath: /app/data/models
        - name: pcap-storage
          mountPath: /app/data/pcap
        - name: datasets-storage
          mountPath: /app/data/datasets
        - name: logs-storage
          mountPath: /app/logs
        # Special capabilities for network monitoring
        securityContext:
          capabilities:
            add:
            - NET_ADMIN
            - NET_RAW
          privileged: false
      
      # Cloud SQL Proxy sidecar
      - name: cloudsql-proxy
        image: gcr.io/cloudsql-docker/gce-proxy:1.33.2
        command:
        - "/cloud_sql_proxy"
        - "-instances=security-platform-prod:asia-southeast1:security-platform-db=tcp:5432"
        - "-credential_file=/secrets/cloudsql/credentials.json"
        securityContext:
          runAsNonRoot: true
        volumeMounts:
        - name: cloudsql-instance-credentials
          mountPath: /secrets/cloudsql
          readOnly: true
        resources:
          requests:
            cpu: 100m
            memory: 128Mi
          limits:
            cpu: 200m
            memory: 256Mi
      
      volumes:
      - name: models-storage
        persistentVolumeClaim:
          claimName: models-pvc
      - name: pcap-storage
        persistentVolumeClaim:
          claimName: pcap-pvc
      - name: datasets-storage
        persistentVolumeClaim:
          claimName: datasets-pvc
      - name: logs-storage
        persistentVolumeClaim:
          claimName: logs-pvc
      - name: cloudsql-instance-credentials
        secret:
          secretName: cloudsql-instance-credentials
      
      nodeSelector:
        cloud.google.com/gke-nodepool: security-platform-nodes
      
      tolerations:
      - key: "security-workload"
        operator: "Equal"
        value: "true"
        effect: "NoSchedule"
      
      affinity:
        podAntiAffinity:
          preferredDuringSchedulingIgnoredDuringExecution:
          - weight: 100
            podAffinityTerm:
              labelSelector:
                matchExpressions:
                - key: app
                  operator: In
                  values:
                  - network-ids
              topologyKey: kubernetes.io/hostname
---
apiVersion: v1
kind: Service
metadata:
  name: network-ids-service
  namespace: security-platform
  labels:
    app: network-ids
    component: api
  annotations:
    prometheus.io/scrape: "true"
    prometheus.io/port: "9090"
spec:
  type: ClusterIP
  ports:
  - port: 5001
    targetPort: 5001
    protocol: TCP
    name: http
  - port: 9090
    targetPort: 9090
    protocol: TCP
    name: metrics
  selector:
    app: network-ids
---
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: network-ids-hpa
  namespace: security-platform
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: network-ids
  minReplicas: 2
  maxReplicas: 10
  metrics:
  - type: Resource
    resource:
      name: cpu
      target:
        type: Utilization
        averageUtilization: 70
  - type: Resource
    resource:
      name: memory
      target:
        type: Utilization
        averageUtilization: 80
  behavior:
    scaleDown:
      stabilizationWindowSeconds: 300
      policies:
      - type: Percent
        value: 50
        periodSeconds: 60
    scaleUp:
      stabilizationWindowSeconds: 60
      policies:
      - type: Percent
        value: 100
        periodSeconds: 60
