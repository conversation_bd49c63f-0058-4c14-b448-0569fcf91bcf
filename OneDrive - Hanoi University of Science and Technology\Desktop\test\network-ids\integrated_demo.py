"""
Integrated Demo Script
Demo tích hợp Network IDS với AI Malware Detection System
"""

import sys
import time
import requests
import json
import threading
from pathlib import Path
from datetime import datetime
import argparse
import logging

# Add paths
sys.path.append(str(Path(__file__).parent))
sys.path.append(str(Path(__file__).parent.parent / "ai-malware-detection"))

import config
from src.monitoring.network_monitor import NetworkMonitor
from src.monitoring.real_time_detector import RealTimeDetector
from src.api.integration_api import MalwareDetectionIntegration, IntegratedThreatIntelligence


class IntegratedSecurityDemo:
    """Demo class tích hợp cả hai hệ thống bảo mật"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        
        # Components
        self.network_monitor = None
        self.real_time_detector = RealTimeDetector()
        self.malware_integration = MalwareDetectionIntegration()
        self.threat_intelligence = IntegratedThreatIntelligence()
        
        # Demo state
        self.is_running = False
        self.demo_thread = None
        
    def check_systems_status(self):
        """Kiểm tra trạng thái của cả hai hệ thống"""
        print("🔍 Checking systems status...")
        
        # Check Network IDS
        try:
            response = requests.get('http://localhost:5001/api/status', timeout=5)
            if response.status_code == 200:
                print("✅ Network IDS API: Online")
                network_ids_status = True
            else:
                print("❌ Network IDS API: Error")
                network_ids_status = False
        except:
            print("❌ Network IDS API: Offline")
            network_ids_status = False
        
        # Check Malware Detection
        malware_status = self.malware_integration.check_malware_api_status()
        if malware_status['status'] == 'online':
            print("✅ Malware Detection API: Online")
            malware_api_status = True
        else:
            print(f"❌ Malware Detection API: {malware_status['status']}")
            malware_api_status = False
        
        return network_ids_status, malware_api_status
    
    def start_network_monitoring(self, interface=None):
        """Bắt đầu network monitoring"""
        try:
            print(f"🚀 Starting network monitoring...")
            
            # Start network monitor
            self.network_monitor = NetworkMonitor(interface)
            self.network_monitor.start_monitoring()
            
            # Start real-time detector
            self.real_time_detector.start_detection()
            
            print("✅ Network monitoring started successfully")
            return True
            
        except Exception as e:
            print(f"❌ Failed to start network monitoring: {e}")
            return False
    
    def stop_network_monitoring(self):
        """Dừng network monitoring"""
        try:
            if self.network_monitor:
                self.network_monitor.stop_monitoring()
            
            self.real_time_detector.stop_detection()
            
            print("⏹️  Network monitoring stopped")
            
        except Exception as e:
            print(f"❌ Error stopping network monitoring: {e}")
    
    def simulate_network_threats(self):
        """Simulate network threats để test detection"""
        print("🎭 Simulating network threats...")
        
        # Simulate different types of network attacks
        threats = [
            {
                'type': 'PORT_SCAN',
                'severity': 'high',
                'source_ip': '*************',
                'message': 'Port scan detected from suspicious IP',
                'details': {'ports_scanned': 50, 'scan_type': 'TCP SYN'}
            },
            {
                'type': 'DOS_ATTACK',
                'severity': 'critical',
                'source_ip': '*********',
                'message': 'DoS attack detected - high packet rate',
                'details': {'packet_rate': 10000, 'duration': 30}
            },
            {
                'type': 'HTTP_SUSPICIOUS',
                'severity': 'medium',
                'source_ip': '***********',
                'message': 'Suspicious HTTP request detected',
                'details': {'url': '/admin/login.php', 'method': 'POST'}
            }
        ]
        
        # Create alerts through real-time detector's alert manager
        for threat in threats:
            self.real_time_detector.alert_manager.create_alert(
                alert_type=threat['type'],
                severity=threat['severity'],
                source_ip=threat['source_ip'],
                message=threat['message'],
                details=threat['details']
            )
            
            print(f"  📢 Alert created: {threat['type']} from {threat['source_ip']}")
            time.sleep(1)
    
    def test_malware_integration(self):
        """Test tích hợp với malware detection system"""
        print("🦠 Testing malware detection integration...")
        
        # Test với sample file (tạo dummy file)
        sample_file_content = b"This is a test file for malware detection"
        filename = "test_sample.exe"
        
        try:
            result = self.malware_integration.analyze_suspicious_file(
                file_data=sample_file_content,
                filename=filename,
                source_ip="*************"
            )
            
            if result['success']:
                print("✅ Malware analysis completed successfully")
                print(f"   Result: {result['result']}")
            else:
                print(f"❌ Malware analysis failed: {result['error']}")
                
        except Exception as e:
            print(f"❌ Error in malware integration test: {e}")
    
    def generate_threat_intelligence_report(self):
        """Tạo báo cáo threat intelligence tổng hợp"""
        print("📊 Generating integrated threat intelligence report...")
        
        try:
            # Get network statistics
            network_stats = {}
            if self.network_monitor:
                network_stats = self.network_monitor.get_current_stats()
            
            # Get detection statistics
            detection_stats = self.real_time_detector.get_detection_stats()
            
            # Generate unified report
            report = self.threat_intelligence.generate_unified_threat_report(
                network_stats, detection_stats
            )
            
            # Display report summary
            print("\n" + "="*60)
            print("📋 INTEGRATED THREAT INTELLIGENCE REPORT")
            print("="*60)
            
            print(f"📅 Timestamp: {report['timestamp']}")
            
            # Network Security Summary
            network_sec = report.get('network_security', {})
            print(f"\n🌐 Network Security:")
            print(f"   Status: {network_sec.get('monitoring_status', 'unknown')}")
            print(f"   Total Packets: {network_sec.get('total_packets', 0):,}")
            print(f"   Active Alerts: {network_sec.get('alerts_count', 0)}")
            
            # Detection Performance
            detection_perf = report.get('detection_performance', {})
            print(f"\n🤖 Detection Performance:")
            print(f"   Total Analyzed: {detection_perf.get('total_analyzed', 0)}")
            print(f"   Anomalies: {detection_perf.get('anomalies_detected', 0)}")
            print(f"   Intrusions: {detection_perf.get('intrusions_detected', 0)}")
            print(f"   Detection Rate: {detection_perf.get('detection_rate', 0):.1%}")
            
            # Risk Assessment
            risk_assessment = report.get('risk_assessment', {})
            print(f"\n⚠️  Risk Assessment:")
            print(f"   Risk Level: {risk_assessment.get('risk_level', 'unknown').upper()}")
            print(f"   Risk Score: {risk_assessment.get('risk_score', 0):.2f}/1.0")
            
            # Top Threats
            top_threats = network_sec.get('top_threats', [])
            if top_threats:
                print(f"\n🎯 Top Network Threats:")
                for i, threat in enumerate(top_threats[:5], 1):
                    print(f"   {i}. {threat['type']}: {threat['count']} incidents")
            
            # Recommendations
            recommendations = report.get('recommendations', [])
            if recommendations:
                print(f"\n💡 Security Recommendations:")
                for i, rec in enumerate(recommendations[:3], 1):
                    print(f"   {i}. [{rec['priority'].upper()}] {rec['message']}")
            
            print("="*60)
            
            # Save report to file
            report_file = config.LOGS_DIR / f"threat_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
            with open(report_file, 'w', encoding='utf-8') as f:
                json.dump(report, f, indent=2, ensure_ascii=False)
            
            print(f"📄 Full report saved to: {report_file}")
            
        except Exception as e:
            print(f"❌ Error generating threat intelligence report: {e}")
    
    def run_demo(self, duration=60, interface=None):
        """Chạy demo tích hợp"""
        print("🎬 Starting Integrated Security Systems Demo")
        print("="*50)
        
        try:
            # Check systems status
            network_status, malware_status = self.check_systems_status()
            
            if not network_status:
                print("⚠️  Network IDS API not available. Starting anyway...")
            
            if not malware_status:
                print("⚠️  Malware Detection API not available. Limited functionality.")
            
            # Start network monitoring
            if self.start_network_monitoring(interface):
                print(f"⏰ Running demo for {duration} seconds...")
                
                # Wait a bit for monitoring to start
                time.sleep(5)
                
                # Simulate threats
                self.simulate_network_threats()
                
                # Test malware integration if available
                if malware_status:
                    time.sleep(5)
                    self.test_malware_integration()
                
                # Let the systems run and collect data
                remaining_time = duration - 15  # Account for setup time
                print(f"📊 Collecting data for {remaining_time} seconds...")
                
                for i in range(remaining_time):
                    if i % 10 == 0:
                        # Show periodic updates
                        stats = self.real_time_detector.get_detection_stats()
                        print(f"   📈 Progress: {i}/{remaining_time}s - "
                              f"Analyzed: {stats['total_analyzed']}, "
                              f"Alerts: {len(stats['recent_alerts']['recent_alerts'])}")
                    time.sleep(1)
                
                # Generate final report
                print("\n🎯 Demo completed! Generating final report...")
                self.generate_threat_intelligence_report()
                
            else:
                print("❌ Failed to start network monitoring. Demo aborted.")
                
        except KeyboardInterrupt:
            print("\n⏹️  Demo interrupted by user")
        except Exception as e:
            print(f"❌ Demo error: {e}")
        finally:
            # Cleanup
            self.stop_network_monitoring()
            print("🏁 Demo finished!")


def main():
    """Main function"""
    parser = argparse.ArgumentParser(description='Integrated Security Systems Demo')
    parser.add_argument('--duration', '-d', type=int, default=60, 
                       help='Demo duration in seconds (default: 60)')
    parser.add_argument('--interface', '-i', type=str, 
                       help='Network interface to monitor')
    parser.add_argument('--verbose', '-v', action='store_true',
                       help='Enable verbose logging')
    
    args = parser.parse_args()
    
    # Setup logging
    log_level = logging.DEBUG if args.verbose else logging.INFO
    logging.basicConfig(
        level=log_level,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    # Create directories
    config.create_directories()
    
    # Run demo
    demo = IntegratedSecurityDemo()
    demo.run_demo(duration=args.duration, interface=args.interface)


if __name__ == "__main__":
    main()
