"""
Packet Capture Module
Module capture packets từ network interface
"""

import sys
import time
import threading
from pathlib import Path
from datetime import datetime
import logging
from collections import defaultdict
import argparse

# Add parent directory to path
sys.path.append(str(Path(__file__).parent.parent.parent))

try:
    from scapy.all import *
    from scapy.layers.inet import IP, TCP, UDP, ICMP
    from scapy.layers.http import HTTP
    from scapy.layers.dns import DNS
    import psutil
except ImportError as e:
    print(f"❌ Import error: {e}")
    print("💡 Cài đặt: pip install scapy psutil")
    sys.exit(1)

import config


class PacketCapture:
    """Class để capture và xử lý network packets"""
    
    def __init__(self, interface=None):
        """
        Khởi tạo PacketCapture
        
        Args:
            interface (str): Network interface name
        """
        self.interface = interface or config.NETWORK_CONFIG['default_interface']
        self.is_capturing = False
        self.captured_packets = []
        self.packet_count = 0
        self.start_time = None
        self.stats = defaultdict(int)
        self.logger = logging.getLogger(__name__)
        
        # Threading
        self.capture_thread = None
        self.stop_event = threading.Event()
        
        # Callbacks
        self.packet_callbacks = []
        
    def add_packet_callback(self, callback):
        """
        Thêm callback function để xử lý packets
        
        Args:
            callback (function): Function nhận packet làm parameter
        """
        self.packet_callbacks.append(callback)
    
    def get_available_interfaces(self):
        """
        Lấy danh sách network interfaces có sẵn
        
        Returns:
            list: Danh sách interfaces
        """
        try:
            interfaces = []
            
            # Sử dụng psutil để lấy interfaces
            for interface_name, addrs in psutil.net_if_addrs().items():
                interface_info = {
                    'name': interface_name,
                    'addresses': []
                }
                
                for addr in addrs:
                    if addr.family == 2:  # IPv4
                        interface_info['addresses'].append({
                            'type': 'IPv4',
                            'address': addr.address,
                            'netmask': addr.netmask
                        })
                    elif addr.family == 23:  # IPv6 on Windows
                        interface_info['addresses'].append({
                            'type': 'IPv6',
                            'address': addr.address,
                            'netmask': addr.netmask
                        })
                
                if interface_info['addresses']:
                    interfaces.append(interface_info)
            
            return interfaces
            
        except Exception as e:
            self.logger.error(f"Lỗi khi lấy interfaces: {e}")
            return []
    
    def validate_interface(self, interface_name):
        """
        Kiểm tra interface có hợp lệ không
        
        Args:
            interface_name (str): Tên interface
            
        Returns:
            bool: True nếu hợp lệ
        """
        interfaces = self.get_available_interfaces()
        interface_names = [iface['name'] for iface in interfaces]
        return interface_name in interface_names
    
    def packet_handler(self, packet):
        """
        Handler function để xử lý từng packet
        
        Args:
            packet: Scapy packet object
        """
        try:
            self.packet_count += 1
            self.captured_packets.append(packet)
            
            # Update statistics
            self._update_stats(packet)
            
            # Call registered callbacks
            for callback in self.packet_callbacks:
                try:
                    callback(packet)
                except Exception as e:
                    self.logger.error(f"Lỗi trong packet callback: {e}")
            
            # Limit memory usage
            if len(self.captured_packets) > config.NETWORK_CONFIG['max_packets']:
                self.captured_packets = self.captured_packets[-config.NETWORK_CONFIG['max_packets']//2:]
                
        except Exception as e:
            self.logger.error(f"Lỗi khi xử lý packet: {e}")
    
    def _update_stats(self, packet):
        """Cập nhật thống kê packets"""
        self.stats['total_packets'] += 1
        self.stats['total_bytes'] += len(packet)
        
        # Protocol statistics
        if packet.haslayer(IP):
            self.stats['ip_packets'] += 1
            
            if packet.haslayer(TCP):
                self.stats['tcp_packets'] += 1
            elif packet.haslayer(UDP):
                self.stats['udp_packets'] += 1
            elif packet.haslayer(ICMP):
                self.stats['icmp_packets'] += 1
        
        # Application layer protocols
        if packet.haslayer(HTTP):
            self.stats['http_packets'] += 1
        if packet.haslayer(DNS):
            self.stats['dns_packets'] += 1
    
    def start_capture(self, packet_count=0, timeout=None, filter_str=""):
        """
        Bắt đầu capture packets
        
        Args:
            packet_count (int): Số packets tối đa (0 = unlimited)
            timeout (int): Timeout in seconds
            filter_str (str): BPF filter string
        """
        if self.is_capturing:
            self.logger.warning("Capture đã đang chạy")
            return
        
        if not self.validate_interface(self.interface):
            self.logger.error(f"Interface không hợp lệ: {self.interface}")
            return
        
        self.is_capturing = True
        self.start_time = datetime.now()
        self.packet_count = 0
        self.captured_packets = []
        self.stats = defaultdict(int)
        self.stop_event.clear()
        
        self.logger.info(f"Bắt đầu capture trên interface: {self.interface}")
        if filter_str:
            self.logger.info(f"Filter: {filter_str}")
        
        try:
            # Start capture in separate thread
            self.capture_thread = threading.Thread(
                target=self._capture_worker,
                args=(packet_count, timeout, filter_str)
            )
            self.capture_thread.daemon = True
            self.capture_thread.start()
            
        except Exception as e:
            self.logger.error(f"Lỗi khi bắt đầu capture: {e}")
            self.is_capturing = False
    
    def _capture_worker(self, packet_count, timeout, filter_str):
        """Worker thread cho packet capture"""
        try:
            sniff(
                iface=self.interface,
                prn=self.packet_handler,
                count=packet_count,
                timeout=timeout,
                filter=filter_str,
                stop_filter=lambda x: self.stop_event.is_set()
            )
        except Exception as e:
            self.logger.error(f"Lỗi trong capture worker: {e}")
        finally:
            self.is_capturing = False
            self.logger.info("Capture đã dừng")
    
    def stop_capture(self):
        """Dừng capture packets"""
        if not self.is_capturing:
            self.logger.warning("Capture không đang chạy")
            return
        
        self.logger.info("Đang dừng capture...")
        self.stop_event.set()
        
        # Wait for capture thread to finish
        if self.capture_thread and self.capture_thread.is_alive():
            self.capture_thread.join(timeout=5)
        
        self.is_capturing = False
        
        # Log final statistics
        duration = (datetime.now() - self.start_time).total_seconds()
        self.logger.info(f"Capture hoàn thành:")
        self.logger.info(f"  - Thời gian: {duration:.2f} giây")
        self.logger.info(f"  - Packets: {self.packet_count}")
        self.logger.info(f"  - Tốc độ: {self.packet_count/duration:.2f} packets/sec")
    
    def get_statistics(self):
        """
        Lấy thống kê capture
        
        Returns:
            dict: Thống kê
        """
        if self.start_time:
            duration = (datetime.now() - self.start_time).total_seconds()
        else:
            duration = 0
        
        stats = dict(self.stats)
        stats.update({
            'duration': duration,
            'packets_per_second': self.packet_count / duration if duration > 0 else 0,
            'bytes_per_second': stats.get('total_bytes', 0) / duration if duration > 0 else 0,
            'is_capturing': self.is_capturing,
            'interface': self.interface
        })
        
        return stats
    
    def save_pcap(self, filename):
        """
        Lưu captured packets vào PCAP file
        
        Args:
            filename (str): Tên file PCAP
        """
        if not self.captured_packets:
            self.logger.warning("Không có packets để lưu")
            return
        
        try:
            pcap_path = config.PCAP_DIR / filename
            wrpcap(str(pcap_path), self.captured_packets)
            self.logger.info(f"Đã lưu {len(self.captured_packets)} packets vào {pcap_path}")
        except Exception as e:
            self.logger.error(f"Lỗi khi lưu PCAP: {e}")
    
    def load_pcap(self, filename):
        """
        Load packets từ PCAP file
        
        Args:
            filename (str): Tên file PCAP
        """
        try:
            pcap_path = config.PCAP_DIR / filename
            if not pcap_path.exists():
                self.logger.error(f"File không tồn tại: {pcap_path}")
                return
            
            self.captured_packets = rdpcap(str(pcap_path))
            self.packet_count = len(self.captured_packets)
            
            # Update statistics
            self.stats = defaultdict(int)
            for packet in self.captured_packets:
                self._update_stats(packet)
            
            self.logger.info(f"Đã load {self.packet_count} packets từ {pcap_path}")
            
        except Exception as e:
            self.logger.error(f"Lỗi khi load PCAP: {e}")
    
    def get_packets(self, limit=None):
        """
        Lấy captured packets
        
        Args:
            limit (int): Số packets tối đa
            
        Returns:
            list: Danh sách packets
        """
        if limit:
            return self.captured_packets[-limit:]
        return self.captured_packets


def main():
    """Main function để test packet capture"""
    parser = argparse.ArgumentParser(description='Network Packet Capture Tool')
    parser.add_argument('--interface', '-i', type=str, help='Network interface')
    parser.add_argument('--count', '-c', type=int, default=100, help='Number of packets to capture')
    parser.add_argument('--timeout', '-t', type=int, default=30, help='Capture timeout (seconds)')
    parser.add_argument('--filter', '-f', type=str, default='', help='BPF filter')
    parser.add_argument('--output', '-o', type=str, help='Output PCAP file')
    parser.add_argument('--list-interfaces', action='store_true', help='List available interfaces')
    
    args = parser.parse_args()
    
    # Setup logging
    logging.basicConfig(level=logging.INFO, 
                       format='%(asctime)s - %(levelname)s - %(message)s')
    
    # Create directories
    config.create_directories()
    
    # Initialize packet capture
    capture = PacketCapture(args.interface)
    
    # List interfaces
    if args.list_interfaces:
        print("🌐 Available network interfaces:")
        interfaces = capture.get_available_interfaces()
        for i, interface in enumerate(interfaces, 1):
            print(f"  {i}. {interface['name']}")
            for addr in interface['addresses']:
                print(f"     {addr['type']}: {addr['address']}")
        return
    
    # Add callback to print packet info
    def packet_callback(packet):
        if packet.haslayer(IP):
            src = packet[IP].src
            dst = packet[IP].dst
            proto = packet[IP].proto
            print(f"📦 {src} -> {dst} (Protocol: {proto})")
    
    capture.add_packet_callback(packet_callback)
    
    try:
        # Start capture
        print(f"🚀 Bắt đầu capture {args.count} packets...")
        capture.start_capture(
            packet_count=args.count,
            timeout=args.timeout,
            filter_str=args.filter
        )
        
        # Wait for capture to complete
        while capture.is_capturing:
            time.sleep(1)
            stats = capture.get_statistics()
            print(f"\r📊 Packets: {stats['total_packets']}, "
                  f"Rate: {stats['packets_per_second']:.1f} pps", end='')
        
        print()  # New line
        
        # Show final statistics
        stats = capture.get_statistics()
        print(f"\n📈 Final Statistics:")
        print(f"  Total packets: {stats['total_packets']}")
        print(f"  Total bytes: {stats['total_bytes']}")
        print(f"  Duration: {stats['duration']:.2f} seconds")
        print(f"  TCP packets: {stats['tcp_packets']}")
        print(f"  UDP packets: {stats['udp_packets']}")
        print(f"  ICMP packets: {stats['icmp_packets']}")
        
        # Save to PCAP if requested
        if args.output:
            capture.save_pcap(args.output)
            
    except KeyboardInterrupt:
        print(f"\n⏹️  Capture interrupted by user")
        capture.stop_capture()
    except Exception as e:
        print(f"💥 Error: {e}")


if __name__ == "__main__":
    main()
