# AI Malware Detection System

Hệ thống phát hiện phần mềm độc hại sử dụng Machine Learning và Deep Learning.

## Tính năng chính

- 🔍 **Phân tích file PE**: Trích xuất đặc trưng từ Windows executables
- 🤖 **Machine Learning**: Sử dụng Random Forest, SVM để phân loại
- 🌐 **Web Interface**: Giao diện web thân thiện để upload và kiểm tra file
- 📊 **Báo cáo chi tiết**: Hiển thị kết quả phân tích và độ tin cậy
- 💾 **Database**: Lư<PERSON> trữ lịch sử phân tích và kết quả

## Cấu trúc dự án

```
ai-malware-detection/
├── src/
│   ├── feature_extraction/     # Module trích xuất đặc trưng
│   ├── models/                # ML models và training
│   ├── api/                   # Flask/FastAPI backend
│   └── web/                   # Frontend web interface
├── data/
│   ├── samples/               # Sample malware/benign files
│   ├── datasets/              # Training datasets
│   └── models/                # Trained model files
├── tests/                     # Unit tests
├── docs/                      # Documentation
└── requirements.txt           # Python dependencies
```

## Cài đặt

1. Clone repository
2. Cài đặt dependencies: `pip install -r requirements.txt`
3. Chạy training: `python src/models/train.py`
4. Khởi động web server: `python src/api/app.py`

## Sử dụng

1. Truy cập http://localhost:5000
2. Upload file cần kiểm tra
3. Xem kết quả phân tích

## Công nghệ sử dụng

- **Python 3.8+**
- **scikit-learn**: Machine Learning
- **Flask**: Web framework
- **pefile**: PE file analysis
- **SQLite**: Database
- **Bootstrap**: Frontend UI

## Tác giả

Dự án AI Malware Detection - Phát hiện phần mềm độc hại thông minh
