# 🤖 AI Malware Detection System

Hệ thống phát hiện phần mềm độc hại thông minh sử dụng Machine Learning và Deep Learning.

![Python](https://img.shields.io/badge/Python-3.8+-blue.svg)
![Flask](https://img.shields.io/badge/Flask-2.3+-green.svg)
![scikit-learn](https://img.shields.io/badge/scikit--learn-1.3+-orange.svg)
![License](https://img.shields.io/badge/License-MIT-yellow.svg)

## ✨ Tính năng chính

- 🔍 **Phân tích file PE**: Trích xuất đặc trưng từ Windows executables (entropy, imports, sections, strings)
- 🤖 **Machine Learning**: Sử dụng Random Forest, SVM để phân loại với độ chính xác cao
- 🌐 **Web Interface**: Giao diện web hiện đại, responsive với Bootstrap
- 📊 **<PERSON><PERSON><PERSON> cáo chi tiết**: Hi<PERSON>n thị kết quả phân tích, confidence score và đặc trưng kỹ thuật
- 💾 **Database**: SQLite database lưu trữ lịch sử phân tích và kết quả
- 🚀 **API RESTful**: API endpoints để tích hợp với hệ thống khác
- 📈 **Logging & Monitoring**: Hệ thống logging chi tiết và monitoring
- 🧪 **Unit Tests**: Test coverage cao với pytest

## 🏗️ Cấu trúc dự án

```
ai-malware-detection/
├── src/
│   ├── feature_extraction/     # Module trích xuất đặc trưng
│   │   ├── pe_analyzer.py     # Phân tích file PE
│   │   └── batch_processor.py # Xử lý batch files
│   ├── models/                # ML models và training
│   │   ├── classifier.py      # Malware classifier
│   │   └── train.py          # Training script
│   ├── api/                   # Flask backend
│   │   ├── app.py            # Main Flask app
│   │   ├── database.py       # Database models
│   │   └── logging_config.py # Logging configuration
│   └── web/                   # Frontend web interface
│       ├── templates/        # HTML templates
│       └── static/          # CSS, JS, images
├── data/
│   ├── samples/              # Sample malware/benign files
│   ├── datasets/             # Training datasets
│   └── models/               # Trained model files
├── tests/                    # Unit tests
├── docs/                     # Documentation
├── config.py                 # Configuration file
├── demo.py                   # Demo script
├── run_tests.py             # Test runner
└── requirements.txt          # Python dependencies
```

## 🚀 Quick Start

### 1. Cài đặt

```bash
# Clone repository
git clone <repository-url>
cd ai-malware-detection

# Tạo virtual environment (khuyến nghị)
python -m venv venv
venv\Scripts\activate  # Windows
# source venv/bin/activate  # Linux/Mac

# Cài đặt dependencies
pip install -r requirements.txt

# Tạo thư mục cần thiết
python config.py
```

### 2. Chuẩn bị dữ liệu

```bash
# Tạo sample dataset (benign files từ Windows system)
python src/models/train.py --create-sample

# Thêm malware samples vào data/samples/malware/ (cần thiết để train)
# Sau đó tạo dataset hoàn chỉnh
python src/models/train.py --malware-dir data/samples/malware --benign-dir data/samples/benign
```

### 3. Train model

```bash
# Train Random Forest model
python src/models/train.py --dataset data/datasets/malware_dataset.csv --model-type random_forest

# Hoặc train SVM model
python src/models/train.py --dataset data/datasets/malware_dataset.csv --model-type svm
```

### 4. Chạy hệ thống

```bash
# Khởi động web server
python src/api/app.py

# Truy cập web interface
# Mở browser: http://localhost:5000
```

### 5. Demo và Testing

```bash
# Chạy demo
python demo.py

# Chạy unit tests
python run_tests.py

# Chạy test cụ thể
python run_tests.py --test pe_analyzer
```

## 📖 Documentation

- **[Installation Guide](docs/INSTALLATION.md)**: Hướng dẫn cài đặt chi tiết
- **[User Guide](docs/USER_GUIDE.md)**: Hướng dẫn sử dụng đầy đủ
- **[API Documentation](docs/API.md)**: Tài liệu API endpoints
- **[Developer Guide](docs/DEVELOPER.md)**: Hướng dẫn phát triển

## 🔧 Sử dụng

### Web Interface

1. **Truy cập**: http://localhost:5000
2. **Upload file**: Chọn file PE (.exe, .dll, .sys, etc.)
3. **Phân tích**: Click "Phân tích File"
4. **Xem kết quả**: Kết quả hiển thị với confidence score và đặc trưng

### API Usage

```python
import requests

# Upload và phân tích file
with open('sample.exe', 'rb') as f:
    response = requests.post(
        'http://localhost:5000/api/upload',
        files={'file': f}
    )
    result = response.json()
    print(f"Prediction: {result['prediction']}")
    print(f"Confidence: {result['confidence']}")
```

### Command Line

```python
from src.models.classifier import MalwareClassifier

# Load trained model
classifier = MalwareClassifier()
classifier.load_model('data/models/random_forest_model.joblib',
                     'data/models/random_forest_scaler.joblib')

# Predict single file
result = classifier.predict('sample.exe')
print(result)
```

## 🧪 Testing

```bash
# Chạy tất cả tests
python run_tests.py

# Chạy test với verbosity
python run_tests.py --verbosity 2

# Chạy test cụ thể
python run_tests.py --test api
python run_tests.py --test pe_analyzer

# Filter tests
python run_tests.py --pattern "test_analyze"
```

## 🚀 Performance

- **Phân tích file**: < 5 giây cho file PE thông thường
- **Batch processing**: 100+ files/phút (tùy thuộc hardware)
- **Web response**: < 1 giây cho API calls
- **Memory usage**: < 500MB RAM cho model Random Forest
- **Accuracy**: 95%+ trên test dataset (tùy thuộc chất lượng training data)

## 🛠️ Công nghệ sử dụng

### Backend
- **Python 3.8+**: Ngôn ngữ chính
- **Flask 2.3+**: Web framework
- **scikit-learn 1.3+**: Machine Learning
- **SQLAlchemy**: Database ORM
- **pefile**: PE file analysis
- **SQLite**: Database

### Frontend
- **HTML5/CSS3**: Markup và styling
- **Bootstrap 5**: UI framework
- **JavaScript ES6**: Client-side logic
- **Font Awesome**: Icons

### ML/AI
- **Random Forest**: Primary classifier
- **SVM**: Alternative classifier
- **Feature Engineering**: 20+ PE features
- **Cross Validation**: Model validation

## 📊 Features Extracted

### File Properties
- File size, hashes (MD5, SHA1, SHA256)
- PE type (EXE, DLL, Driver)
- Compilation timestamp

### PE Structure
- Number of sections
- Section characteristics và entropy
- Entry point address
- Image base address

### Imports Analysis
- Number of imported DLLs
- Number of imported functions
- Suspicious API calls detection

### Content Analysis
- File entropy calculation
- String analysis (ASCII/Unicode)
- Suspicious strings detection

## 🔒 Security Notes

⚠️ **QUAN TRỌNG**:
- Chỉ phân tích malware trong môi trường an toàn (VM, sandbox)
- Không chạy malware samples trên hệ thống chính
- Backup dữ liệu quan trọng trước khi phân tích
- Sử dụng antivirus song song để bảo vệ

## 🤝 Contributing

1. Fork repository
2. Tạo feature branch: `git checkout -b feature/amazing-feature`
3. Commit changes: `git commit -m 'Add amazing feature'`
4. Push to branch: `git push origin feature/amazing-feature`
5. Open Pull Request

## 📝 License

Dự án này được phân phối dưới MIT License. Xem `LICENSE` file để biết thêm chi tiết.

## 👥 Authors

- **AI Malware Detection Team** - *Initial work*

## 🙏 Acknowledgments

- **pefile library**: PE file parsing
- **scikit-learn**: Machine Learning algorithms
- **Flask**: Web framework
- **Bootstrap**: UI components
- **Font Awesome**: Icons

## 📞 Support

- **Issues**: Tạo issue trên GitHub repository
- **Documentation**: Xem thư mục `docs/`
- **Email**: [<EMAIL>]

---

⭐ **Nếu project này hữu ích, hãy cho chúng tôi một star!** ⭐
