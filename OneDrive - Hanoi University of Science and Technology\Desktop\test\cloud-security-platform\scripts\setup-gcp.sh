#!/bin/bash

# GCP Setup Script for Cloud Security Platform
# Script để setup GCP project và enable các services cần thiết

set -e

# Configuration
PROJECT_ID=${PROJECT_ID:-"security-platform-prod"}
REGION=${REGION:-"asia-southeast1"}
ZONE=${ZONE:-"asia-southeast1-a"}
BILLING_ACCOUNT_ID=${BILLING_ACCOUNT_ID:-""}

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check prerequisites
check_prerequisites() {
    log_info "Checking prerequisites..."
    
    # Check if gcloud is installed
    if ! command -v gcloud &> /dev/null; then
        log_error "gcloud CLI is not installed"
        log_error "Install from: https://cloud.google.com/sdk/docs/install"
        exit 1
    fi
    
    # Check if authenticated
    if ! gcloud auth list --filter=status:ACTIVE --format="value(account)" | head -n1 &> /dev/null; then
        log_error "gcloud is not authenticated. Run 'gcloud auth login'"
        exit 1
    fi
    
    # Check if terraform is installed
    if ! command -v terraform &> /dev/null; then
        log_warning "Terraform is not installed. Install from: https://www.terraform.io/downloads"
    fi
    
    # Check if kubectl is installed
    if ! command -v kubectl &> /dev/null; then
        log_warning "kubectl is not installed. Install from: https://kubernetes.io/docs/tasks/tools/"
    fi
    
    log_success "Prerequisites check completed"
}

# Create or select project
setup_project() {
    log_info "Setting up GCP project: ${PROJECT_ID}"
    
    # Check if project exists
    if gcloud projects describe "${PROJECT_ID}" &> /dev/null; then
        log_info "Project ${PROJECT_ID} already exists"
    else
        log_info "Creating project ${PROJECT_ID}..."
        
        if [ -z "$BILLING_ACCOUNT_ID" ]; then
            log_error "BILLING_ACCOUNT_ID is required to create a new project"
            log_error "Get your billing account ID: gcloud billing accounts list"
            exit 1
        fi
        
        gcloud projects create "${PROJECT_ID}" --name="Security Platform"
        
        # Link billing account
        gcloud billing projects link "${PROJECT_ID}" --billing-account="${BILLING_ACCOUNT_ID}"
        
        log_success "Project ${PROJECT_ID} created and linked to billing account"
    fi
    
    # Set current project
    gcloud config set project "${PROJECT_ID}"
    log_success "Current project set to ${PROJECT_ID}"
}

# Enable required APIs
enable_apis() {
    log_info "Enabling required APIs..."
    
    apis=(
        "compute.googleapis.com"
        "container.googleapis.com"
        "sqladmin.googleapis.com"
        "storage.googleapis.com"
        "monitoring.googleapis.com"
        "logging.googleapis.com"
        "cloudbuild.googleapis.com"
        "containerregistry.googleapis.com"
        "secretmanager.googleapis.com"
        "iam.googleapis.com"
        "cloudresourcemanager.googleapis.com"
        "servicenetworking.googleapis.com"
        "artifactregistry.googleapis.com"
    )
    
    for api in "${apis[@]}"; do
        log_info "Enabling ${api}..."
        gcloud services enable "${api}"
    done
    
    log_success "All required APIs enabled"
}

# Create service accounts
create_service_accounts() {
    log_info "Creating service accounts..."
    
    # GKE service account
    if gcloud iam service-accounts describe "security-platform-gke-sa@${PROJECT_ID}.iam.gserviceaccount.com" &> /dev/null; then
        log_info "GKE service account already exists"
    else
        gcloud iam service-accounts create security-platform-gke-sa \
            --display-name="Security Platform GKE Service Account" \
            --description="Service account for GKE nodes"
        log_success "GKE service account created"
    fi
    
    # Cloud Build service account
    if gcloud iam service-accounts describe "cloudbuild-sa@${PROJECT_ID}.iam.gserviceaccount.com" &> /dev/null; then
        log_info "Cloud Build service account already exists"
    else
        gcloud iam service-accounts create cloudbuild-sa \
            --display-name="Cloud Build Service Account" \
            --description="Service account for Cloud Build"
        log_success "Cloud Build service account created"
    fi
    
    # Storage service account
    if gcloud iam service-accounts describe "security-platform-storage-sa@${PROJECT_ID}.iam.gserviceaccount.com" &> /dev/null; then
        log_info "Storage service account already exists"
    else
        gcloud iam service-accounts create security-platform-storage-sa \
            --display-name="Security Platform Storage Service Account" \
            --description="Service account for Cloud Storage access"
        log_success "Storage service account created"
    fi
    
    # Cloud SQL service account
    if gcloud iam service-accounts describe "security-platform-cloudsql-sa@${PROJECT_ID}.iam.gserviceaccount.com" &> /dev/null; then
        log_info "Cloud SQL service account already exists"
    else
        gcloud iam service-accounts create security-platform-cloudsql-sa \
            --display-name="Security Platform Cloud SQL Service Account" \
            --description="Service account for Cloud SQL proxy"
        log_success "Cloud SQL service account created"
    fi
}

# Assign IAM roles
assign_iam_roles() {
    log_info "Assigning IAM roles..."
    
    # GKE service account roles
    gke_roles=(
        "roles/logging.logWriter"
        "roles/monitoring.metricWriter"
        "roles/monitoring.viewer"
        "roles/stackdriver.resourceMetadata.writer"
        "roles/storage.objectViewer"
        "roles/artifactregistry.reader"
    )
    
    for role in "${gke_roles[@]}"; do
        gcloud projects add-iam-policy-binding "${PROJECT_ID}" \
            --member="serviceAccount:security-platform-gke-sa@${PROJECT_ID}.iam.gserviceaccount.com" \
            --role="${role}"
    done
    
    # Cloud Build service account roles
    cloudbuild_roles=(
        "roles/cloudbuild.builds.builder"
        "roles/container.developer"
        "roles/storage.admin"
        "roles/container.clusterAdmin"
        "roles/iam.serviceAccountUser"
    )
    
    for role in "${cloudbuild_roles[@]}"; do
        gcloud projects add-iam-policy-binding "${PROJECT_ID}" \
            --member="serviceAccount:cloudbuild-sa@${PROJECT_ID}.iam.gserviceaccount.com" \
            --role="${role}"
    done
    
    # Cloud SQL service account role
    gcloud projects add-iam-policy-binding "${PROJECT_ID}" \
        --member="serviceAccount:security-platform-cloudsql-sa@${PROJECT_ID}.iam.gserviceaccount.com" \
        --role="roles/cloudsql.client"
    
    log_success "IAM roles assigned"
}

# Create Cloud Storage bucket for Terraform state
create_terraform_state_bucket() {
    log_info "Creating Terraform state bucket..."
    
    BUCKET_NAME="${PROJECT_ID}-terraform-state"
    
    if gsutil ls -b "gs://${BUCKET_NAME}" &> /dev/null; then
        log_info "Terraform state bucket already exists"
    else
        gsutil mb -p "${PROJECT_ID}" -c STANDARD -l "${REGION}" "gs://${BUCKET_NAME}"
        
        # Enable versioning
        gsutil versioning set on "gs://${BUCKET_NAME}"
        
        # Set lifecycle policy
        cat > lifecycle.json << EOF
{
  "lifecycle": {
    "rule": [
      {
        "action": {"type": "Delete"},
        "condition": {"age": 365}
      }
    ]
  }
}
EOF
        
        gsutil lifecycle set lifecycle.json "gs://${BUCKET_NAME}"
        rm lifecycle.json
        
        log_success "Terraform state bucket created: gs://${BUCKET_NAME}"
    fi
}

# Setup Container Registry
setup_container_registry() {
    log_info "Setting up Container Registry..."
    
    # Configure Docker to use gcloud as credential helper
    gcloud auth configure-docker --quiet
    
    log_success "Container Registry configured"
}

# Create initial secrets
create_initial_secrets() {
    log_info "Creating initial secrets in Secret Manager..."
    
    # Database password secret
    if gcloud secrets describe security-platform-db-password &> /dev/null; then
        log_info "Database password secret already exists"
    else
        echo "$(openssl rand -base64 32)" | gcloud secrets create security-platform-db-password --data-file=-
        log_success "Database password secret created"
    fi
    
    # App secrets
    if gcloud secrets describe security-platform-app-secrets &> /dev/null; then
        log_info "App secrets already exist"
    else
        cat > app-secrets.json << EOF
{
  "malware_secret_key": "$(openssl rand -base64 32)",
  "ids_secret_key": "$(openssl rand -base64 32)",
  "dashboard_secret_key": "$(openssl rand -base64 32)",
  "api_key": "$(openssl rand -base64 32)",
  "jwt_secret": "$(openssl rand -base64 32)"
}
EOF
        
        gcloud secrets create security-platform-app-secrets --data-file=app-secrets.json
        rm app-secrets.json
        log_success "App secrets created"
    fi
}

# Setup Cloud Build triggers
setup_cloud_build_triggers() {
    log_info "Setting up Cloud Build triggers..."
    
    # Note: This requires the repository to be connected to Cloud Build
    log_warning "Cloud Build triggers need to be set up manually in the console"
    log_warning "Or use the trigger configuration files in ci-cd/triggers/"
    
    echo "To set up triggers:"
    echo "1. Go to Cloud Build > Triggers in the console"
    echo "2. Connect your GitHub repository"
    echo "3. Import trigger configurations from ci-cd/triggers/"
}

# Display setup summary
display_summary() {
    log_success "GCP setup completed successfully!"
    
    echo ""
    echo "=== Setup Summary ==="
    echo "Project ID: ${PROJECT_ID}"
    echo "Region: ${REGION}"
    echo "Zone: ${ZONE}"
    echo ""
    echo "=== Created Resources ==="
    echo "• Service Accounts:"
    echo "  - security-platform-gke-sa@${PROJECT_ID}.iam.gserviceaccount.com"
    echo "  - cloudbuild-sa@${PROJECT_ID}.iam.gserviceaccount.com"
    echo "  - security-platform-storage-sa@${PROJECT_ID}.iam.gserviceaccount.com"
    echo "  - security-platform-cloudsql-sa@${PROJECT_ID}.iam.gserviceaccount.com"
    echo ""
    echo "• Storage:"
    echo "  - Terraform state bucket: gs://${PROJECT_ID}-terraform-state"
    echo ""
    echo "• Secrets:"
    echo "  - security-platform-db-password"
    echo "  - security-platform-app-secrets"
    echo ""
    echo "=== Next Steps ==="
    echo "1. Review and update terraform/variables.tf with your values"
    echo "2. Initialize Terraform: cd terraform && terraform init"
    echo "3. Plan infrastructure: terraform plan"
    echo "4. Apply infrastructure: terraform apply"
    echo "5. Build and push Docker images: ./scripts/build-images.sh"
    echo "6. Deploy applications: ./scripts/deploy.sh production"
    echo "7. Set up monitoring and alerting"
    echo "8. Configure DNS and SSL certificates"
    echo ""
    echo "=== Important Notes ==="
    echo "• Update project-config.yaml with your project settings"
    echo "• Review security settings before production deployment"
    echo "• Set up backup and disaster recovery procedures"
    echo "• Configure monitoring and alerting"
}

# Main function
main() {
    log_info "Starting GCP setup for Cloud Security Platform..."
    
    check_prerequisites
    setup_project
    enable_apis
    create_service_accounts
    assign_iam_roles
    create_terraform_state_bucket
    setup_container_registry
    create_initial_secrets
    setup_cloud_build_triggers
    display_summary
}

# Handle script arguments
case "${1:-}" in
    "--help"|"-h")
        echo "Usage: $0"
        echo ""
        echo "Environment variables:"
        echo "  PROJECT_ID           - GCP Project ID (default: security-platform-prod)"
        echo "  REGION              - GCP Region (default: asia-southeast1)"
        echo "  ZONE                - GCP Zone (default: asia-southeast1-a)"
        echo "  BILLING_ACCOUNT_ID  - Billing Account ID (required for new projects)"
        echo ""
        echo "Example:"
        echo "  PROJECT_ID=my-security-project BILLING_ACCOUNT_ID=123456-789ABC-DEF012 $0"
        exit 0
        ;;
    "")
        main
        ;;
    *)
        log_error "Unknown argument: $1"
        echo "Use $0 --help for usage information"
        exit 1
        ;;
esac
