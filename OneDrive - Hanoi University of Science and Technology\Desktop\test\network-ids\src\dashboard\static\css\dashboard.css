/* Network IDS Dashboard CSS */

/* Global Styles */
body {
    background-color: #f8f9fa;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

/* Navigation */
.navbar-brand {
    font-weight: bold;
    font-size: 1.2rem;
}

.navbar-brand i {
    margin-right: 8px;
    color: #28a745;
}

#status-indicator {
    font-size: 0.8rem;
    margin-right: 5px;
}

/* Cards */
.card {
    border: none;
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    transition: transform 0.2s ease-in-out;
}

.card:hover {
    transform: translateY(-2px);
}

.card-header {
    background-color: #fff;
    border-bottom: 1px solid #e9ecef;
    font-weight: 600;
    border-radius: 10px 10px 0 0 !important;
}

.card-header h5 {
    margin: 0;
    color: #495057;
}

.card-header i {
    margin-right: 8px;
    color: #6c757d;
}

/* Statistics Cards */
.card.bg-primary {
    background: linear-gradient(135deg, #007bff, #0056b3) !important;
}

.card.bg-warning {
    background: linear-gradient(135deg, #ffc107, #e0a800) !important;
}

.card.bg-danger {
    background: linear-gradient(135deg, #dc3545, #c82333) !important;
}

.card.bg-info {
    background: linear-gradient(135deg, #17a2b8, #138496) !important;
}

.card.bg-success {
    background: linear-gradient(135deg, #28a745, #1e7e34) !important;
}

/* Buttons */
.btn {
    border-radius: 6px;
    font-weight: 500;
    transition: all 0.2s ease-in-out;
}

.btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

.btn-success {
    background: linear-gradient(135deg, #28a745, #20c997);
    border: none;
}

.btn-danger {
    background: linear-gradient(135deg, #dc3545, #e83e8c);
    border: none;
}

.btn-primary {
    background: linear-gradient(135deg, #007bff, #6610f2);
    border: none;
}

/* Tables */
.table {
    margin-bottom: 0;
}

.table th {
    border-top: none;
    font-weight: 600;
    color: #495057;
    background-color: #f8f9fa;
}

.table-striped tbody tr:nth-of-type(odd) {
    background-color: rgba(0, 0, 0, 0.02);
}

/* Alert Severity Badges */
.severity-critical {
    background-color: #dc3545;
    color: white;
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 0.75rem;
    font-weight: 600;
}

.severity-high {
    background-color: #fd7e14;
    color: white;
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 0.75rem;
    font-weight: 600;
}

.severity-medium {
    background-color: #ffc107;
    color: #212529;
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 0.75rem;
    font-weight: 600;
}

.severity-low {
    background-color: #28a745;
    color: white;
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 0.75rem;
    font-weight: 600;
}

/* Top Talkers */
.top-talker-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 0;
    border-bottom: 1px solid #e9ecef;
}

.top-talker-item:last-child {
    border-bottom: none;
}

.top-talker-ip {
    font-family: 'Courier New', monospace;
    font-weight: 600;
    color: #495057;
}

.top-talker-count {
    background-color: #e9ecef;
    color: #495057;
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 0.8rem;
    font-weight: 600;
}

/* Charts */
canvas {
    max-height: 300px !important;
}

/* Model Status Indicators */
.text-success {
    color: #28a745 !important;
}

.text-danger {
    color: #dc3545 !important;
}

.text-warning {
    color: #ffc107 !important;
}

.text-secondary {
    color: #6c757d !important;
}

/* Progress Bars */
.progress {
    height: 8px;
    border-radius: 4px;
    background-color: #e9ecef;
}

.progress-bar {
    border-radius: 4px;
}

/* Modal */
.modal-content {
    border: none;
    border-radius: 10px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
}

.modal-header {
    border-bottom: 1px solid #e9ecef;
    border-radius: 10px 10px 0 0;
}

.modal-footer {
    border-top: 1px solid #e9ecef;
    border-radius: 0 0 10px 10px;
}

/* Form Controls */
.form-control, .form-select {
    border-radius: 6px;
    border: 1px solid #ced4da;
    transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}

.form-control:focus, .form-select:focus {
    border-color: #80bdff;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

/* Responsive Design */
@media (max-width: 768px) {
    .card-body h4 {
        font-size: 1.5rem;
    }
    
    .btn {
        font-size: 0.875rem;
        padding: 0.375rem 0.75rem;
    }
    
    .table-responsive {
        font-size: 0.875rem;
    }
}

/* Loading Animation */
.loading {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    border-top-color: #fff;
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* Alert Animation */
.alert-flash {
    animation: flash 0.5s ease-in-out;
}

@keyframes flash {
    0% { background-color: transparent; }
    50% { background-color: rgba(255, 193, 7, 0.3); }
    100% { background-color: transparent; }
}

/* Status Indicators */
.status-online {
    color: #28a745 !important;
}

.status-offline {
    color: #dc3545 !important;
}

.status-warning {
    color: #ffc107 !important;
}

/* Scrollbar Styling */
.table-responsive::-webkit-scrollbar {
    height: 8px;
}

.table-responsive::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4px;
}

.table-responsive::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 4px;
}

.table-responsive::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

/* Utility Classes */
.text-monospace {
    font-family: 'Courier New', monospace !important;
}

.cursor-pointer {
    cursor: pointer;
}

.border-left-primary {
    border-left: 4px solid #007bff !important;
}

.border-left-warning {
    border-left: 4px solid #ffc107 !important;
}

.border-left-danger {
    border-left: 4px solid #dc3545 !important;
}

.border-left-success {
    border-left: 4px solid #28a745 !important;
}
