// Main JavaScript for AI Malware Detection System

class MalwareDetectionApp {
    constructor() {
        this.apiBaseUrl = '';
        this.init();
    }

    init() {
        this.setupEventListeners();
        this.checkApiStatus();
        this.setupFileValidation();
    }

    setupEventListeners() {
        // Upload form submission
        document.getElementById('uploadForm').addEventListener('submit', (e) => {
            e.preventDefault();
            this.handleFileUpload();
        });

        // File input change
        document.getElementById('fileInput').addEventListener('change', (e) => {
            this.validateFile(e.target.files[0]);
        });

        // Smooth scrolling for navigation links
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });
    }

    setupFileValidation() {
        const fileInput = document.getElementById('fileInput');
        const allowedExtensions = ['.exe', '.dll', '.sys', '.scr', '.com', '.pif'];
        const maxSize = 50 * 1024 * 1024; // 50MB

        fileInput.addEventListener('change', (e) => {
            const file = e.target.files[0];
            if (file) {
                this.validateFile(file);
            }
        });
    }

    validateFile(file) {
        const allowedExtensions = ['.exe', '.dll', '.sys', '.scr', '.com', '.pif'];
        const maxSize = 50 * 1024 * 1024; // 50MB
        
        if (!file) return false;

        // Check file extension
        const fileExtension = '.' + file.name.split('.').pop().toLowerCase();
        if (!allowedExtensions.includes(fileExtension)) {
            this.showAlert('danger', `File extension không được hỗ trợ. Chỉ chấp nhận: ${allowedExtensions.join(', ')}`);
            return false;
        }

        // Check file size
        if (file.size > maxSize) {
            this.showAlert('danger', `File quá lớn. Kích thước tối đa: ${maxSize / (1024 * 1024)}MB`);
            return false;
        }

        return true;
    }

    async checkApiStatus() {
        const statusIndicator = document.getElementById('status-indicator');
        
        try {
            const response = await fetch('/api/status');
            const data = await response.json();
            
            if (data.status === 'online' && data.model_loaded) {
                statusIndicator.innerHTML = '<i class="fas fa-circle text-success"></i> Sẵn sàng';
                statusIndicator.className = 'nav-link status-online';
            } else if (data.status === 'online' && !data.model_loaded) {
                statusIndicator.innerHTML = '<i class="fas fa-circle text-warning"></i> Model chưa load';
                statusIndicator.className = 'nav-link status-loading';
            } else {
                throw new Error('API offline');
            }
        } catch (error) {
            statusIndicator.innerHTML = '<i class="fas fa-circle text-danger"></i> Offline';
            statusIndicator.className = 'nav-link status-offline';
        }
    }

    async handleFileUpload() {
        const fileInput = document.getElementById('fileInput');
        const file = fileInput.files[0];
        
        if (!file) {
            this.showAlert('warning', 'Vui lòng chọn file để phân tích');
            return;
        }

        if (!this.validateFile(file)) {
            return;
        }

        // Show progress
        this.showProgress();
        this.disableForm(true);

        const formData = new FormData();
        formData.append('file', file);

        try {
            const response = await fetch('/api/upload', {
                method: 'POST',
                body: formData
            });

            const data = await response.json();

            if (data.success) {
                this.showResults(data);
                this.showAlert('success', 'Phân tích hoàn thành thành công!');
            } else {
                this.showAlert('danger', data.error || 'Có lỗi xảy ra khi phân tích file');
            }
        } catch (error) {
            console.error('Upload error:', error);
            this.showAlert('danger', 'Lỗi kết nối đến server. Vui lòng thử lại.');
        } finally {
            this.hideProgress();
            this.disableForm(false);
        }
    }

    showProgress() {
        const progressContainer = document.getElementById('progressContainer');
        const progressBar = progressContainer.querySelector('.progress-bar');
        
        progressContainer.style.display = 'block';
        
        // Animate progress bar
        let progress = 0;
        const interval = setInterval(() => {
            progress += Math.random() * 15;
            if (progress > 90) progress = 90;
            
            progressBar.style.width = progress + '%';
        }, 200);

        // Store interval for cleanup
        this.progressInterval = interval;
    }

    hideProgress() {
        const progressContainer = document.getElementById('progressContainer');
        const progressBar = progressContainer.querySelector('.progress-bar');
        
        if (this.progressInterval) {
            clearInterval(this.progressInterval);
        }
        
        // Complete progress
        progressBar.style.width = '100%';
        
        setTimeout(() => {
            progressContainer.style.display = 'none';
            progressBar.style.width = '0%';
        }, 500);
    }

    disableForm(disabled) {
        const fileInput = document.getElementById('fileInput');
        const analyzeBtn = document.getElementById('analyzeBtn');
        
        fileInput.disabled = disabled;
        analyzeBtn.disabled = disabled;
        
        if (disabled) {
            analyzeBtn.innerHTML = '<span class="loading-spinner me-2"></span>Đang phân tích...';
        } else {
            analyzeBtn.innerHTML = '<i class="fas fa-search me-2"></i>Phân tích File';
        }
    }

    showAlert(type, message) {
        const alertContainer = document.getElementById('alertContainer');
        
        const alertHtml = `
            <div class="alert alert-${type} alert-dismissible fade show" role="alert">
                <i class="fas fa-${this.getAlertIcon(type)} me-2"></i>
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        `;
        
        alertContainer.innerHTML = alertHtml;
        
        // Auto dismiss after 5 seconds
        setTimeout(() => {
            const alert = alertContainer.querySelector('.alert');
            if (alert) {
                const bsAlert = new bootstrap.Alert(alert);
                bsAlert.close();
            }
        }, 5000);
    }

    getAlertIcon(type) {
        const icons = {
            'success': 'check-circle',
            'danger': 'exclamation-triangle',
            'warning': 'exclamation-circle',
            'info': 'info-circle'
        };
        return icons[type] || 'info-circle';
    }

    showResults(data) {
        const resultsSection = document.getElementById('results');
        const resultsContent = document.getElementById('resultsContent');
        
        // Determine result type and styling
        const isMalware = data.prediction === 'malware';
        const resultClass = isMalware ? 'result-malware' : 'result-benign';
        const resultIcon = isMalware ? 'fas fa-virus text-danger' : 'fas fa-shield-alt text-success';
        const resultText = isMalware ? 'MALWARE PHÁT HIỆN' : 'FILE AN TOÀN';
        const resultColor = isMalware ? 'text-danger' : 'text-success';
        
        // Generate confidence bar
        let confidenceHtml = '';
        if (data.confidence) {
            const confidencePercent = (data.confidence * 100).toFixed(1);
            const confidenceClass = this.getConfidenceClass(data.confidence);
            
            confidenceHtml = `
                <div class="mb-3">
                    <label class="form-label">Độ tin cậy: ${confidencePercent}%</label>
                    <div class="confidence-bar">
                        <div class="confidence-fill ${confidenceClass}" style="width: ${confidencePercent}%"></div>
                    </div>
                </div>
            `;
        }

        // Generate features table
        let featuresHtml = '';
        if (data.features) {
            featuresHtml = `
                <h5 class="mt-4 mb-3">Đặc trưng File</h5>
                <div class="table-responsive">
                    <table class="table table-sm file-info-table">
                        <tbody>
                            <tr><th>File Entropy</th><td>${data.features.file_entropy}</td></tr>
                            <tr><th>Số Sections</th><td>${data.features.section_count}</td></tr>
                            <tr><th>Số DLL Import</th><td>${data.features.import_dll_count}</td></tr>
                            <tr><th>Số Function Import</th><td>${data.features.import_function_count}</td></tr>
                            <tr><th>Suspicious Imports</th><td>${data.features.suspicious_import_count}</td></tr>
                            <tr><th>Suspicious Strings</th><td>${data.features.suspicious_string_count}</td></tr>
                        </tbody>
                    </table>
                </div>
            `;
        }

        const resultsHtml = `
            <div class="result-card card ${resultClass}">
                <div class="card-body text-center">
                    <i class="${resultIcon}" style="font-size: 3rem; margin-bottom: 1rem;"></i>
                    <h3 class="${resultColor} mb-3">${resultText}</h3>
                    <p class="lead mb-0">File: <strong>${data.filename}</strong></p>
                </div>
            </div>
            
            ${confidenceHtml}
            
            <div class="row mt-4">
                <div class="col-md-6">
                    <h5>Thông tin File</h5>
                    <div class="table-responsive">
                        <table class="table table-sm file-info-table">
                            <tbody>
                                <tr><th>Tên file</th><td>${data.filename}</td></tr>
                                <tr><th>Kích thước</th><td>${this.formatFileSize(data.file_size)}</td></tr>
                                <tr><th>Thời gian phân tích</th><td>${data.analysis_time}s</td></tr>
                                <tr><th>Timestamp</th><td>${new Date(data.timestamp).toLocaleString('vi-VN')}</td></tr>
                            </tbody>
                        </table>
                    </div>
                </div>
                <div class="col-md-6">
                    <h5>Hash Values</h5>
                    <div class="mb-3">
                        <label class="form-label">MD5:</label>
                        <div class="hash-display">${data.md5}</div>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">SHA256:</label>
                        <div class="hash-display">${data.sha256}</div>
                    </div>
                </div>
            </div>
            
            ${featuresHtml}
        `;
        
        resultsContent.innerHTML = resultsHtml;
        resultsSection.style.display = 'block';
        
        // Scroll to results
        resultsSection.scrollIntoView({ behavior: 'smooth' });
    }

    getConfidenceClass(confidence) {
        if (confidence >= 0.8) return 'confidence-high';
        if (confidence >= 0.6) return 'confidence-medium';
        return 'confidence-low';
    }

    formatFileSize(bytes) {
        if (bytes === 0) return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }
}

// Initialize app when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    new MalwareDetectionApp();
});

// Refresh API status every 30 seconds
setInterval(() => {
    if (window.app) {
        window.app.checkApiStatus();
    }
}, 30000);
