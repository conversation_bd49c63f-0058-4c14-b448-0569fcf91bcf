"""
Training Script
Script để train malware detection models
"""

import os
import sys
import argparse
import logging
from pathlib import Path
import matplotlib.pyplot as plt
import seaborn as sns

# Add src to path
sys.path.append(str(Path(__file__).parent.parent))

from models.classifier import MalwareClassifier
from feature_extraction.batch_processor import BatchProcessor


def setup_logging(log_level='INFO'):
    """Setup logging configuration"""
    logging.basicConfig(
        level=getattr(logging, log_level),
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler('training.log'),
            logging.StreamHandler(sys.stdout)
        ]
    )


def create_sample_dataset():
    """
    Tạo sample dataset từ Windows system files (benign) 
    Chỉ để demo - trong thực tế cần malware samples thật
    """
    logger = logging.getLogger(__name__)
    
    # Tạ<PERSON> thư mục samples nếu chưa có
    samples_dir = Path("data/samples")
    samples_dir.mkdir(parents=True, exist_ok=True)
    
    benign_dir = samples_dir / "benign"
    benign_dir.mkdir(exist_ok=True)
    
    # Copy một số system files làm benign samples
    system32_path = Path("C:/Windows/System32")
    sample_files = [
        "notepad.exe", "calc.exe", "mspaint.exe", 
        "winver.exe", "taskmgr.exe"
    ]
    
    copied_files = 0
    for file_name in sample_files:
        src_file = system32_path / file_name
        dst_file = benign_dir / file_name
        
        if src_file.exists() and not dst_file.exists():
            try:
                import shutil
                shutil.copy2(src_file, dst_file)
                copied_files += 1
                logger.info(f"Copied {file_name} to samples")
            except Exception as e:
                logger.warning(f"Không thể copy {file_name}: {e}")
    
    logger.info(f"Đã tạo {copied_files} benign samples")
    
    # Tạo thư mục malware (trống - cần user cung cấp)
    malware_dir = samples_dir / "malware"
    malware_dir.mkdir(exist_ok=True)
    
    return str(benign_dir), str(malware_dir)


def train_model(dataset_path, model_type='random_forest', output_dir='data/models'):
    """
    Train malware detection model
    
    Args:
        dataset_path (str): Đường dẫn dataset CSV
        model_type (str): Loại model
        output_dir (str): Thư mục lưu model
    """
    logger = logging.getLogger(__name__)
    
    # Tạo output directory
    output_dir = Path(output_dir)
    output_dir.mkdir(parents=True, exist_ok=True)
    
    # Initialize classifier
    classifier = MalwareClassifier(model_type=model_type)
    
    # Load dataset
    logger.info(f"Loading dataset từ {dataset_path}...")
    X, y = classifier.load_dataset(dataset_path)
    
    if X is None or y is None:
        logger.error("Không thể load dataset")
        return None
    
    # Cross validation
    logger.info("Thực hiện cross validation...")
    cv_results = classifier.cross_validate(X, y, cv=5)
    
    # Train model
    logger.info("Training model...")
    train_results = classifier.train(X, y, test_size=0.2, random_state=42)
    
    if train_results is None:
        logger.error("Training thất bại")
        return None
    
    # Save model
    model_path = output_dir / f"{model_type}_model.joblib"
    scaler_path = output_dir / f"{model_type}_scaler.joblib"
    
    classifier.save_model(str(model_path), str(scaler_path))
    
    # Feature importance
    if model_type == 'random_forest':
        importance = classifier.get_feature_importance()
        
        # Plot feature importance
        if importance:
            plt.figure(figsize=(12, 8))
            features = list(importance.keys())[:15]  # Top 15
            values = list(importance.values())[:15]
            
            plt.barh(features, values)
            plt.xlabel('Feature Importance')
            plt.title('Top 15 Feature Importance - Random Forest')
            plt.tight_layout()
            plt.savefig(output_dir / 'feature_importance.png', dpi=300, bbox_inches='tight')
            plt.close()
    
    # Plot confusion matrix
    plt.figure(figsize=(8, 6))
    sns.heatmap(train_results['confusion_matrix'], 
                annot=True, fmt='d', cmap='Blues',
                xticklabels=['Benign', 'Malware'],
                yticklabels=['Benign', 'Malware'])
    plt.title('Confusion Matrix')
    plt.ylabel('True Label')
    plt.xlabel('Predicted Label')
    plt.tight_layout()
    plt.savefig(output_dir / 'confusion_matrix.png', dpi=300, bbox_inches='tight')
    plt.close()
    
    # Save training report
    report_path = output_dir / 'training_report.txt'
    with open(report_path, 'w', encoding='utf-8') as f:
        f.write(f"Malware Detection Model Training Report\n")
        f.write(f"=====================================\n\n")
        f.write(f"Model Type: {model_type}\n")
        f.write(f"Dataset: {dataset_path}\n")
        f.write(f"Training Samples: {len(X)}\n\n")
        
        f.write(f"Cross Validation Results:\n")
        if cv_results:
            f.write(f"Mean Accuracy: {cv_results['mean_accuracy']:.4f}\n")
            f.write(f"Std Accuracy: {cv_results['std_accuracy']:.4f}\n\n")
        
        f.write(f"Test Results:\n")
        f.write(f"Accuracy: {train_results['test_accuracy']:.4f}\n")
        f.write(f"Precision: {train_results['precision']:.4f}\n")
        f.write(f"Recall: {train_results['recall']:.4f}\n")
        f.write(f"F1-Score: {train_results['f1_score']:.4f}\n")
        if 'roc_auc' in train_results:
            f.write(f"ROC AUC: {train_results['roc_auc']:.4f}\n")
        
        f.write(f"\nClassification Report:\n")
        f.write(train_results['classification_report'])
    
    logger.info(f"Training hoàn thành! Model đã lưu vào {output_dir}")
    return classifier


def main():
    """Main function"""
    parser = argparse.ArgumentParser(description='Train Malware Detection Model')
    parser.add_argument('--dataset', type=str, help='Đường dẫn dataset CSV')
    parser.add_argument('--malware-dir', type=str, help='Thư mục chứa malware samples')
    parser.add_argument('--benign-dir', type=str, help='Thư mục chứa benign samples')
    parser.add_argument('--model-type', type=str, default='random_forest',
                       choices=['random_forest', 'svm'],
                       help='Loại model (default: random_forest)')
    parser.add_argument('--output-dir', type=str, default='data/models',
                       help='Thư mục lưu model (default: data/models)')
    parser.add_argument('--create-sample', action='store_true',
                       help='Tạo sample dataset từ system files')
    parser.add_argument('--log-level', type=str, default='INFO',
                       choices=['DEBUG', 'INFO', 'WARNING', 'ERROR'],
                       help='Log level (default: INFO)')
    
    args = parser.parse_args()
    
    # Setup logging
    setup_logging(args.log_level)
    logger = logging.getLogger(__name__)
    
    try:
        # Tạo sample dataset nếu được yêu cầu
        if args.create_sample:
            logger.info("Tạo sample dataset...")
            benign_dir, malware_dir = create_sample_dataset()
            logger.info(f"Sample dataset đã tạo:")
            logger.info(f"  Benign samples: {benign_dir}")
            logger.info(f"  Malware samples: {malware_dir}")
            logger.info("Lưu ý: Cần thêm malware samples vào thư mục malware để train model!")
            return
        
        # Tạo dataset từ directories
        if args.malware_dir and args.benign_dir:
            logger.info("Tạo dataset từ directories...")
            processor = BatchProcessor()
            dataset_path = "data/datasets/malware_dataset.csv"
            
            # Tạo thư mục datasets
            Path("data/datasets").mkdir(parents=True, exist_ok=True)
            
            dataset = processor.create_dataset(
                args.malware_dir, 
                args.benign_dir, 
                dataset_path
            )
            
            if dataset.empty:
                logger.error("Không thể tạo dataset")
                return
            
            # Validate dataset
            stats = processor.validate_dataset(dataset_path)
            
        elif args.dataset:
            dataset_path = args.dataset
        else:
            logger.error("Cần cung cấp --dataset hoặc --malware-dir và --benign-dir")
            return
        
        # Train model
        classifier = train_model(
            dataset_path=dataset_path,
            model_type=args.model_type,
            output_dir=args.output_dir
        )
        
        if classifier:
            logger.info("Training thành công!")
        else:
            logger.error("Training thất bại!")
            
    except Exception as e:
        logger.error(f"Lỗi trong quá trình training: {e}")
        raise


if __name__ == "__main__":
    main()
