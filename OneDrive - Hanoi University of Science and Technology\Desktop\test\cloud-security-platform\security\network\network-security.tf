# Network Security Configuration for Cloud Security Platform

# Private Google Access for subnet
resource "google_compute_subnetwork" "private_subnet" {
  name                     = "${var.project_name}-private-subnet"
  ip_cidr_range           = "********/16"
  region                  = var.region
  network                 = google_compute_network.vpc.id
  private_ip_google_access = true
  
  secondary_ip_range {
    range_name    = "k8s-pod-range"
    ip_cidr_range = "********/16"
  }
  
  secondary_ip_range {
    range_name    = "k8s-service-range"
    ip_cidr_range = "********/16"
  }
  
  log_config {
    aggregation_interval = "INTERVAL_10_MIN"
    flow_sampling       = 0.5
    metadata           = "INCLUDE_ALL_METADATA"
  }
}

# Firewall rules for enhanced security
resource "google_compute_firewall" "deny_all_ingress" {
  name      = "${var.project_name}-deny-all-ingress"
  network   = google_compute_network.vpc.name
  direction = "INGRESS"
  priority  = 65534
  
  deny {
    protocol = "all"
  }
  
  source_ranges = ["0.0.0.0/0"]
  
  log_config {
    metadata = "INCLUDE_ALL_METADATA"
  }
}

resource "google_compute_firewall" "allow_internal_ingress" {
  name      = "${var.project_name}-allow-internal-ingress"
  network   = google_compute_network.vpc.name
  direction = "INGRESS"
  priority  = 1000
  
  allow {
    protocol = "tcp"
  }
  
  allow {
    protocol = "udp"
  }
  
  allow {
    protocol = "icmp"
  }
  
  source_ranges = [
    "10.0.0.0/16",
    "********/16",
    "********/16",
    "********/16"
  ]
  
  log_config {
    metadata = "INCLUDE_ALL_METADATA"
  }
}

resource "google_compute_firewall" "allow_gke_master" {
  name      = "${var.project_name}-allow-gke-master"
  network   = google_compute_network.vpc.name
  direction = "INGRESS"
  priority  = 1000
  
  allow {
    protocol = "tcp"
    ports    = ["443", "10250"]
  }
  
  source_ranges = ["172.16.0.0/28"]  # GKE master CIDR
  target_tags   = ["gke-node"]
  
  log_config {
    metadata = "INCLUDE_ALL_METADATA"
  }
}

resource "google_compute_firewall" "allow_health_checks" {
  name      = "${var.project_name}-allow-health-checks"
  network   = google_compute_network.vpc.name
  direction = "INGRESS"
  priority  = 1000
  
  allow {
    protocol = "tcp"
    ports    = ["80", "443", "8080", "5000", "5001"]
  }
  
  source_ranges = [
    "130.211.0.0/22",
    "35.191.0.0/16"
  ]
  
  target_tags = ["http-server", "https-server"]
  
  log_config {
    metadata = "INCLUDE_ALL_METADATA"
  }
}

resource "google_compute_firewall" "allow_load_balancer" {
  name      = "${var.project_name}-allow-load-balancer"
  network   = google_compute_network.vpc.name
  direction = "INGRESS"
  priority  = 1000
  
  allow {
    protocol = "tcp"
    ports    = ["80", "443"]
  }
  
  source_ranges = ["0.0.0.0/0"]
  target_tags   = ["load-balancer"]
  
  log_config {
    metadata = "INCLUDE_ALL_METADATA"
  }
}

# Egress rules
resource "google_compute_firewall" "deny_all_egress" {
  name      = "${var.project_name}-deny-all-egress"
  network   = google_compute_network.vpc.name
  direction = "EGRESS"
  priority  = 65534
  
  deny {
    protocol = "all"
  }
  
  destination_ranges = ["0.0.0.0/0"]
  
  log_config {
    metadata = "INCLUDE_ALL_METADATA"
  }
}

resource "google_compute_firewall" "allow_internal_egress" {
  name      = "${var.project_name}-allow-internal-egress"
  network   = google_compute_network.vpc.name
  direction = "EGRESS"
  priority  = 1000
  
  allow {
    protocol = "tcp"
  }
  
  allow {
    protocol = "udp"
  }
  
  allow {
    protocol = "icmp"
  }
  
  destination_ranges = [
    "10.0.0.0/16",
    "********/16",
    "********/16",
    "********/16"
  ]
  
  log_config {
    metadata = "INCLUDE_ALL_METADATA"
  }
}

resource "google_compute_firewall" "allow_google_apis" {
  name      = "${var.project_name}-allow-google-apis"
  network   = google_compute_network.vpc.name
  direction = "EGRESS"
  priority  = 1000
  
  allow {
    protocol = "tcp"
    ports    = ["443"]
  }
  
  destination_ranges = [
    "199.36.153.8/30",  # restricted.googleapis.com
    "199.36.153.4/30"   # private.googleapis.com
  ]
  
  log_config {
    metadata = "INCLUDE_ALL_METADATA"
  }
}

resource "google_compute_firewall" "allow_dns" {
  name      = "${var.project_name}-allow-dns"
  network   = google_compute_network.vpc.name
  direction = "EGRESS"
  priority  = 1000
  
  allow {
    protocol = "tcp"
    ports    = ["53"]
  }
  
  allow {
    protocol = "udp"
    ports    = ["53"]
  }
  
  destination_ranges = ["*******/32", "*******/32"]
  
  log_config {
    metadata = "INCLUDE_ALL_METADATA"
  }
}

# Cloud Armor security policy
resource "google_compute_security_policy" "security_platform_policy" {
  name        = "${var.project_name}-security-policy"
  description = "Security policy for Security Platform"
  
  # Block known attack patterns
  rule {
    action   = "deny(403)"
    priority = "1000"
    match {
      expr {
        expression = "origin.ip == '*********'"
      }
    }
    description = "Block specific malicious IP"
  }
  
  # SQL injection protection
  rule {
    action   = "deny(403)"
    priority = "2000"
    match {
      expr {
        expression = "has(request.headers['user-agent']) && request.headers['user-agent'].contains('sqlmap')"
      }
    }
    description = "Block SQL injection tools"
  }
  
  # XSS protection
  rule {
    action   = "deny(403)"
    priority = "3000"
    match {
      expr {
        expression = "request.url_map.contains('<script>')"
      }
    }
    description = "Block XSS attempts"
  }
  
  # Rate limiting for API endpoints
  rule {
    action   = "rate_based_ban"
    priority = "4000"
    match {
      expr {
        expression = "request.url_map.startsWith('/api/')"
      }
    }
    rate_limit_options {
      conform_action = "allow"
      exceed_action  = "deny(429)"
      enforce_on_key = "IP"
      
      rate_limit_threshold {
        count        = 100
        interval_sec = 60
      }
      
      ban_duration_sec = 300
    }
    description = "Rate limit API endpoints"
  }
  
  # Default allow rule
  rule {
    action   = "allow"
    priority = "2147483647"
    match {
      versioned_expr = "SRC_IPS_V1"
      config {
        src_ip_ranges = ["*"]
      }
    }
    description = "Default allow rule"
  }
}

# Private Service Connect for Google APIs
resource "google_compute_global_address" "private_service_connect" {
  name          = "${var.project_name}-psc-address"
  purpose       = "PRIVATE_SERVICE_CONNECT"
  network       = google_compute_network.vpc.id
  address_type  = "INTERNAL"
  prefix_length = 16
}

resource "google_compute_global_forwarding_rule" "private_service_connect" {
  name                  = "${var.project_name}-psc-forwarding-rule"
  target                = "all-apis"
  network               = google_compute_network.vpc.id
  ip_address            = google_compute_global_address.private_service_connect.address
  load_balancing_scheme = ""
}

# VPC Flow Logs
resource "google_compute_subnetwork" "logged_subnet" {
  name          = "${var.project_name}-logged-subnet"
  ip_cidr_range = "********/16"
  region        = var.region
  network       = google_compute_network.vpc.id
  
  log_config {
    aggregation_interval = "INTERVAL_5_SEC"
    flow_sampling       = 1.0
    metadata           = "INCLUDE_ALL_METADATA"
    metadata_fields = [
      "src_instance",
      "dst_instance",
      "src_vpc",
      "dst_vpc"
    ]
  }
}

# Network Security Monitoring
resource "google_logging_metric" "suspicious_network_activity" {
  name   = "suspicious_network_activity"
  filter = <<-EOT
    resource.type="gce_subnetwork"
    jsonPayload.connection.dest_port=(22 OR 3389 OR 1433 OR 3306)
    jsonPayload.connection.protocol="TCP"
    jsonPayload.reporter="SRC"
  EOT
  
  metric_descriptor {
    metric_kind = "GAUGE"
    value_type  = "INT64"
    display_name = "Suspicious Network Activity"
  }
  
  value_extractor = "EXTRACT(jsonPayload.bytes_sent)"
}

resource "google_monitoring_alert_policy" "network_security_alert" {
  display_name = "Network Security Alert"
  combiner     = "OR"
  
  conditions {
    display_name = "Suspicious network activity detected"
    
    condition_threshold {
      filter          = "metric.type=\"logging.googleapis.com/user/suspicious_network_activity\""
      duration        = "60s"
      comparison      = "COMPARISON_GREATER_THAN"
      threshold_value = 100
      
      aggregations {
        alignment_period   = "60s"
        per_series_aligner = "ALIGN_RATE"
      }
    }
  }
  
  notification_channels = [
    google_monitoring_notification_channel.email.name
  ]
  
  alert_strategy {
    auto_close = "1800s"
  }
}

resource "google_monitoring_notification_channel" "email" {
  display_name = "Security Team Email"
  type         = "email"
  
  labels = {
    email_address = var.security_email
  }
}

# DDoS protection
resource "google_compute_security_policy" "ddos_protection" {
  name        = "${var.project_name}-ddos-protection"
  description = "DDoS protection policy"
  
  # Adaptive protection
  adaptive_protection_config {
    layer_7_ddos_defense_config {
      enable = true
    }
  }
  
  # Rate limiting
  rule {
    action   = "rate_based_ban"
    priority = "1000"
    match {
      versioned_expr = "SRC_IPS_V1"
      config {
        src_ip_ranges = ["*"]
      }
    }
    rate_limit_options {
      conform_action = "allow"
      exceed_action  = "deny(429)"
      enforce_on_key = "IP"
      
      rate_limit_threshold {
        count        = 1000
        interval_sec = 60
      }
      
      ban_duration_sec = 600
    }
    description = "DDoS rate limiting"
  }
  
  # Default allow
  rule {
    action   = "allow"
    priority = "2147483647"
    match {
      versioned_expr = "SRC_IPS_V1"
      config {
        src_ip_ranges = ["*"]
      }
    }
    description = "Default allow"
  }
}

# Outputs
output "network_security_config" {
  description = "Network security configuration"
  value = {
    vpc_name           = google_compute_network.vpc.name
    private_subnet     = google_compute_subnetwork.private_subnet.name
    security_policy    = google_compute_security_policy.security_platform_policy.name
    ddos_policy       = google_compute_security_policy.ddos_protection.name
  }
}
