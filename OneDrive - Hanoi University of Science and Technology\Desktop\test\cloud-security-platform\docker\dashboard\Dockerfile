# Integrated Security Dashboard Dockerfile
FROM node:18-alpine AS frontend-builder

# Set working directory for frontend build
WORKDIR /frontend

# Copy package files
COPY dashboard/package*.json ./

# Install dependencies
RUN npm ci --only=production

# Copy frontend source
COPY dashboard/src ./src
COPY dashboard/public ./public

# Build frontend
RUN npm run build

# Python backend stage
FROM python:3.9-slim

# Set working directory
WORKDIR /app

# Set environment variables
ENV PYTHONPATH=/app
ENV PYTHONUNBUFFERED=1
ENV FLASK_APP=dashboard_app
ENV FLASK_ENV=production

# Install system dependencies
RUN apt-get update && apt-get install -y \
    gcc \
    g++ \
    libc6-dev \
    libffi-dev \
    libssl-dev \
    curl \
    nginx \
    && rm -rf /var/lib/apt/lists/*

# Create non-root user
RUN groupadd -r appuser && useradd -r -g appuser appuser

# Copy requirements first for better caching
COPY requirements.txt .

# Install Python dependencies
RUN pip install --no-cache-dir -r requirements.txt

# Copy application code
COPY src/ ./src/
COPY config.py .
COPY dashboard_app.py .

# Copy built frontend from previous stage
COPY --from=frontend-builder /frontend/dist ./static/

# Create necessary directories
RUN mkdir -p logs templates \
    && chown -R appuser:appuser /app

# Copy nginx configuration
COPY docker/dashboard/nginx.conf /etc/nginx/nginx.conf

# Copy entrypoint script
COPY docker/dashboard/entrypoint.sh /entrypoint.sh
RUN chmod +x /entrypoint.sh

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \
    CMD curl -f http://localhost:8080/health || exit 1

# Switch to non-root user
USER appuser

# Expose port
EXPOSE 8080

# Set entrypoint
ENTRYPOINT ["/entrypoint.sh"]

# Default command
CMD ["python", "dashboard_app.py"]
