/* Custom CSS for AI Malware Detection System */

:root {
    --primary-color: #2c3e50;
    --secondary-color: #3498db;
    --success-color: #27ae60;
    --danger-color: #e74c3c;
    --warning-color: #f39c12;
    --info-color: #17a2b8;
    --light-color: #f8f9fa;
    --dark-color: #343a40;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    line-height: 1.6;
}

/* Hero Section */
.hero-section {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
    min-height: 60vh;
    display: flex;
    align-items: center;
}

.hero-section h1 {
    font-weight: 700;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
}

/* Cards */
.card {
    border: none;
    border-radius: 15px;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(0,0,0,0.15);
}

.card-header {
    border-radius: 15px 15px 0 0 !important;
    border-bottom: none;
    padding: 1.5rem;
}

/* Buttons */
.btn {
    border-radius: 25px;
    padding: 0.75rem 2rem;
    font-weight: 600;
    transition: all 0.3s ease;
}

.btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0,0,0,0.2);
}

.btn-primary {
    background: linear-gradient(45deg, var(--primary-color), var(--secondary-color));
    border: none;
}

.btn-success {
    background: linear-gradient(45deg, var(--success-color), #2ecc71);
    border: none;
}

.btn-danger {
    background: linear-gradient(45deg, var(--danger-color), #c0392b);
    border: none;
}

/* Form Controls */
.form-control {
    border-radius: 10px;
    border: 2px solid #e9ecef;
    padding: 0.75rem 1rem;
    transition: border-color 0.3s ease, box-shadow 0.3s ease;
}

.form-control:focus {
    border-color: var(--secondary-color);
    box-shadow: 0 0 0 0.2rem rgba(52, 152, 219, 0.25);
}

/* Progress Bar */
.progress {
    height: 8px;
    border-radius: 10px;
    background-color: #e9ecef;
}

.progress-bar {
    border-radius: 10px;
    background: linear-gradient(45deg, var(--secondary-color), var(--info-color));
}

/* Status Indicator */
#status-indicator {
    font-size: 0.9rem;
}

.status-online {
    color: var(--success-color) !important;
}

.status-offline {
    color: var(--danger-color) !important;
}

.status-loading {
    color: var(--warning-color) !important;
}

/* Results Section */
.result-card {
    border-left: 5px solid var(--secondary-color);
    margin-bottom: 1rem;
}

.result-malware {
    border-left-color: var(--danger-color);
    background-color: #fdf2f2;
}

.result-benign {
    border-left-color: var(--success-color);
    background-color: #f0f9f0;
}

.confidence-bar {
    height: 20px;
    border-radius: 10px;
    background-color: #e9ecef;
    overflow: hidden;
}

.confidence-fill {
    height: 100%;
    border-radius: 10px;
    transition: width 0.5s ease;
}

.confidence-high {
    background: linear-gradient(45deg, var(--success-color), #2ecc71);
}

.confidence-medium {
    background: linear-gradient(45deg, var(--warning-color), #e67e22);
}

.confidence-low {
    background: linear-gradient(45deg, var(--danger-color), #c0392b);
}

/* Feature Box */
.feature-box {
    padding: 2rem;
    border-radius: 15px;
    background: white;
    box-shadow: 0 5px 15px rgba(0,0,0,0.08);
    transition: transform 0.3s ease;
    height: 100%;
}

.feature-box:hover {
    transform: translateY(-10px);
}

.feature-box i {
    margin-bottom: 1rem;
}

/* Alerts */
.alert {
    border-radius: 10px;
    border: none;
    padding: 1rem 1.5rem;
}

.alert-success {
    background: linear-gradient(45deg, rgba(39, 174, 96, 0.1), rgba(46, 204, 113, 0.1));
    color: var(--success-color);
    border-left: 4px solid var(--success-color);
}

.alert-danger {
    background: linear-gradient(45deg, rgba(231, 76, 60, 0.1), rgba(192, 57, 43, 0.1));
    color: var(--danger-color);
    border-left: 4px solid var(--danger-color);
}

.alert-warning {
    background: linear-gradient(45deg, rgba(243, 156, 18, 0.1), rgba(230, 126, 34, 0.1));
    color: var(--warning-color);
    border-left: 4px solid var(--warning-color);
}

.alert-info {
    background: linear-gradient(45deg, rgba(23, 162, 184, 0.1), rgba(52, 152, 219, 0.1));
    color: var(--info-color);
    border-left: 4px solid var(--info-color);
}

/* File Info Table */
.file-info-table {
    background: white;
    border-radius: 10px;
    overflow: hidden;
}

.file-info-table th {
    background: var(--light-color);
    border: none;
    font-weight: 600;
    color: var(--dark-color);
}

.file-info-table td {
    border: none;
    padding: 0.75rem;
}

/* Hash Display */
.hash-display {
    font-family: 'Courier New', monospace;
    font-size: 0.85rem;
    background: var(--light-color);
    padding: 0.5rem;
    border-radius: 5px;
    word-break: break-all;
}

/* Loading Animation */
.loading-spinner {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid rgba(255,255,255,.3);
    border-radius: 50%;
    border-top-color: #fff;
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* Responsive Design */
@media (max-width: 768px) {
    .hero-section {
        min-height: 50vh;
        padding: 3rem 0;
    }
    
    .hero-section h1 {
        font-size: 2rem;
    }
    
    .feature-box {
        margin-bottom: 2rem;
    }
    
    .btn {
        padding: 0.5rem 1.5rem;
    }
}

/* Smooth Scrolling */
html {
    scroll-behavior: smooth;
}

/* Custom Scrollbar */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: var(--light-color);
}

::-webkit-scrollbar-thumb {
    background: var(--secondary-color);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: var(--primary-color);
}
