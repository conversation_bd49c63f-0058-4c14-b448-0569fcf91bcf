"""
Alert System Module
Hệ thống cảnh báo cho Network IDS
"""

import sys
import time
import smtplib
import json
import requests
from pathlib import Path
from datetime import datetime, timedelta
from collections import defaultdict
from email.mime.text import MIMEText
from email.mime.multipart import MI<PERSON>Multipart
import logging

# Add parent directory to path
sys.path.append(str(Path(__file__).parent.parent.parent))

import config


class AlertManager:
    """Class quản lý alerts và notifications"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.alert_history = []
        self.alert_cooldowns = defaultdict(datetime)
        self.notification_channels = []
        
        # Initialize notification channels
        self._init_notification_channels()
        
    def _init_notification_channels(self):
        """Khởi tạo các notification channels"""
        alert_config = config.ALERT_CONFIG
        
        # Email notification
        if alert_config['email']['enabled']:
            self.notification_channels.append(EmailNotifier(alert_config['email']))
        
        # Slack notification
        if alert_config['slack']['enabled']:
            self.notification_channels.append(SlackNotifier(alert_config['slack']))
        
        # SMS notification
        if alert_config['sms']['enabled']:
            self.notification_channels.append(SMSNotifier(alert_config['sms']))
        
        # Console notification (always enabled)
        self.notification_channels.append(ConsoleNotifier())
    
    def create_alert(self, alert_type, severity, source_ip, message, details=None):
        """
        Tạo alert mới
        
        Args:
            alert_type (str): Loại alert
            severity (str): Mức độ nghiêm trọng
            source_ip (str): IP nguồn
            message (str): Thông điệp alert
            details (dict): Chi tiết bổ sung
            
        Returns:
            dict: Alert object
        """
        alert = {
            'id': self._generate_alert_id(),
            'timestamp': datetime.now(),
            'type': alert_type,
            'severity': severity,
            'source_ip': source_ip,
            'message': message,
            'details': details or {},
            'status': 'active',
            'acknowledged': False,
            'acknowledged_by': None,
            'acknowledged_at': None
        }
        
        # Check cooldown
        cooldown_key = f"{alert_type}_{source_ip}"
        if self._is_in_cooldown(cooldown_key):
            self.logger.debug(f"Alert {cooldown_key} trong cooldown period")
            return None
        
        # Add to history
        self.alert_history.append(alert)
        
        # Set cooldown
        self.alert_cooldowns[cooldown_key] = datetime.now()
        
        # Send notifications
        self._send_notifications(alert)
        
        self.logger.info(f"Alert created: {alert['id']} - {alert['type']} - {alert['severity']}")
        
        return alert
    
    def _generate_alert_id(self):
        """Tạo unique alert ID"""
        timestamp = int(time.time() * 1000)
        return f"ALERT_{timestamp}"
    
    def _is_in_cooldown(self, cooldown_key):
        """Kiểm tra alert có trong cooldown period không"""
        if cooldown_key not in self.alert_cooldowns:
            return False
        
        last_alert_time = self.alert_cooldowns[cooldown_key]
        cooldown_duration = config.ALERT_CONFIG.get('alert_cooldown', 300)  # 5 minutes default
        
        return (datetime.now() - last_alert_time).total_seconds() < cooldown_duration
    
    def _send_notifications(self, alert):
        """Gửi notifications qua các channels"""
        for channel in self.notification_channels:
            try:
                channel.send_notification(alert)
            except Exception as e:
                self.logger.error(f"Lỗi khi gửi notification qua {channel.__class__.__name__}: {e}")
    
    def acknowledge_alert(self, alert_id, acknowledged_by):
        """
        Acknowledge alert
        
        Args:
            alert_id (str): Alert ID
            acknowledged_by (str): Người acknowledge
            
        Returns:
            bool: Success status
        """
        for alert in self.alert_history:
            if alert['id'] == alert_id:
                alert['acknowledged'] = True
                alert['acknowledged_by'] = acknowledged_by
                alert['acknowledged_at'] = datetime.now()
                
                self.logger.info(f"Alert {alert_id} acknowledged by {acknowledged_by}")
                return True
        
        return False
    
    def get_active_alerts(self, severity_filter=None, limit=100):
        """
        Lấy danh sách active alerts
        
        Args:
            severity_filter (str): Filter theo severity
            limit (int): Số lượng alerts tối đa
            
        Returns:
            list: Danh sách alerts
        """
        active_alerts = [
            alert for alert in self.alert_history
            if alert['status'] == 'active' and not alert['acknowledged']
        ]
        
        if severity_filter:
            active_alerts = [
                alert for alert in active_alerts
                if alert['severity'] == severity_filter
            ]
        
        # Sort by timestamp (newest first)
        active_alerts.sort(key=lambda x: x['timestamp'], reverse=True)
        
        return active_alerts[:limit]
    
    def get_alert_statistics(self, time_range_hours=24):
        """
        Lấy thống kê alerts
        
        Args:
            time_range_hours (int): Khoảng thời gian (hours)
            
        Returns:
            dict: Alert statistics
        """
        cutoff_time = datetime.now() - timedelta(hours=time_range_hours)
        
        recent_alerts = [
            alert for alert in self.alert_history
            if alert['timestamp'] >= cutoff_time
        ]
        
        stats = {
            'total_alerts': len(recent_alerts),
            'active_alerts': len([a for a in recent_alerts if a['status'] == 'active']),
            'acknowledged_alerts': len([a for a in recent_alerts if a['acknowledged']]),
            'severity_distribution': defaultdict(int),
            'type_distribution': defaultdict(int),
            'top_source_ips': defaultdict(int)
        }
        
        for alert in recent_alerts:
            stats['severity_distribution'][alert['severity']] += 1
            stats['type_distribution'][alert['type']] += 1
            stats['top_source_ips'][alert['source_ip']] += 1
        
        # Convert to regular dicts and sort
        stats['severity_distribution'] = dict(stats['severity_distribution'])
        stats['type_distribution'] = dict(stats['type_distribution'])
        
        # Top 10 source IPs
        top_ips = sorted(
            stats['top_source_ips'].items(),
            key=lambda x: x[1],
            reverse=True
        )[:10]
        stats['top_source_ips'] = dict(top_ips)
        
        return stats
    
    def export_alerts(self, filename, time_range_hours=24):
        """
        Export alerts to JSON file
        
        Args:
            filename (str): Output filename
            time_range_hours (int): Time range in hours
        """
        try:
            cutoff_time = datetime.now() - timedelta(hours=time_range_hours)
            
            alerts_to_export = [
                alert for alert in self.alert_history
                if alert['timestamp'] >= cutoff_time
            ]
            
            # Convert datetime objects to strings
            for alert in alerts_to_export:
                alert['timestamp'] = alert['timestamp'].isoformat()
                if alert['acknowledged_at']:
                    alert['acknowledged_at'] = alert['acknowledged_at'].isoformat()
            
            output_path = config.LOGS_DIR / filename
            with open(output_path, 'w', encoding='utf-8') as f:
                json.dump(alerts_to_export, f, indent=2, ensure_ascii=False)
            
            self.logger.info(f"Alerts exported to: {output_path}")
            
        except Exception as e:
            self.logger.error(f"Lỗi khi export alerts: {e}")


class NotificationChannel:
    """Base class cho notification channels"""
    
    def send_notification(self, alert):
        """Send notification - to be implemented by subclasses"""
        raise NotImplementedError


class ConsoleNotifier(NotificationChannel):
    """Console notification channel"""
    
    def send_notification(self, alert):
        """Print alert to console"""
        severity_colors = {
            'critical': '\033[91m',  # Red
            'high': '\033[93m',      # Yellow
            'medium': '\033[94m',    # Blue
            'low': '\033[92m'        # Green
        }
        
        color = severity_colors.get(alert['severity'].lower(), '\033[0m')
        reset_color = '\033[0m'
        
        print(f"{color}🚨 ALERT [{alert['severity'].upper()}] {alert['type']}{reset_color}")
        print(f"   Time: {alert['timestamp'].strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"   Source: {alert['source_ip']}")
        print(f"   Message: {alert['message']}")
        if alert['details']:
            print(f"   Details: {alert['details']}")
        print()


class EmailNotifier(NotificationChannel):
    """Email notification channel"""
    
    def __init__(self, email_config):
        self.config = email_config
        self.logger = logging.getLogger(__name__)
    
    def send_notification(self, alert):
        """Send email notification"""
        try:
            # Create message
            msg = MIMEMultipart()
            msg['From'] = self.config['username']
            msg['To'] = ', '.join(self.config['recipients'])
            msg['Subject'] = f"[NETWORK IDS] {alert['severity'].upper()} Alert: {alert['type']}"
            
            # Email body
            body = f"""
Network Intrusion Detection System Alert

Alert ID: {alert['id']}
Timestamp: {alert['timestamp'].strftime('%Y-%m-%d %H:%M:%S')}
Type: {alert['type']}
Severity: {alert['severity'].upper()}
Source IP: {alert['source_ip']}

Message: {alert['message']}

Details:
{json.dumps(alert['details'], indent=2)}

Please investigate this alert immediately.

---
Network IDS Alert System
            """
            
            msg.attach(MIMEText(body, 'plain'))
            
            # Send email
            server = smtplib.SMTP(self.config['smtp_server'], self.config['smtp_port'])
            server.starttls()
            server.login(self.config['username'], self.config['password'])
            
            text = msg.as_string()
            server.sendmail(self.config['username'], self.config['recipients'], text)
            server.quit()
            
            self.logger.info(f"Email alert sent for {alert['id']}")
            
        except Exception as e:
            self.logger.error(f"Lỗi khi gửi email alert: {e}")


class SlackNotifier(NotificationChannel):
    """Slack notification channel"""
    
    def __init__(self, slack_config):
        self.config = slack_config
        self.logger = logging.getLogger(__name__)
    
    def send_notification(self, alert):
        """Send Slack notification"""
        try:
            # Slack message payload
            severity_colors = {
                'critical': '#FF0000',  # Red
                'high': '#FFA500',      # Orange
                'medium': '#FFFF00',    # Yellow
                'low': '#00FF00'        # Green
            }
            
            color = severity_colors.get(alert['severity'].lower(), '#808080')
            
            payload = {
                'channel': self.config['channel'],
                'username': 'Network IDS',
                'icon_emoji': ':warning:',
                'attachments': [{
                    'color': color,
                    'title': f"{alert['severity'].upper()} Alert: {alert['type']}",
                    'fields': [
                        {
                            'title': 'Alert ID',
                            'value': alert['id'],
                            'short': True
                        },
                        {
                            'title': 'Source IP',
                            'value': alert['source_ip'],
                            'short': True
                        },
                        {
                            'title': 'Timestamp',
                            'value': alert['timestamp'].strftime('%Y-%m-%d %H:%M:%S'),
                            'short': True
                        },
                        {
                            'title': 'Message',
                            'value': alert['message'],
                            'short': False
                        }
                    ],
                    'footer': 'Network IDS Alert System',
                    'ts': int(alert['timestamp'].timestamp())
                }]
            }
            
            # Send to Slack
            response = requests.post(
                self.config['webhook_url'],
                json=payload,
                timeout=10
            )
            response.raise_for_status()
            
            self.logger.info(f"Slack alert sent for {alert['id']}")
            
        except Exception as e:
            self.logger.error(f"Lỗi khi gửi Slack alert: {e}")


class SMSNotifier(NotificationChannel):
    """SMS notification channel using Twilio"""
    
    def __init__(self, sms_config):
        self.config = sms_config
        self.logger = logging.getLogger(__name__)
        
        try:
            from twilio.rest import Client
            self.client = Client(self.config['twilio_sid'], self.config['twilio_token'])
        except ImportError:
            self.logger.error("Twilio library không có sẵn. Cài đặt: pip install twilio")
            self.client = None
    
    def send_notification(self, alert):
        """Send SMS notification"""
        if not self.client:
            return
        
        try:
            message_body = f"""
NETWORK IDS ALERT
{alert['severity'].upper()}: {alert['type']}
Source: {alert['source_ip']}
Time: {alert['timestamp'].strftime('%H:%M:%S')}
{alert['message']}
            """.strip()
            
            # Send to all configured phone numbers
            for phone_number in self.config['phone_numbers']:
                message = self.client.messages.create(
                    body=message_body,
                    from_='+1234567890',  # Your Twilio number
                    to=phone_number
                )
                
                self.logger.info(f"SMS alert sent to {phone_number} for {alert['id']}")
                
        except Exception as e:
            self.logger.error(f"Lỗi khi gửi SMS alert: {e}")


if __name__ == "__main__":
    # Test alert system
    logging.basicConfig(level=logging.INFO)
    
    # Create alert manager
    alert_manager = AlertManager()
    
    # Test alerts
    alert_manager.create_alert(
        alert_type='PORT_SCAN',
        severity='high',
        source_ip='*************',
        message='Port scan detected from suspicious IP',
        details={'ports_scanned': 50, 'scan_type': 'TCP SYN'}
    )
    
    alert_manager.create_alert(
        alert_type='HTTP_SUSPICIOUS',
        severity='medium',
        source_ip='*********',
        message='Suspicious HTTP request detected',
        details={'url': '/admin/login.php', 'method': 'POST'}
    )
    
    # Show statistics
    stats = alert_manager.get_alert_statistics()
    print("Alert Statistics:")
    print(f"  Total alerts: {stats['total_alerts']}")
    print(f"  Active alerts: {stats['active_alerts']}")
    print(f"  Severity distribution: {stats['severity_distribution']}")
    
    # Export alerts
    alert_manager.export_alerts('test_alerts.json')
    
    print("✅ Alert system test completed!")
