"""
Network Monitor Module
Module monitor network traffic real-time
"""

import sys
import time
import threading
from pathlib import Path
from datetime import datetime, timedelta
from collections import defaultdict, deque
import logging
import json

# Add parent directory to path
sys.path.append(str(Path(__file__).parent.parent.parent))

try:
    import psutil
    from scapy.all import *
except ImportError as e:
    print(f"❌ Import error: {e}")
    print("💡 Cài đặt: pip install psutil scapy")
    sys.exit(1)

import config
from traffic_analyzer.packet_capture import PacketCapture
from traffic_analyzer.feature_extractor import FeatureExtractor
from traffic_analyzer.protocol_analyzer import ProtocolAnalyzer


class NetworkMonitor:
    """Class monitor network traffic real-time"""
    
    def __init__(self, interface=None):
        """
        Khởi tạo NetworkMonitor
        
        Args:
            interface (str): Network interface to monitor
        """
        self.interface = interface or config.NETWORK_CONFIG['default_interface']
        self.is_monitoring = False
        self.logger = logging.getLogger(__name__)
        
        # Components
        self.packet_capture = PacketCapture(self.interface)
        self.feature_extractor = FeatureExtractor()
        self.protocol_analyzer = ProtocolAnalyzer()
        
        # Statistics
        self.stats = {
            'start_time': None,
            'total_packets': 0,
            'total_bytes': 0,
            'packets_per_second': 0,
            'bytes_per_second': 0,
            'protocol_distribution': defaultdict(int),
            'top_talkers': defaultdict(int),
            'port_activity': defaultdict(int),
            'alerts': []
        }
        
        # Real-time data storage
        self.packet_buffer = deque(maxlen=1000)  # Last 1000 packets
        self.time_series_data = {
            'timestamps': deque(maxlen=3600),  # Last hour
            'packet_counts': deque(maxlen=3600),
            'byte_counts': deque(maxlen=3600),
            'protocol_counts': deque(maxlen=3600)
        }
        
        # Threading
        self.monitor_thread = None
        self.stats_thread = None
        self.stop_event = threading.Event()
        
        # Callbacks for real-time updates
        self.update_callbacks = []
        
    def add_update_callback(self, callback):
        """
        Thêm callback để nhận real-time updates
        
        Args:
            callback (function): Function nhận stats làm parameter
        """
        self.update_callbacks.append(callback)
    
    def start_monitoring(self):
        """Bắt đầu monitoring"""
        if self.is_monitoring:
            self.logger.warning("Monitoring đã đang chạy")
            return
        
        self.logger.info(f"Bắt đầu monitoring interface: {self.interface}")
        
        # Reset statistics
        self.stats['start_time'] = datetime.now()
        self.stats['total_packets'] = 0
        self.stats['total_bytes'] = 0
        self.stats['alerts'] = []
        
        # Clear buffers
        self.packet_buffer.clear()
        for key in self.time_series_data:
            self.time_series_data[key].clear()
        
        self.is_monitoring = True
        self.stop_event.clear()
        
        # Setup packet callback
        self.packet_capture.add_packet_callback(self._packet_callback)
        
        # Start monitoring threads
        self.monitor_thread = threading.Thread(target=self._monitor_worker)
        self.stats_thread = threading.Thread(target=self._stats_worker)
        
        self.monitor_thread.daemon = True
        self.stats_thread.daemon = True
        
        self.monitor_thread.start()
        self.stats_thread.start()
        
        # Start packet capture
        self.packet_capture.start_capture()
    
    def stop_monitoring(self):
        """Dừng monitoring"""
        if not self.is_monitoring:
            self.logger.warning("Monitoring không đang chạy")
            return
        
        self.logger.info("Đang dừng monitoring...")
        
        # Stop packet capture
        self.packet_capture.stop_capture()
        
        # Signal threads to stop
        self.stop_event.set()
        
        # Wait for threads to finish
        if self.monitor_thread and self.monitor_thread.is_alive():
            self.monitor_thread.join(timeout=5)
        
        if self.stats_thread and self.stats_thread.is_alive():
            self.stats_thread.join(timeout=5)
        
        self.is_monitoring = False
        self.logger.info("Monitoring đã dừng")
    
    def _packet_callback(self, packet):
        """Callback xử lý mỗi packet"""
        try:
            # Add to buffer
            self.packet_buffer.append(packet)
            
            # Update basic stats
            self.stats['total_packets'] += 1
            self.stats['total_bytes'] += len(packet)
            
            # Protocol analysis
            if packet.haslayer(IP):
                proto = packet[IP].proto
                if proto == 1:
                    self.stats['protocol_distribution']['ICMP'] += 1
                elif proto == 6:
                    self.stats['protocol_distribution']['TCP'] += 1
                elif proto == 17:
                    self.stats['protocol_distribution']['UDP'] += 1
                else:
                    self.stats['protocol_distribution'][f'PROTO_{proto}'] += 1
                
                # Top talkers
                src_ip = packet[IP].src
                dst_ip = packet[IP].dst
                self.stats['top_talkers'][src_ip] += 1
                self.stats['top_talkers'][dst_ip] += 1
                
                # Port activity
                if packet.haslayer(TCP):
                    self.stats['port_activity'][packet[TCP].dport] += 1
                elif packet.haslayer(UDP):
                    self.stats['port_activity'][packet[UDP].dport] += 1
            
        except Exception as e:
            self.logger.error(f"Lỗi trong packet callback: {e}")
    
    def _monitor_worker(self):
        """Worker thread cho monitoring logic"""
        while not self.stop_event.is_set():
            try:
                # Analyze recent packets
                if len(self.packet_buffer) >= 100:  # Analyze every 100 packets
                    recent_packets = list(self.packet_buffer)[-100:]
                    self._analyze_packets(recent_packets)
                
                time.sleep(1)  # Check every second
                
            except Exception as e:
                self.logger.error(f"Lỗi trong monitor worker: {e}")
    
    def _stats_worker(self):
        """Worker thread cho statistics updates"""
        last_packet_count = 0
        last_byte_count = 0
        last_time = time.time()
        
        while not self.stop_event.is_set():
            try:
                current_time = time.time()
                current_packets = self.stats['total_packets']
                current_bytes = self.stats['total_bytes']
                
                # Calculate rates
                time_diff = current_time - last_time
                if time_diff > 0:
                    self.stats['packets_per_second'] = (current_packets - last_packet_count) / time_diff
                    self.stats['bytes_per_second'] = (current_bytes - last_byte_count) / time_diff
                
                # Update time series data
                self.time_series_data['timestamps'].append(datetime.now())
                self.time_series_data['packet_counts'].append(current_packets)
                self.time_series_data['byte_counts'].append(current_bytes)
                self.time_series_data['protocol_counts'].append(dict(self.stats['protocol_distribution']))
                
                # Call update callbacks
                for callback in self.update_callbacks:
                    try:
                        callback(self.get_current_stats())
                    except Exception as e:
                        self.logger.error(f"Lỗi trong update callback: {e}")
                
                # Update for next iteration
                last_packet_count = current_packets
                last_byte_count = current_bytes
                last_time = current_time
                
                time.sleep(config.MONITORING_CONFIG['update_interval'])
                
            except Exception as e:
                self.logger.error(f"Lỗi trong stats worker: {e}")
    
    def _analyze_packets(self, packets):
        """Phân tích packets để detect anomalies"""
        try:
            # Protocol analysis
            protocol_report = self.protocol_analyzer.generate_protocol_report(packets)
            
            # Check for suspicious activities
            self._check_suspicious_activities(protocol_report)
            
            # Extract features for ML analysis (if models are loaded)
            # This would integrate with the ML models later
            
        except Exception as e:
            self.logger.error(f"Lỗi khi phân tích packets: {e}")
    
    def _check_suspicious_activities(self, protocol_report):
        """Kiểm tra các hoạt động đáng nghi"""
        current_time = datetime.now()
        
        # Check HTTP suspicious requests
        http_analysis = protocol_report.get('http_analysis', {})
        if http_analysis.get('suspicious_requests'):
            for req in http_analysis['suspicious_requests']:
                alert = {
                    'timestamp': current_time,
                    'type': 'HTTP_SUSPICIOUS',
                    'severity': 'HIGH',
                    'source_ip': req['src_ip'],
                    'details': f"Suspicious HTTP request: {req['reason']}",
                    'raw_data': req
                }
                self.stats['alerts'].append(alert)
        
        # Check DNS suspicious domains
        dns_analysis = protocol_report.get('dns_analysis', {})
        if dns_analysis.get('suspicious_domains'):
            for domain in dns_analysis['suspicious_domains']:
                alert = {
                    'timestamp': current_time,
                    'type': 'DNS_SUSPICIOUS',
                    'severity': 'MEDIUM',
                    'source_ip': domain['src_ip'],
                    'details': f"Suspicious DNS query: {domain['domain']}",
                    'raw_data': domain
                }
                self.stats['alerts'].append(alert)
        
        # Check TCP port scanning
        tcp_analysis = protocol_report.get('tcp_analysis', {})
        if tcp_analysis.get('port_scans'):
            for scan in tcp_analysis['port_scans']:
                alert = {
                    'timestamp': current_time,
                    'type': 'PORT_SCAN',
                    'severity': 'HIGH',
                    'source_ip': scan['src_ip'],
                    'details': f"Port scan detected: {scan['unique_ports']} ports",
                    'raw_data': scan
                }
                self.stats['alerts'].append(alert)
        
        # Limit alerts to prevent memory issues
        if len(self.stats['alerts']) > 1000:
            self.stats['alerts'] = self.stats['alerts'][-500:]
    
    def get_current_stats(self):
        """
        Lấy statistics hiện tại
        
        Returns:
            dict: Current statistics
        """
        stats = dict(self.stats)
        
        # Add uptime
        if stats['start_time']:
            uptime = datetime.now() - stats['start_time']
            stats['uptime_seconds'] = uptime.total_seconds()
        else:
            stats['uptime_seconds'] = 0
        
        # Add top talkers (top 10)
        top_talkers = sorted(
            self.stats['top_talkers'].items(),
            key=lambda x: x[1],
            reverse=True
        )[:10]
        stats['top_talkers'] = dict(top_talkers)
        
        # Add top ports (top 10)
        top_ports = sorted(
            self.stats['port_activity'].items(),
            key=lambda x: x[1],
            reverse=True
        )[:10]
        stats['top_ports'] = dict(top_ports)
        
        # Add recent alerts (last 50)
        stats['recent_alerts'] = self.stats['alerts'][-50:]
        
        return stats
    
    def get_time_series_data(self, duration_minutes=60):
        """
        Lấy time series data
        
        Args:
            duration_minutes (int): Duration in minutes
            
        Returns:
            dict: Time series data
        """
        cutoff_time = datetime.now() - timedelta(minutes=duration_minutes)
        
        # Filter data by time
        filtered_data = {
            'timestamps': [],
            'packet_counts': [],
            'byte_counts': [],
            'protocol_counts': []
        }
        
        for i, timestamp in enumerate(self.time_series_data['timestamps']):
            if timestamp >= cutoff_time:
                filtered_data['timestamps'].append(timestamp)
                filtered_data['packet_counts'].append(self.time_series_data['packet_counts'][i])
                filtered_data['byte_counts'].append(self.time_series_data['byte_counts'][i])
                filtered_data['protocol_counts'].append(self.time_series_data['protocol_counts'][i])
        
        return filtered_data
    
    def export_stats(self, filename):
        """
        Export statistics to JSON file
        
        Args:
            filename (str): Output filename
        """
        try:
            stats = self.get_current_stats()
            
            # Convert datetime objects to strings
            for alert in stats.get('recent_alerts', []):
                if 'timestamp' in alert:
                    alert['timestamp'] = alert['timestamp'].isoformat()
            
            if 'start_time' in stats and stats['start_time']:
                stats['start_time'] = stats['start_time'].isoformat()
            
            # Save to file
            output_path = config.LOGS_DIR / filename
            with open(output_path, 'w', encoding='utf-8') as f:
                json.dump(stats, f, indent=2, ensure_ascii=False)
            
            self.logger.info(f"Statistics exported to: {output_path}")
            
        except Exception as e:
            self.logger.error(f"Lỗi khi export statistics: {e}")


def main():
    """Main function để test network monitor"""
    import argparse
    
    parser = argparse.ArgumentParser(description='Network Traffic Monitor')
    parser.add_argument('--interface', '-i', type=str, help='Network interface')
    parser.add_argument('--duration', '-d', type=int, default=60, help='Monitor duration (seconds)')
    
    args = parser.parse_args()
    
    # Setup logging
    logging.basicConfig(level=logging.INFO,
                       format='%(asctime)s - %(levelname)s - %(message)s')
    
    # Create directories
    config.create_directories()
    
    # Initialize monitor
    monitor = NetworkMonitor(args.interface)
    
    # Add callback to print stats
    def print_stats(stats):
        print(f"\r📊 Packets: {stats['total_packets']}, "
              f"Rate: {stats['packets_per_second']:.1f} pps, "
              f"Alerts: {len(stats['recent_alerts'])}", end='')
    
    monitor.add_update_callback(print_stats)
    
    try:
        # Start monitoring
        print(f"🚀 Bắt đầu monitoring interface: {monitor.interface}")
        monitor.start_monitoring()
        
        # Monitor for specified duration
        time.sleep(args.duration)
        
        # Stop monitoring
        monitor.stop_monitoring()
        
        # Show final stats
        final_stats = monitor.get_current_stats()
        print(f"\n\n📈 Final Statistics:")
        print(f"  Total packets: {final_stats['total_packets']}")
        print(f"  Total bytes: {final_stats['total_bytes']}")
        print(f"  Uptime: {final_stats['uptime_seconds']:.1f} seconds")
        print(f"  Alerts: {len(final_stats['recent_alerts'])}")
        
        # Export stats
        monitor.export_stats('monitor_stats.json')
        
    except KeyboardInterrupt:
        print(f"\n⏹️  Monitoring interrupted by user")
        monitor.stop_monitoring()
    except Exception as e:
        print(f"💥 Error: {e}")


if __name__ == "__main__":
    main()
