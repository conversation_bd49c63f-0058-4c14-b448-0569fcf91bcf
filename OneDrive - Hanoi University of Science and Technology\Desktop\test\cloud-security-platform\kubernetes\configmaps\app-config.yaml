apiVersion: v1
kind: ConfigMap
metadata:
  name: malware-detection-config
  namespace: security-platform
  labels:
    app: malware-detection
    component: config
data:
  FLASK_ENV: "production"
  FLASK_HOST: "0.0.0.0"
  FLASK_PORT: "5000"
  FLASK_DEBUG: "false"
  LOG_LEVEL: "INFO"
  MAX_CONTENT_LENGTH: "104857600"  # 100MB
  UPLOAD_FOLDER: "/app/data/uploads"
  MODEL_PATH: "/app/data/models"
  ALLOWED_EXTENSIONS: "exe,dll,bin,com,scr,pif,bat,cmd"
---
apiVersion: v1
kind: ConfigMap
metadata:
  name: network-ids-config
  namespace: security-platform
  labels:
    app: network-ids
    component: config
data:
  FLASK_ENV: "production"
  FLASK_HOST: "0.0.0.0"
  FLASK_PORT: "5001"
  FLASK_DEBUG: "false"
  LOG_LEVEL: "INFO"
  NETWORK_INTERFACE: "eth0"
  MONITORING_ENABLED: "true"
  REAL_TIME_DETECTION: "true"
  MODEL_PATH: "/app/data/models"
  PCAP_PATH: "/app/data/pcap"
---
apiVersion: v1
kind: ConfigMap
metadata:
  name: dashboard-config
  namespace: security-platform
  labels:
    app: security-dashboard
    component: config
data:
  FLASK_ENV: "production"
  FLASK_HOST: "0.0.0.0"
  FLASK_PORT: "8080"
  FLASK_DEBUG: "false"
  LOG_LEVEL: "INFO"
  MALWARE_API_URL: "http://malware-detection-service:5000"
  IDS_API_URL: "http://network-ids-service:5001"
  REQUEST_TIMEOUT: "10"
---
apiVersion: v1
kind: ConfigMap
metadata:
  name: shared-config
  namespace: security-platform
  labels:
    component: shared-config
data:
  # Database configuration
  DB_HOST: "127.0.0.1"  # Will be overridden by Cloud SQL proxy
  DB_PORT: "5432"
  DB_NAME: "security_platform"
  DB_USER: "security_user"
  
  # Redis configuration
  REDIS_HOST: "redis-service"
  REDIS_PORT: "6379"
  REDIS_DB: "0"
  
  # Monitoring configuration
  PROMETHEUS_ENABLED: "true"
  METRICS_PORT: "9090"
  
  # Cloud configuration
  GCP_PROJECT_ID: "security-platform-prod"
  GCP_REGION: "asia-southeast1"
  
  # Storage configuration
  MODELS_BUCKET: "security-platform-models"
  LOGS_BUCKET: "security-platform-logs"
  
  # Security configuration
  ENABLE_CORS: "true"
  CORS_ORIGINS: "*"
  
  # Feature flags
  FEATURE_MALWARE_DETECTION: "true"
  FEATURE_NETWORK_IDS: "true"
  FEATURE_INTEGRATED_DASHBOARD: "true"
  FEATURE_THREAT_INTELLIGENCE: "true"
---
apiVersion: v1
kind: ConfigMap
metadata:
  name: nginx-config
  namespace: security-platform
  labels:
    app: api-gateway
    component: config
data:
  nginx.conf: |
    events {
        worker_connections 1024;
    }
    
    http {
        include       /etc/nginx/mime.types;
        default_type  application/octet-stream;
        
        log_format main '$remote_addr - $remote_user [$time_local] "$request" '
                        '$status $body_bytes_sent "$http_referer" '
                        '"$http_user_agent" "$http_x_forwarded_for"';
        
        access_log /var/log/nginx/access.log main;
        error_log /var/log/nginx/error.log warn;
        
        sendfile on;
        tcp_nopush on;
        tcp_nodelay on;
        keepalive_timeout 65;
        types_hash_max_size 2048;
        
        # Rate limiting
        limit_req_zone $binary_remote_addr zone=api:10m rate=10r/s;
        
        # Upstream services
        upstream malware-detection {
            server malware-detection-service:5000;
        }
        
        upstream network-ids {
            server network-ids-service:5001;
        }
        
        upstream dashboard {
            server dashboard-service:8080;
        }
        
        server {
            listen 80;
            server_name _;
            
            # Security headers
            add_header X-Frame-Options DENY;
            add_header X-Content-Type-Options nosniff;
            add_header X-XSS-Protection "1; mode=block";
            
            # Health check endpoint
            location /health {
                access_log off;
                return 200 "healthy\n";
                add_header Content-Type text/plain;
            }
            
            # API routes with rate limiting
            location /api/malware/ {
                limit_req zone=api burst=20 nodelay;
                proxy_pass http://malware-detection/api/;
                proxy_set_header Host $host;
                proxy_set_header X-Real-IP $remote_addr;
                proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
                proxy_set_header X-Forwarded-Proto $scheme;
                proxy_connect_timeout 30s;
                proxy_send_timeout 30s;
                proxy_read_timeout 30s;
            }
            
            location /api/ids/ {
                limit_req zone=api burst=20 nodelay;
                proxy_pass http://network-ids/api/;
                proxy_set_header Host $host;
                proxy_set_header X-Real-IP $remote_addr;
                proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
                proxy_set_header X-Forwarded-Proto $scheme;
                proxy_connect_timeout 30s;
                proxy_send_timeout 30s;
                proxy_read_timeout 30s;
            }
            
            # Dashboard routes
            location / {
                proxy_pass http://dashboard/;
                proxy_set_header Host $host;
                proxy_set_header X-Real-IP $remote_addr;
                proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
                proxy_set_header X-Forwarded-Proto $scheme;
                proxy_connect_timeout 30s;
                proxy_send_timeout 30s;
                proxy_read_timeout 30s;
            }
        }
    }
