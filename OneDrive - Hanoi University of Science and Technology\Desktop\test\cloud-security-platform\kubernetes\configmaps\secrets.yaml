# Kubernetes Secrets for Security Platform
# Note: In production, use external secret management like Google Secret Manager

apiVersion: v1
kind: Secret
metadata:
  name: database-secrets
  namespace: security-platform
  labels:
    app: security-platform
    component: database
type: Opaque
data:
  # Base64 encoded values - replace with actual values
  # echo -n "your-password" | base64
  DB_PASSWORD: c2VjdXJpdHktcGFzc3dvcmQ=  # security-password
  DB_ROOT_PASSWORD: cm9vdC1wYXNzd29yZA==  # root-password
---
apiVersion: v1
kind: Secret
metadata:
  name: app-secrets
  namespace: security-platform
  labels:
    app: security-platform
    component: application
type: Opaque
data:
  # Flask secret keys
  MALWARE_SECRET_KEY: bWFsd2FyZS1zZWNyZXQta2V5LWNoYW5nZS1pbi1wcm9kdWN0aW9u  # malware-secret-key-change-in-production
  IDS_SECRET_KEY: aWRzLXNlY3JldC1rZXktY2hhbmdlLWluLXByb2R1Y3Rpb24=  # ids-secret-key-change-in-production
  DASHBOARD_SECRET_KEY: ZGFzaGJvYXJkLXNlY3JldC1rZXktY2hhbmdlLWluLXByb2R1Y3Rpb24=  # dashboard-secret-key-change-in-production
  
  # API keys
  API_KEY: YXBpLWtleS1jaGFuZ2UtaW4tcHJvZHVjdGlvbg==  # api-key-change-in-production
  
  # JWT secret
  JWT_SECRET: and0LXNlY3JldC1jaGFuZ2UtaW4tcHJvZHVjdGlvbg==  # jwt-secret-change-in-production
---
apiVersion: v1
kind: Secret
metadata:
  name: cloud-secrets
  namespace: security-platform
  labels:
    app: security-platform
    component: cloud
type: Opaque
data:
  # Google Cloud Service Account Key (JSON)
  # Replace with actual service account key
  GCP_SERVICE_ACCOUNT_KEY: ewogICJ0eXBlIjogInNlcnZpY2VfYWNjb3VudCIsCiAgInByb2plY3RfaWQiOiAic2VjdXJpdHktcGxhdGZvcm0tcHJvZCIKfQ==
  
  # Cloud Storage credentials
  STORAGE_ACCESS_KEY: c3RvcmFnZS1hY2Nlc3Mta2V5
  STORAGE_SECRET_KEY: c3RvcmFnZS1zZWNyZXQta2V5
---
apiVersion: v1
kind: Secret
metadata:
  name: monitoring-secrets
  namespace: security-platform
  labels:
    app: security-platform
    component: monitoring
type: Opaque
data:
  # Grafana admin password
  GRAFANA_ADMIN_PASSWORD: YWRtaW4xMjM=  # admin123
  
  # Prometheus basic auth
  PROMETHEUS_USERNAME: cHJvbWV0aGV1cw==  # prometheus
  PROMETHEUS_PASSWORD: cHJvbS1wYXNzd29yZA==  # prom-password
  
  # Alertmanager webhook URLs
  SLACK_WEBHOOK_URL: aHR0cHM6Ly9ob29rcy5zbGFjay5jb20vc2VydmljZXMvLi4u  # https://hooks.slack.com/services/...
---
# Service Account for Cloud SQL Proxy
apiVersion: v1
kind: Secret
metadata:
  name: cloudsql-instance-credentials
  namespace: security-platform
  labels:
    app: security-platform
    component: database
type: Opaque
data:
  # Service account key for Cloud SQL proxy
  # This should be the JSON key file for a service account with Cloud SQL Client role
  credentials.json: ****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
