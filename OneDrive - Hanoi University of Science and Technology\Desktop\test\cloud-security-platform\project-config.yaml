# Cloud Security Platform Configuration
# <PERSON><PERSON><PERSON> hình chung cho toàn bộ platform

project:
  name: "cloud-security-platform"
  version: "1.0.0"
  description: "Integrated AI Malware Detection and Network IDS on Google Cloud"
  
# Google Cloud Platform Configuration
gcp:
  project_id: "security-platform-prod"  # Change this to your GCP project ID
  region: "asia-southeast1"
  zone: "asia-southeast1-a"
  
  # Alternative regions for multi-region deployment
  regions:
    primary: "asia-southeast1"
    secondary: "asia-northeast1"
    
  # Network configuration
  network:
    vpc_name: "security-platform-vpc"
    subnet_name: "security-platform-subnet"
    subnet_cidr: "10.0.0.0/16"
    
  # GKE Cluster configuration
  gke:
    cluster_name: "security-platform-cluster"
    node_pool_name: "security-platform-nodes"
    machine_type: "e2-standard-4"
    min_nodes: 2
    max_nodes: 10
    disk_size: 100
    
  # Cloud SQL configuration
  cloudsql:
    instance_name: "security-platform-db"
    database_version: "POSTGRES_14"
    tier: "db-custom-2-4096"  # 2 vCPU, 4GB RAM
    
  # Cloud Storage configuration
  storage:
    models_bucket: "security-platform-models"
    logs_bucket: "security-platform-logs"
    backups_bucket: "security-platform-backups"

# Application Services Configuration
services:
  # AI Malware Detection Service
  malware_detection:
    name: "malware-detection"
    port: 5000
    replicas: 3
    resources:
      requests:
        cpu: "500m"
        memory: "1Gi"
      limits:
        cpu: "2000m"
        memory: "4Gi"
    health_check:
      path: "/api/health"
      port: 5000
      
  # Network IDS Service
  network_ids:
    name: "network-ids"
    port: 5001
    replicas: 3
    resources:
      requests:
        cpu: "500m"
        memory: "1Gi"
      limits:
        cpu: "2000m"
        memory: "4Gi"
    health_check:
      path: "/api/status"
      port: 5001
      
  # Integrated Dashboard
  dashboard:
    name: "security-dashboard"
    port: 8080
    replicas: 2
    resources:
      requests:
        cpu: "200m"
        memory: "512Mi"
      limits:
        cpu: "1000m"
        memory: "2Gi"
    health_check:
      path: "/health"
      port: 8080
      
  # API Gateway
  api_gateway:
    name: "api-gateway"
    port: 80
    replicas: 2
    resources:
      requests:
        cpu: "200m"
        memory: "512Mi"
      limits:
        cpu: "1000m"
        memory: "2Gi"

# Database Configuration
database:
  host: "127.0.0.1"  # Will be overridden by Cloud SQL proxy
  port: 5432
  name: "security_platform"
  username: "security_user"
  # Password will be stored in Kubernetes secrets
  
  # Connection pool settings
  pool:
    min_connections: 5
    max_connections: 20
    
# Redis Configuration (for caching)
redis:
  host: "redis-service"
  port: 6379
  database: 0
  
# Monitoring Configuration
monitoring:
  prometheus:
    enabled: true
    retention: "30d"
    storage_size: "50Gi"
    
  grafana:
    enabled: true
    admin_password: "admin123"  # Change in production
    
  alertmanager:
    enabled: true
    slack_webhook: ""  # Configure for Slack notifications
    
# Logging Configuration
logging:
  level: "INFO"
  format: "json"
  
  # Cloud Logging configuration
  cloud_logging:
    enabled: true
    log_level: "INFO"
    
  # Log retention
  retention:
    application_logs: "30d"
    audit_logs: "90d"
    security_logs: "180d"

# Security Configuration
security:
  # TLS/SSL Configuration
  tls:
    enabled: true
    cert_manager: true
    issuer: "letsencrypt-prod"
    
  # Network Policies
  network_policies:
    enabled: true
    default_deny: true
    
  # Pod Security Standards
  pod_security:
    enforce: "restricted"
    audit: "restricted"
    warn: "restricted"
    
  # RBAC Configuration
  rbac:
    enabled: true
    
# Backup Configuration
backup:
  enabled: true
  schedule: "0 2 * * *"  # Daily at 2 AM
  retention: "30d"
  
  # Database backup
  database:
    enabled: true
    schedule: "0 1 * * *"  # Daily at 1 AM
    
  # Model backup
  models:
    enabled: true
    schedule: "0 3 * * 0"  # Weekly on Sunday at 3 AM

# Auto-scaling Configuration
autoscaling:
  # Horizontal Pod Autoscaler
  hpa:
    enabled: true
    min_replicas: 2
    max_replicas: 10
    target_cpu: 70
    target_memory: 80
    
  # Vertical Pod Autoscaler
  vpa:
    enabled: true
    update_mode: "Auto"
    
  # Cluster Autoscaler
  cluster_autoscaler:
    enabled: true
    min_nodes: 2
    max_nodes: 20

# CI/CD Configuration
cicd:
  # Cloud Build configuration
  cloud_build:
    enabled: true
    trigger_branch: "main"
    
  # Container Registry
  registry:
    hostname: "gcr.io"
    project_id: "${gcp.project_id}"
    
  # Deployment strategy
  deployment:
    strategy: "rolling"  # rolling, blue-green, canary
    max_unavailable: "25%"
    max_surge: "25%"

# Feature Flags
features:
  malware_detection: true
  network_ids: true
  integrated_dashboard: true
  threat_intelligence: true
  real_time_monitoring: true
  auto_scaling: true
  backup: true
  monitoring: true

# Environment-specific overrides
environments:
  development:
    gke:
      min_nodes: 1
      max_nodes: 3
      machine_type: "e2-standard-2"
    services:
      malware_detection:
        replicas: 1
      network_ids:
        replicas: 1
      dashboard:
        replicas: 1
        
  staging:
    gke:
      min_nodes: 2
      max_nodes: 5
      machine_type: "e2-standard-2"
    services:
      malware_detection:
        replicas: 2
      network_ids:
        replicas: 2
      dashboard:
        replicas: 1
        
  production:
    gke:
      min_nodes: 3
      max_nodes: 15
      machine_type: "e2-standard-4"
    services:
      malware_detection:
        replicas: 3
      network_ids:
        replicas: 3
      dashboard:
        replicas: 2

# Cost Optimization
cost_optimization:
  # Preemptible nodes for non-critical workloads
  preemptible_nodes:
    enabled: true
    percentage: 50
    
  # Committed use discounts
  committed_use:
    enabled: true
    term: "1y"  # 1 year commitment
    
  # Resource quotas
  resource_quotas:
    enabled: true
    cpu_limit: "100"
    memory_limit: "200Gi"
    storage_limit: "1Ti"
