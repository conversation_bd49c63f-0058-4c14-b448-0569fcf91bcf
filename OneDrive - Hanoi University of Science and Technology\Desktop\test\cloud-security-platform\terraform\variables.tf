# Terraform Variables for Cloud Security Platform

# Project Configuration
variable "project_id" {
  description = "GCP Project ID"
  type        = string
  default     = "security-platform-prod"
}

variable "project_name" {
  description = "Project name used for resource naming"
  type        = string
  default     = "security-platform"
}

variable "environment" {
  description = "Environment (development, staging, production)"
  type        = string
  default     = "production"
  
  validation {
    condition     = contains(["development", "staging", "production"], var.environment)
    error_message = "Environment must be one of: development, staging, production."
  }
}

# Regional Configuration
variable "region" {
  description = "GCP region"
  type        = string
  default     = "asia-southeast1"
}

variable "zone" {
  description = "GCP zone"
  type        = string
  default     = "asia-southeast1-a"
}

# Network Configuration
variable "subnet_cidr" {
  description = "CIDR block for the subnet"
  type        = string
  default     = "10.0.0.0/16"
}

# GKE Configuration
variable "gke_num_nodes" {
  description = "Number of GKE nodes per zone"
  type        = number
  default     = 2
}

variable "gke_min_nodes" {
  description = "Minimum number of GKE nodes per zone"
  type        = number
  default     = 1
}

variable "gke_max_nodes" {
  description = "Maximum number of GKE nodes per zone"
  type        = number
  default     = 10
}

variable "gke_machine_type" {
  description = "Machine type for GKE nodes"
  type        = string
  default     = "e2-standard-4"
}

variable "gke_disk_size" {
  description = "Disk size for GKE nodes (GB)"
  type        = number
  default     = 100
}

variable "gke_preemptible" {
  description = "Use preemptible instances for GKE nodes"
  type        = bool
  default     = false
}

# Database Configuration
variable "db_tier" {
  description = "Cloud SQL instance tier"
  type        = string
  default     = "db-custom-2-4096"
}

variable "db_disk_size" {
  description = "Cloud SQL disk size (GB)"
  type        = number
  default     = 20
}

variable "db_name" {
  description = "Database name"
  type        = string
  default     = "security_platform"
}

variable "db_user" {
  description = "Database user"
  type        = string
  default     = "security_user"
}

# Application Configuration
variable "malware_detection_replicas" {
  description = "Number of malware detection service replicas"
  type        = number
  default     = 3
}

variable "network_ids_replicas" {
  description = "Number of network IDS service replicas"
  type        = number
  default     = 3
}

variable "dashboard_replicas" {
  description = "Number of dashboard service replicas"
  type        = number
  default     = 2
}

# Container Registry Configuration
variable "container_registry_location" {
  description = "Container Registry location"
  type        = string
  default     = "asia"
}

# Monitoring Configuration
variable "enable_monitoring" {
  description = "Enable monitoring stack (Prometheus, Grafana)"
  type        = bool
  default     = true
}

variable "monitoring_retention_days" {
  description = "Monitoring data retention in days"
  type        = number
  default     = 30
}

# Backup Configuration
variable "backup_retention_days" {
  description = "Backup retention in days"
  type        = number
  default     = 30
}

# Security Configuration
variable "enable_network_policy" {
  description = "Enable Kubernetes network policies"
  type        = bool
  default     = true
}

variable "enable_pod_security_policy" {
  description = "Enable Pod Security Policy"
  type        = bool
  default     = true
}

variable "authorized_networks" {
  description = "List of authorized networks for GKE master"
  type = list(object({
    cidr_block   = string
    display_name = string
  }))
  default = [
    {
      cidr_block   = "0.0.0.0/0"
      display_name = "All networks"
    }
  ]
}

# Cost Optimization
variable "enable_preemptible_nodes" {
  description = "Enable preemptible nodes for cost optimization"
  type        = bool
  default     = false
}

variable "preemptible_percentage" {
  description = "Percentage of preemptible nodes (0-100)"
  type        = number
  default     = 50
  
  validation {
    condition     = var.preemptible_percentage >= 0 && var.preemptible_percentage <= 100
    error_message = "Preemptible percentage must be between 0 and 100."
  }
}

# Feature Flags
variable "enable_malware_detection" {
  description = "Enable malware detection service"
  type        = bool
  default     = true
}

variable "enable_network_ids" {
  description = "Enable network IDS service"
  type        = bool
  default     = true
}

variable "enable_integrated_dashboard" {
  description = "Enable integrated dashboard"
  type        = bool
  default     = true
}

variable "enable_threat_intelligence" {
  description = "Enable threat intelligence features"
  type        = bool
  default     = true
}

# SSL/TLS Configuration
variable "ssl_certificate_domains" {
  description = "List of domains for SSL certificate"
  type        = list(string)
  default     = []
}

variable "enable_https_redirect" {
  description = "Enable HTTPS redirect"
  type        = bool
  default     = true
}

# Scaling Configuration
variable "enable_horizontal_pod_autoscaler" {
  description = "Enable Horizontal Pod Autoscaler"
  type        = bool
  default     = true
}

variable "enable_vertical_pod_autoscaler" {
  description = "Enable Vertical Pod Autoscaler"
  type        = bool
  default     = true
}

variable "enable_cluster_autoscaler" {
  description = "Enable Cluster Autoscaler"
  type        = bool
  default     = true
}

# Resource Limits
variable "default_cpu_request" {
  description = "Default CPU request for containers"
  type        = string
  default     = "200m"
}

variable "default_memory_request" {
  description = "Default memory request for containers"
  type        = string
  default     = "512Mi"
}

variable "default_cpu_limit" {
  description = "Default CPU limit for containers"
  type        = string
  default     = "1000m"
}

variable "default_memory_limit" {
  description = "Default memory limit for containers"
  type        = string
  default     = "2Gi"
}

# Logging Configuration
variable "log_retention_days" {
  description = "Log retention in days"
  type        = number
  default     = 30
}

variable "enable_audit_logs" {
  description = "Enable audit logging"
  type        = bool
  default     = true
}

# Alerting Configuration
variable "alert_email_recipients" {
  description = "List of email addresses for alerts"
  type        = list(string)
  default     = []
}

variable "slack_webhook_url" {
  description = "Slack webhook URL for alerts"
  type        = string
  default     = ""
  sensitive   = true
}

# Maintenance Configuration
variable "maintenance_window_day" {
  description = "Day of week for maintenance (1=Monday, 7=Sunday)"
  type        = number
  default     = 7
  
  validation {
    condition     = var.maintenance_window_day >= 1 && var.maintenance_window_day <= 7
    error_message = "Maintenance window day must be between 1 (Monday) and 7 (Sunday)."
  }
}

variable "maintenance_window_hour" {
  description = "Hour of day for maintenance (0-23)"
  type        = number
  default     = 2
  
  validation {
    condition     = var.maintenance_window_hour >= 0 && var.maintenance_window_hour <= 23
    error_message = "Maintenance window hour must be between 0 and 23."
  }
}

# Tags and Labels
variable "additional_labels" {
  description = "Additional labels to apply to resources"
  type        = map(string)
  default     = {}
}

variable "additional_tags" {
  description = "Additional tags to apply to resources"
  type        = list(string)
  default     = []
}
