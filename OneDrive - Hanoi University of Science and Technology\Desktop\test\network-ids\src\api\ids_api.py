"""
IDS API Module
Flask API cho Network Intrusion Detection System
"""

import sys
import os
import json
from pathlib import Path
from datetime import datetime
from flask import Flask, request, jsonify, render_template
from flask_cors import CORS
import logging

# Add parent directory to path
sys.path.append(str(Path(__file__).parent.parent.parent))

import config
from monitoring.network_monitor import NetworkMonitor
from monitoring.alert_system import AlertManager
from monitoring.real_time_detector import RealTimeDetector
from ml_models.train_models import train_anomaly_model, train_intrusion_model, create_sample_dataset


# Initialize Flask app
app = Flask(__name__, 
           template_folder='../dashboard/templates',
           static_folder='../dashboard/static')
app.config.update(config.FLASK_CONFIG)
CORS(app)

# Global components
network_monitor = None
alert_manager = AlertManager()
real_time_detector = RealTimeDetector()

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


@app.route('/')
def index():
    """Main dashboard page"""
    return render_template('dashboard.html')


@app.route('/api/status')
def api_status():
    """Get system status"""
    try:
        status = {
            'status': 'online',
            'timestamp': datetime.now().isoformat(),
            'components': {
                'network_monitor': network_monitor is not None and network_monitor.is_monitoring,
                'real_time_detector': real_time_detector.is_detecting,
                'models_loaded': real_time_detector.get_detection_stats()['models_loaded']
            },
            'version': '1.0.0'
        }
        
        return jsonify(status)
        
    except Exception as e:
        logger.error(f"Error in api_status: {e}")
        return jsonify({'error': str(e)}), 500


@app.route('/api/monitoring/start', methods=['POST'])
def start_monitoring():
    """Start network monitoring"""
    global network_monitor
    
    try:
        data = request.get_json() or {}
        interface = data.get('interface', config.NETWORK_CONFIG['default_interface'])
        
        if network_monitor and network_monitor.is_monitoring:
            return jsonify({'error': 'Monitoring đã đang chạy'}), 400
        
        # Initialize network monitor
        network_monitor = NetworkMonitor(interface)
        
        # Add callback to feed packets to real-time detector
        network_monitor.add_update_callback(lambda stats: None)  # Placeholder
        
        # Start monitoring
        network_monitor.start_monitoring()
        real_time_detector.start_detection()
        
        return jsonify({
            'success': True,
            'message': f'Monitoring started on interface: {interface}',
            'timestamp': datetime.now().isoformat()
        })
        
    except Exception as e:
        logger.error(f"Error starting monitoring: {e}")
        return jsonify({'error': str(e)}), 500


@app.route('/api/monitoring/stop', methods=['POST'])
def stop_monitoring():
    """Stop network monitoring"""
    global network_monitor
    
    try:
        if not network_monitor or not network_monitor.is_monitoring:
            return jsonify({'error': 'Monitoring không đang chạy'}), 400
        
        # Stop components
        network_monitor.stop_monitoring()
        real_time_detector.stop_detection()
        
        return jsonify({
            'success': True,
            'message': 'Monitoring stopped',
            'timestamp': datetime.now().isoformat()
        })
        
    except Exception as e:
        logger.error(f"Error stopping monitoring: {e}")
        return jsonify({'error': str(e)}), 500


@app.route('/api/monitoring/stats')
def monitoring_stats():
    """Get monitoring statistics"""
    try:
        stats = {}
        
        # Network monitor stats
        if network_monitor:
            stats['network'] = network_monitor.get_current_stats()
        else:
            stats['network'] = {'status': 'not_running'}
        
        # Detection stats
        stats['detection'] = real_time_detector.get_detection_stats()
        
        # Alert stats
        stats['alerts'] = alert_manager.get_alert_statistics()
        
        return jsonify(stats)
        
    except Exception as e:
        logger.error(f"Error getting monitoring stats: {e}")
        return jsonify({'error': str(e)}), 500


@app.route('/api/alerts')
def get_alerts():
    """Get active alerts"""
    try:
        severity_filter = request.args.get('severity')
        limit = int(request.args.get('limit', 100))
        
        alerts = alert_manager.get_active_alerts(severity_filter, limit)
        
        # Convert datetime objects to strings
        for alert in alerts:
            alert['timestamp'] = alert['timestamp'].isoformat()
            if alert['acknowledged_at']:
                alert['acknowledged_at'] = alert['acknowledged_at'].isoformat()
        
        return jsonify({
            'alerts': alerts,
            'total': len(alerts),
            'timestamp': datetime.now().isoformat()
        })
        
    except Exception as e:
        logger.error(f"Error getting alerts: {e}")
        return jsonify({'error': str(e)}), 500


@app.route('/api/alerts/<alert_id>/acknowledge', methods=['POST'])
def acknowledge_alert(alert_id):
    """Acknowledge an alert"""
    try:
        data = request.get_json() or {}
        acknowledged_by = data.get('acknowledged_by', 'unknown')
        
        success = alert_manager.acknowledge_alert(alert_id, acknowledged_by)
        
        if success:
            return jsonify({
                'success': True,
                'message': f'Alert {alert_id} acknowledged',
                'timestamp': datetime.now().isoformat()
            })
        else:
            return jsonify({'error': 'Alert not found'}), 404
            
    except Exception as e:
        logger.error(f"Error acknowledging alert: {e}")
        return jsonify({'error': str(e)}), 500


@app.route('/api/interfaces')
def get_interfaces():
    """Get available network interfaces"""
    try:
        from traffic_analyzer.packet_capture import PacketCapture
        
        capture = PacketCapture()
        interfaces = capture.get_available_interfaces()
        
        return jsonify({
            'interfaces': interfaces,
            'default': config.NETWORK_CONFIG['default_interface']
        })
        
    except Exception as e:
        logger.error(f"Error getting interfaces: {e}")
        return jsonify({'error': str(e)}), 500


@app.route('/api/models/train', methods=['POST'])
def train_models():
    """Train ML models"""
    try:
        data = request.get_json() or {}
        model_type = data.get('model_type', 'both')  # 'anomaly', 'intrusion', 'both'
        algorithm = data.get('algorithm')
        
        # Create sample dataset if needed
        dataset_path = config.DATASETS_DIR / 'sample_network_data.csv'
        if not dataset_path.exists():
            create_sample_dataset()
        
        results = {}
        
        # Train anomaly model
        if model_type in ['anomaly', 'both']:
            algo = algorithm or 'isolation_forest'
            detector = train_anomaly_model(str(dataset_path), algo)
            if detector:
                results['anomaly'] = {
                    'success': True,
                    'algorithm': algo,
                    'message': 'Anomaly model trained successfully'
                }
            else:
                results['anomaly'] = {
                    'success': False,
                    'message': 'Anomaly model training failed'
                }
        
        # Train intrusion model
        if model_type in ['intrusion', 'both']:
            algo = algorithm or 'random_forest'
            classifier = train_intrusion_model(str(dataset_path), algo)
            if classifier:
                results['intrusion'] = {
                    'success': True,
                    'algorithm': algo,
                    'message': 'Intrusion model trained successfully'
                }
            else:
                results['intrusion'] = {
                    'success': False,
                    'message': 'Intrusion model training failed'
                }
        
        # Reload models in real-time detector
        global real_time_detector
        real_time_detector = RealTimeDetector()
        
        return jsonify({
            'success': True,
            'results': results,
            'timestamp': datetime.now().isoformat()
        })
        
    except Exception as e:
        logger.error(f"Error training models: {e}")
        return jsonify({'error': str(e)}), 500


@app.route('/api/models/status')
def models_status():
    """Get models status"""
    try:
        stats = real_time_detector.get_detection_stats()
        models_status = stats['models_loaded']
        
        # Check model files
        model_files = {
            'anomaly_model': config.MODEL_DIR / "isolation_forest_anomaly_model.joblib",
            'anomaly_scaler': config.MODEL_DIR / "isolation_forest_anomaly_scaler.joblib",
            'intrusion_model': config.MODEL_DIR / "random_forest_intrusion_model.joblib",
            'intrusion_scaler': config.MODEL_DIR / "random_forest_intrusion_scaler.joblib"
        }
        
        file_status = {}
        for name, path in model_files.items():
            file_status[name] = {
                'exists': path.exists(),
                'path': str(path),
                'size': path.stat().st_size if path.exists() else 0
            }
        
        return jsonify({
            'models_loaded': models_status,
            'model_files': file_status,
            'timestamp': datetime.now().isoformat()
        })
        
    except Exception as e:
        logger.error(f"Error getting models status: {e}")
        return jsonify({'error': str(e)}), 500


@app.route('/api/time-series')
def get_time_series():
    """Get time series data for charts"""
    try:
        duration = int(request.args.get('duration', 60))  # minutes
        
        data = {}
        
        # Network monitor time series
        if network_monitor:
            data['network'] = network_monitor.get_time_series_data(duration)
            
            # Convert datetime objects to strings
            if 'timestamps' in data['network']:
                data['network']['timestamps'] = [
                    ts.isoformat() for ts in data['network']['timestamps']
                ]
        
        return jsonify(data)
        
    except Exception as e:
        logger.error(f"Error getting time series data: {e}")
        return jsonify({'error': str(e)}), 500


@app.route('/api/export/alerts')
def export_alerts():
    """Export alerts to JSON"""
    try:
        hours = int(request.args.get('hours', 24))
        filename = f"alerts_export_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        
        alert_manager.export_alerts(filename, hours)
        
        return jsonify({
            'success': True,
            'filename': filename,
            'message': f'Alerts exported for last {hours} hours',
            'timestamp': datetime.now().isoformat()
        })
        
    except Exception as e:
        logger.error(f"Error exporting alerts: {e}")
        return jsonify({'error': str(e)}), 500


@app.errorhandler(404)
def not_found(error):
    """404 error handler"""
    return jsonify({
        'success': False,
        'error': 'Endpoint không tồn tại',
        'timestamp': datetime.now().isoformat()
    }), 404


@app.errorhandler(500)
def internal_error(error):
    """500 error handler"""
    return jsonify({
        'success': False,
        'error': 'Internal server error',
        'timestamp': datetime.now().isoformat()
    }), 500


def main():
    """Main function"""
    # Create directories
    config.create_directories()
    
    # Start Flask app
    logger.info(f"Starting IDS API server on {config.FLASK_CONFIG['HOST']}:{config.FLASK_CONFIG['PORT']}")
    
    app.run(
        host=config.FLASK_CONFIG['HOST'],
        port=config.FLASK_CONFIG['PORT'],
        debug=config.FLASK_CONFIG['DEBUG'],
        threaded=config.FLASK_CONFIG.get('THREADED', True)
    )


if __name__ == '__main__':
    main()
