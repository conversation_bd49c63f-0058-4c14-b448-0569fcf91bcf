"""
Demo Script
Script demo cho AI Malware Detection System
"""

import os
import sys
import time
from pathlib import Path
import argparse

# Add src to path
sys.path.append(str(Path(__file__).parent / 'src'))

from feature_extraction.pe_analyzer import PEAnalyzer
from feature_extraction.batch_processor import BatchProcessor
from models.classifier import MalwareClassifier
from models.train import create_sample_dataset, train_model
from api.database import DatabaseManager
import config


def print_banner():
    """In banner của hệ thống"""
    banner = """
    ╔══════════════════════════════════════════════════════════════╗
    ║                                                              ║
    ║        🤖 AI MALWARE DETECTION SYSTEM - DEMO 🤖              ║
    ║                                                              ║
    ║        Hệ thống phát hiện phần mềm độc hại bằng AI          ║
    ║        Sử dụng Machine Learning và Deep Learning             ║
    ║                                                              ║
    ╚══════════════════════════════════════════════════════════════╝
    """
    print(banner)


def demo_pe_analysis():
    """Demo phân tích file PE"""
    print("\n" + "="*60)
    print("🔍 DEMO: PHÂN TÍCH FILE PE")
    print("="*60)
    
    # Sample PE file
    sample_file = "C:\\Windows\\System32\\notepad.exe"
    
    if not os.path.exists(sample_file):
        print(f"❌ File mẫu không tồn tại: {sample_file}")
        print("💡 Hãy thay đổi đường dẫn trong demo.py")
        return
    
    print(f"📁 Phân tích file: {sample_file}")
    
    # Initialize analyzer
    analyzer = PEAnalyzer()
    
    # Analyze file
    print("⏳ Đang phân tích...")
    start_time = time.time()
    
    features = analyzer.analyze_file(sample_file)
    
    end_time = time.time()
    analysis_time = end_time - start_time
    
    if features:
        print(f"✅ Phân tích hoàn thành trong {analysis_time:.3f} giây")
        print(f"\n📊 Kết quả phân tích:")
        print(f"   • File size: {features['file_size']:,} bytes")
        print(f"   • MD5: {features['md5']}")
        print(f"   • SHA256: {features['sha256'][:32]}...")
        print(f"   • Is EXE: {features['is_exe']}")
        print(f"   • Is DLL: {features['is_dll']}")
        print(f"   • Sections: {features['number_of_sections']}")
        print(f"   • File entropy: {features['file_entropy']:.3f}")
        print(f"   • Import DLLs: {features['import_dll_count']}")
        print(f"   • Import functions: {features['import_function_count']}")
        print(f"   • Suspicious imports: {features['suspicious_import_count']}")
        print(f"   • Suspicious strings: {features['suspicious_string_count']}")
        
        # Feature vector
        feature_vector = analyzer.get_feature_vector()
        print(f"\n🔢 Feature vector ({len(feature_vector)} features):")
        feature_names = PEAnalyzer.get_feature_names()
        for i, (name, value) in enumerate(zip(feature_names[:5], feature_vector[:5])):
            print(f"   {i+1}. {name}: {value}")
        print(f"   ... và {len(feature_vector)-5} features khác")
        
    else:
        print("❌ Không thể phân tích file")


def demo_batch_processing():
    """Demo xử lý batch files"""
    print("\n" + "="*60)
    print("📦 DEMO: BATCH PROCESSING")
    print("="*60)
    
    # Create sample dataset
    print("📁 Tạo sample dataset...")
    benign_dir, malware_dir = create_sample_dataset()
    
    print(f"✅ Đã tạo sample dataset:")
    print(f"   • Benign samples: {benign_dir}")
    print(f"   • Malware samples: {malware_dir}")
    
    # Check if we have samples
    benign_files = list(Path(benign_dir).glob("*.exe"))
    malware_files = list(Path(malware_dir).glob("*.exe"))
    
    print(f"📊 Thống kê samples:")
    print(f"   • Benign files: {len(benign_files)}")
    print(f"   • Malware files: {len(malware_files)}")
    
    if len(benign_files) == 0:
        print("⚠️  Không có benign samples để demo batch processing")
        return
    
    # Process benign files
    print("\n⏳ Xử lý benign files...")
    processor = BatchProcessor(n_workers=2)
    
    start_time = time.time()
    benign_df = processor.process_directory(benign_dir, 'benign')
    end_time = time.time()
    
    if not benign_df.empty:
        print(f"✅ Đã xử lý {len(benign_df)} benign files trong {end_time-start_time:.3f} giây")
        print(f"📊 Dataset shape: {benign_df.shape}")
        print(f"🔍 Sample features:")
        for col in benign_df.columns[:5]:
            if col not in ['file_path', 'file_name', 'label', 'md5', 'sha256']:
                print(f"   • {col}: {benign_df[col].iloc[0]}")
    else:
        print("❌ Không thể xử lý files")


def demo_model_training():
    """Demo training model"""
    print("\n" + "="*60)
    print("🧠 DEMO: MODEL TRAINING")
    print("="*60)
    
    # Check if dataset exists
    dataset_path = "data/datasets/malware_dataset.csv"
    
    if not os.path.exists(dataset_path):
        print(f"❌ Dataset không tồn tại: {dataset_path}")
        print("💡 Chạy: python src/models/train.py --create-sample")
        print("💡 Sau đó thêm malware samples và chạy training")
        return
    
    print(f"📁 Sử dụng dataset: {dataset_path}")
    
    # Initialize classifier
    classifier = MalwareClassifier('random_forest')
    
    # Load dataset
    print("⏳ Loading dataset...")
    X, y = classifier.load_dataset(dataset_path)
    
    if X is None or y is None:
        print("❌ Không thể load dataset")
        return
    
    print(f"✅ Dataset loaded: {X.shape[0]} samples, {X.shape[1]} features")
    
    # Cross validation
    print("⏳ Thực hiện cross validation...")
    cv_results = classifier.cross_validate(X, y, cv=3)
    
    if cv_results:
        print(f"📊 Cross validation results:")
        print(f"   • Mean accuracy: {cv_results['mean_accuracy']:.3f}")
        print(f"   • Std accuracy: {cv_results['std_accuracy']:.3f}")
    
    # Train model
    print("⏳ Training model...")
    start_time = time.time()
    
    train_results = classifier.train(X, y, test_size=0.2)
    
    end_time = time.time()
    training_time = end_time - start_time
    
    if train_results:
        print(f"✅ Training hoàn thành trong {training_time:.3f} giây")
        print(f"📊 Training results:")
        print(f"   • Test accuracy: {train_results['test_accuracy']:.3f}")
        print(f"   • Precision: {train_results['precision']:.3f}")
        print(f"   • Recall: {train_results['recall']:.3f}")
        print(f"   • F1-score: {train_results['f1_score']:.3f}")
        if 'roc_auc' in train_results:
            print(f"   • ROC AUC: {train_results['roc_auc']:.3f}")
        
        # Feature importance
        importance = classifier.get_feature_importance()
        if importance:
            print(f"\n🔝 Top 5 important features:")
            for i, (feature, imp) in enumerate(list(importance.items())[:5], 1):
                print(f"   {i}. {feature}: {imp:.3f}")
    else:
        print("❌ Training thất bại")


def demo_prediction():
    """Demo prediction với trained model"""
    print("\n" + "="*60)
    print("🎯 DEMO: PREDICTION")
    print("="*60)
    
    # Check if model exists
    model_path = config.TRAINED_MODEL_PATH
    scaler_path = config.SCALER_PATH
    
    if not (model_path.exists() and scaler_path.exists()):
        print(f"❌ Trained model không tồn tại")
        print(f"💡 Chạy training trước: python src/models/train.py")
        return
    
    print(f"📁 Loading model: {model_path}")
    
    # Initialize classifier
    classifier = MalwareClassifier()
    classifier.load_model(str(model_path), str(scaler_path))
    
    print("✅ Model loaded successfully")
    
    # Test file
    test_file = "C:\\Windows\\System32\\notepad.exe"
    
    if not os.path.exists(test_file):
        print(f"❌ Test file không tồn tại: {test_file}")
        return
    
    print(f"🔍 Predicting file: {test_file}")
    
    # Predict
    start_time = time.time()
    result = classifier.predict(test_file)
    end_time = time.time()
    
    prediction_time = end_time - start_time
    
    if result:
        print(f"✅ Prediction hoàn thành trong {prediction_time:.3f} giây")
        print(f"📊 Kết quả:")
        print(f"   • Prediction: {result['prediction'].upper()}")
        if 'confidence' in result:
            print(f"   • Confidence: {result['confidence']:.3f}")
            print(f"   • Malware probability: {result['malware_probability']:.3f}")
            print(f"   • Benign probability: {result['benign_probability']:.3f}")
        
        # Interpretation
        if result['prediction'] == 'malware':
            print(f"⚠️  FILE ĐƯỢC XÁC ĐỊNH LÀ MALWARE!")
        else:
            print(f"✅ File được xác định là an toàn (benign)")
    else:
        print("❌ Prediction thất bại")


def demo_database():
    """Demo database operations"""
    print("\n" + "="*60)
    print("💾 DEMO: DATABASE OPERATIONS")
    print("="*60)
    
    # Initialize database
    db_manager = DatabaseManager()
    
    # Save sample analysis result
    print("💾 Lưu sample analysis result...")
    
    sample_result = {
        'filename': 'demo_file.exe',
        'file_size': 12345,
        'md5': 'sample_md5_hash_12345',
        'sha256': 'sample_sha256_hash_67890',
        'prediction': 'benign',
        'confidence': 0.95,
        'malware_probability': 0.05,
        'benign_probability': 0.95,
        'model_type': 'random_forest',
        'analysis_time': 1.234,
        'client_ip': '127.0.0.1'
    }
    
    result_id = db_manager.save_analysis_result(sample_result)
    
    if result_id:
        print(f"✅ Đã lưu analysis result với ID: {result_id}")
    else:
        print("❌ Không thể lưu analysis result")
    
    # Get statistics
    print("📊 Lấy thống kê hệ thống...")
    stats = db_manager.get_statistics()
    
    if stats:
        print(f"📈 Thống kê:")
        print(f"   • Total analyses: {stats['total_analyses']}")
        print(f"   • Malware detected: {stats['malware_detected']}")
        print(f"   • Benign detected: {stats['benign_detected']}")
        print(f"   • Detection rate: {stats['detection_rate']:.1f}%")
        
        if stats['active_model']['type']:
            print(f"   • Active model: {stats['active_model']['type']}")
            if stats['active_model']['accuracy']:
                print(f"   • Model accuracy: {stats['active_model']['accuracy']:.3f}")
    
    # Get analysis history
    print("📜 Lấy lịch sử phân tích...")
    history = db_manager.get_analysis_history(limit=5)
    
    if history:
        print(f"📋 {len(history)} recent analyses:")
        for i, analysis in enumerate(history, 1):
            print(f"   {i}. {analysis.filename} -> {analysis.prediction} "
                  f"({analysis.created_at.strftime('%Y-%m-%d %H:%M')})")
    else:
        print("📭 Chưa có lịch sử phân tích")


def demo_full_workflow():
    """Demo complete workflow"""
    print("\n" + "="*60)
    print("🔄 DEMO: COMPLETE WORKFLOW")
    print("="*60)
    
    print("🚀 Chạy complete workflow demo...")
    
    # 1. PE Analysis
    print("\n1️⃣ PE Analysis...")
    demo_pe_analysis()
    
    # 2. Batch Processing
    print("\n2️⃣ Batch Processing...")
    demo_batch_processing()
    
    # 3. Database Operations
    print("\n3️⃣ Database Operations...")
    demo_database()
    
    # 4. Model Training (skip if no data)
    print("\n4️⃣ Model Training...")
    if os.path.exists("data/datasets/malware_dataset.csv"):
        demo_model_training()
    else:
        print("⏭️  Skipping model training (no dataset)")
    
    # 5. Prediction (skip if no model)
    print("\n5️⃣ Prediction...")
    if config.TRAINED_MODEL_PATH.exists():
        demo_prediction()
    else:
        print("⏭️  Skipping prediction (no trained model)")
    
    print("\n🎉 Complete workflow demo finished!")


def main():
    """Main function"""
    parser = argparse.ArgumentParser(description='AI Malware Detection System Demo')
    parser.add_argument('--demo', type=str, 
                       choices=['pe', 'batch', 'training', 'prediction', 'database', 'full'],
                       default='full',
                       help='Demo to run (default: full)')
    
    args = parser.parse_args()
    
    # Print banner
    print_banner()
    
    # Create necessary directories
    config.create_directories()
    
    try:
        if args.demo == 'pe':
            demo_pe_analysis()
        elif args.demo == 'batch':
            demo_batch_processing()
        elif args.demo == 'training':
            demo_model_training()
        elif args.demo == 'prediction':
            demo_prediction()
        elif args.demo == 'database':
            demo_database()
        elif args.demo == 'full':
            demo_full_workflow()
        
        print(f"\n✨ Demo '{args.demo}' hoàn thành!")
        print(f"💡 Để chạy web interface: python src/api/app.py")
        print(f"💡 Để chạy tests: python run_tests.py")
        
    except KeyboardInterrupt:
        print(f"\n⏹️  Demo bị ngắt bởi user")
    except Exception as e:
        print(f"\n💥 Lỗi trong demo: {e}")
        import traceback
        traceback.print_exc()


if __name__ == '__main__':
    main()
