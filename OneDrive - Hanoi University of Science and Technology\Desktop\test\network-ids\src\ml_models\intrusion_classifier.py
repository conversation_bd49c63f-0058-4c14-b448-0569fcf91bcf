"""
Intrusion Classifier Module
Module phân loại các loại tấn công mạng
"""

import sys
import numpy as np
import pandas as pd
from pathlib import Path
import joblib
import logging
from datetime import datetime
from sklearn.ensemble import RandomForestClassifier
from sklearn.svm import SVC
from sklearn.preprocessing import StandardScaler, LabelEncoder
from sklearn.model_selection import train_test_split, cross_val_score, GridSearchCV
from sklearn.metrics import classification_report, confusion_matrix, accuracy_score
from sklearn.metrics import precision_score, recall_score, f1_score
import matplotlib.pyplot as plt
import seaborn as sns

# Add parent directory to path
sys.path.append(str(Path(__file__).parent.parent.parent))

try:
    import tensorflow as tf
    from tensorflow import keras
    from tensorflow.keras import layers
    TENSORFLOW_AVAILABLE = True
except ImportError:
    TENSORFLOW_AVAILABLE = False

import config


class IntrusionClassifier:
    """Class phân loại các loại intrusion/attack"""
    
    def __init__(self, algorithm='random_forest'):
        """
        Khởi tạo IntrusionClassifier
        
        Args:
            algorithm (str): Thuật toán ('random_forest', 'svm', 'neural_network')
        """
        self.algorithm = algorithm
        self.model = None
        self.scaler = StandardScaler()
        self.label_encoder = LabelEncoder()
        self.is_trained = False
        self.feature_names = []
        self.classes_ = []
        self.logger = logging.getLogger(__name__)
        
        # Attack types from config
        self.attack_types = config.INTRUSION_CONFIG['attack_types']
        
    def prepare_data(self, data, target_column='label'):
        """
        Chuẩn bị dữ liệu cho training
        
        Args:
            data (pd.DataFrame): Raw data
            target_column (str): Tên cột target
            
        Returns:
            tuple: (X, y) processed data
        """
        try:
            # Separate features and target
            if target_column not in data.columns:
                raise ValueError(f"Target column '{target_column}' không tồn tại")
            
            X = data.drop(columns=[target_column])
            y = data[target_column]
            
            # Select only numeric columns
            numeric_columns = X.select_dtypes(include=[np.number]).columns
            X = X[numeric_columns].copy()
            
            # Handle missing values
            X = X.fillna(X.mean())
            
            # Remove infinite values
            X = X.replace([np.inf, -np.inf], np.nan)
            X = X.fillna(X.mean())
            
            # Store feature names
            self.feature_names = list(X.columns)
            
            # Encode labels
            y_encoded = self.label_encoder.fit_transform(y)
            self.classes_ = self.label_encoder.classes_
            
            self.logger.info(f"Đã chuẩn bị {X.shape[0]} samples với {X.shape[1]} features")
            self.logger.info(f"Classes: {list(self.classes_)}")
            
            return X, y_encoded
            
        except Exception as e:
            self.logger.error(f"Lỗi khi chuẩn bị dữ liệu: {e}")
            return None, None
    
    def train(self, X, y, test_size=0.2, cv_folds=5):
        """
        Train intrusion classification model
        
        Args:
            X (pd.DataFrame): Training features
            y (array): Training labels
            test_size (float): Test set size
            cv_folds (int): Cross validation folds
            
        Returns:
            dict: Training results
        """
        try:
            # Split data
            X_train, X_test, y_train, y_test = train_test_split(
                X, y, test_size=test_size, 
                random_state=config.ML_CONFIG['random_state'],
                stratify=y
            )
            
            # Scale features
            X_train_scaled = self.scaler.fit_transform(X_train)
            X_test_scaled = self.scaler.transform(X_test)
            
            # Initialize model
            if self.algorithm == 'random_forest':
                self.model = RandomForestClassifier(
                    n_estimators=100,
                    max_depth=20,
                    min_samples_split=5,
                    min_samples_leaf=2,
                    random_state=config.ML_CONFIG['random_state'],
                    n_jobs=-1
                )
            
            elif self.algorithm == 'svm':
                self.model = SVC(
                    kernel='rbf',
                    C=1.0,
                    gamma='scale',
                    random_state=config.ML_CONFIG['random_state'],
                    probability=True
                )
            
            elif self.algorithm == 'neural_network':
                if not TENSORFLOW_AVAILABLE:
                    raise ValueError("TensorFlow không có sẵn cho neural network")
                self.model = self._build_neural_network(
                    X_train_scaled.shape[1], 
                    len(self.classes_)
                )
            
            else:
                raise ValueError(f"Thuật toán không được hỗ trợ: {self.algorithm}")
            
            # Train model
            self.logger.info(f"Training {self.algorithm} model...")
            start_time = datetime.now()
            
            if self.algorithm == 'neural_network':
                # Train neural network
                history = self.model.fit(
                    X_train_scaled, 
                    tf.keras.utils.to_categorical(y_train, len(self.classes_)),
                    epochs=100,
                    batch_size=32,
                    validation_split=0.2,
                    verbose=0
                )
                training_loss = history.history['loss'][-1]
                training_accuracy = history.history['accuracy'][-1]
            else:
                # Train traditional ML models
                self.model.fit(X_train_scaled, y_train)
                training_loss = None
                training_accuracy = self.model.score(X_train_scaled, y_train)
            
            end_time = datetime.now()
            training_time = (end_time - start_time).total_seconds()
            
            # Cross validation
            if self.algorithm != 'neural_network':
                cv_scores = cross_val_score(
                    self.model, X_train_scaled, y_train, 
                    cv=cv_folds, scoring='accuracy'
                )
                cv_mean = cv_scores.mean()
                cv_std = cv_scores.std()
            else:
                cv_mean = cv_std = None
            
            # Test evaluation
            if self.algorithm == 'neural_network':
                y_pred_proba = self.model.predict(X_test_scaled, verbose=0)
                y_pred = np.argmax(y_pred_proba, axis=1)
            else:
                y_pred = self.model.predict(X_test_scaled)
            
            # Calculate metrics
            test_accuracy = accuracy_score(y_test, y_pred)
            precision = precision_score(y_test, y_pred, average='weighted')
            recall = recall_score(y_test, y_pred, average='weighted')
            f1 = f1_score(y_test, y_pred, average='weighted')
            
            self.is_trained = True
            
            # Prepare results
            results = {
                'algorithm': self.algorithm,
                'training_time': training_time,
                'training_samples': len(X_train),
                'test_samples': len(X_test),
                'features': X.shape[1],
                'classes': len(self.classes_),
                'train_accuracy': training_accuracy,
                'test_accuracy': test_accuracy,
                'precision': precision,
                'recall': recall,
                'f1_score': f1,
                'confusion_matrix': confusion_matrix(y_test, y_pred),
                'classification_report': classification_report(
                    y_test, y_pred, 
                    target_names=self.classes_
                )
            }
            
            if training_loss:
                results['training_loss'] = training_loss
            
            if cv_mean is not None:
                results['cv_mean_accuracy'] = cv_mean
                results['cv_std_accuracy'] = cv_std
            
            self.logger.info(f"Training hoàn thành trong {training_time:.2f} giây")
            self.logger.info(f"Test accuracy: {test_accuracy:.4f}")
            
            return results
            
        except Exception as e:
            self.logger.error(f"Lỗi khi training model: {e}")
            return None
    
    def _build_neural_network(self, input_dim, num_classes):
        """Xây dựng neural network model"""
        model = keras.Sequential([
            layers.Dense(128, activation='relu', input_shape=(input_dim,)),
            layers.Dropout(0.3),
            layers.Dense(64, activation='relu'),
            layers.Dropout(0.3),
            layers.Dense(32, activation='relu'),
            layers.Dense(num_classes, activation='softmax')
        ])
        
        model.compile(
            optimizer='adam',
            loss='categorical_crossentropy',
            metrics=['accuracy']
        )
        
        return model
    
    def predict(self, X):
        """
        Predict attack types
        
        Args:
            X (pd.DataFrame): Features to predict
            
        Returns:
            array: Predicted attack types
        """
        if not self.is_trained:
            raise ValueError("Model chưa được train")
        
        try:
            # Scale features
            X_scaled = self.scaler.transform(X)
            
            if self.algorithm == 'neural_network':
                predictions_proba = self.model.predict(X_scaled, verbose=0)
                predictions = np.argmax(predictions_proba, axis=1)
            else:
                predictions = self.model.predict(X_scaled)
            
            # Decode labels
            predicted_labels = self.label_encoder.inverse_transform(predictions)
            
            return predicted_labels
            
        except Exception as e:
            self.logger.error(f"Lỗi khi predict: {e}")
            return None
    
    def predict_proba(self, X):
        """
        Predict attack probabilities
        
        Args:
            X (pd.DataFrame): Features to predict
            
        Returns:
            array: Prediction probabilities
        """
        if not self.is_trained:
            raise ValueError("Model chưa được train")
        
        try:
            X_scaled = self.scaler.transform(X)
            
            if self.algorithm == 'neural_network':
                probabilities = self.model.predict(X_scaled, verbose=0)
            else:
                probabilities = self.model.predict_proba(X_scaled)
            
            return probabilities
            
        except Exception as e:
            self.logger.error(f"Lỗi khi predict probabilities: {e}")
            return None
    
    def get_feature_importance(self):
        """
        Lấy feature importance
        
        Returns:
            dict: Feature importance scores
        """
        if not self.is_trained:
            return None
        
        try:
            if self.algorithm == 'random_forest':
                importance = self.model.feature_importances_
                feature_importance = dict(zip(self.feature_names, importance))
                
                # Sort by importance
                sorted_features = sorted(
                    feature_importance.items(), 
                    key=lambda x: x[1], 
                    reverse=True
                )
                
                return dict(sorted_features)
            
            else:
                self.logger.warning(f"Feature importance không có sẵn cho {self.algorithm}")
                return None
                
        except Exception as e:
            self.logger.error(f"Lỗi khi lấy feature importance: {e}")
            return None
    
    def save_model(self, model_path, scaler_path):
        """
        Lưu model và scaler
        
        Args:
            model_path (str): Đường dẫn lưu model
            scaler_path (str): Đường dẫn lưu scaler
        """
        try:
            if not self.is_trained:
                raise ValueError("Model chưa được train")
            
            if self.algorithm == 'neural_network':
                self.model.save(model_path)
            else:
                joblib.dump(self.model, model_path)
            
            joblib.dump(self.scaler, scaler_path)
            
            # Save metadata
            metadata = {
                'algorithm': self.algorithm,
                'feature_names': self.feature_names,
                'classes': list(self.classes_),
                'label_encoder_classes': list(self.label_encoder.classes_)
            }
            
            metadata_path = Path(model_path).parent / 'intrusion_metadata.json'
            import json
            with open(metadata_path, 'w') as f:
                json.dump(metadata, f, indent=2)
            
            # Save label encoder
            encoder_path = Path(model_path).parent / 'label_encoder.joblib'
            joblib.dump(self.label_encoder, encoder_path)
            
            self.logger.info(f"Model đã lưu vào: {model_path}")
            
        except Exception as e:
            self.logger.error(f"Lỗi khi lưu model: {e}")
    
    def load_model(self, model_path, scaler_path):
        """
        Load model và scaler đã train
        
        Args:
            model_path (str): Đường dẫn model
            scaler_path (str): Đường dẫn scaler
        """
        try:
            if self.algorithm == 'neural_network':
                if not TENSORFLOW_AVAILABLE:
                    raise ValueError("TensorFlow không có sẵn")
                self.model = keras.models.load_model(model_path)
            else:
                self.model = joblib.load(model_path)
            
            self.scaler = joblib.load(scaler_path)
            
            # Load label encoder
            encoder_path = Path(model_path).parent / 'label_encoder.joblib'
            if encoder_path.exists():
                self.label_encoder = joblib.load(encoder_path)
                self.classes_ = self.label_encoder.classes_
            
            # Load metadata
            metadata_path = Path(model_path).parent / 'intrusion_metadata.json'
            if metadata_path.exists():
                import json
                with open(metadata_path, 'r') as f:
                    metadata = json.load(f)
                
                self.feature_names = metadata.get('feature_names', [])
                if not hasattr(self, 'classes_') or len(self.classes_) == 0:
                    self.classes_ = np.array(metadata.get('classes', []))
            
            self.is_trained = True
            self.logger.info(f"Đã load model từ: {model_path}")
            
        except Exception as e:
            self.logger.error(f"Lỗi khi load model: {e}")
    
    def plot_results(self, results):
        """
        Vẽ biểu đồ kết quả training
        
        Args:
            results (dict): Training results
        """
        try:
            plt.figure(figsize=(15, 10))
            
            # Plot 1: Confusion Matrix
            plt.subplot(2, 3, 1)
            sns.heatmap(
                results['confusion_matrix'], 
                annot=True, 
                fmt='d',
                xticklabels=self.classes_,
                yticklabels=self.classes_,
                cmap='Blues'
            )
            plt.title('Confusion Matrix')
            plt.ylabel('True Label')
            plt.xlabel('Predicted Label')
            
            # Plot 2: Metrics
            plt.subplot(2, 3, 2)
            metrics = ['Accuracy', 'Precision', 'Recall', 'F1-Score']
            values = [
                results['test_accuracy'],
                results['precision'],
                results['recall'],
                results['f1_score']
            ]
            bars = plt.bar(metrics, values, color=['blue', 'green', 'orange', 'red'])
            plt.ylim(0, 1)
            plt.title('Performance Metrics')
            plt.ylabel('Score')
            
            # Add value labels on bars
            for bar, value in zip(bars, values):
                plt.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.01,
                        f'{value:.3f}', ha='center', va='bottom')
            
            # Plot 3: Feature Importance (if available)
            if self.algorithm == 'random_forest':
                importance = self.get_feature_importance()
                if importance:
                    plt.subplot(2, 3, 3)
                    top_features = list(importance.items())[:10]
                    features, scores = zip(*top_features)
                    plt.barh(features, scores)
                    plt.title('Top 10 Feature Importance')
                    plt.xlabel('Importance Score')
            
            # Plot 4: Class Distribution
            plt.subplot(2, 3, 4)
            class_counts = np.bincount(self.label_encoder.transform(self.classes_))
            plt.pie(class_counts, labels=self.classes_, autopct='%1.1f%%')
            plt.title('Class Distribution')
            
            # Plot 5: Training Info
            plt.subplot(2, 3, 5)
            info_text = f"""
            Algorithm: {results['algorithm']}
            Training Time: {results['training_time']:.2f}s
            Training Samples: {results['training_samples']}
            Test Samples: {results['test_samples']}
            Features: {results['features']}
            Classes: {results['classes']}
            """
            plt.text(0.1, 0.5, info_text, fontsize=10, verticalalignment='center')
            plt.axis('off')
            plt.title('Training Information')
            
            plt.tight_layout()
            plt.show()
            
        except Exception as e:
            self.logger.error(f"Lỗi khi vẽ biểu đồ: {e}")


if __name__ == "__main__":
    # Test intrusion classifier
    logging.basicConfig(level=logging.INFO)
    
    # Create sample data
    np.random.seed(42)
    
    # Generate synthetic network traffic data
    n_samples = 1000
    n_features = 20
    
    # Normal traffic
    normal_data = np.random.normal(0, 1, (n_samples//2, n_features))
    
    # Attack traffic (different patterns)
    dos_data = np.random.normal(2, 0.5, (n_samples//4, n_features))
    probe_data = np.random.normal(-1, 1.5, (n_samples//4, n_features))
    
    X = np.vstack([normal_data, dos_data, probe_data])
    y = ['normal'] * (n_samples//2) + ['dos'] * (n_samples//4) + ['probe'] * (n_samples//4)
    
    df = pd.DataFrame(X, columns=[f'feature_{i}' for i in range(n_features)])
    df['label'] = y
    
    # Test classifier
    classifier = IntrusionClassifier('random_forest')
    X_processed, y_processed = classifier.prepare_data(df, 'label')
    
    if X_processed is not None:
        results = classifier.train(X_processed, y_processed)
        if results:
            print("Training results:")
            print(f"  Test accuracy: {results['test_accuracy']:.4f}")
            print(f"  F1-score: {results['f1_score']:.4f}")
            
            # Test predictions
            predictions = classifier.predict(X_processed[:10])
            probabilities = classifier.predict_proba(X_processed[:10])
            
            print(f"Sample predictions: {predictions}")
            print(f"Sample probabilities shape: {probabilities.shape}")
    
    print("✅ Intrusion Classifier test completed!")
