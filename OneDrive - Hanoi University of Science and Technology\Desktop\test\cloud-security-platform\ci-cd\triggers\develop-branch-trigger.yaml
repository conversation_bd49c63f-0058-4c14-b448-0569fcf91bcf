# Cloud Build Trigger for Develop Branch (Staging Deployment)
# This trigger runs on pushes to the develop branch and deploys to staging

name: security-platform-develop-trigger
description: "Staging deployment trigger for Security Platform"

# GitHub configuration
github:
  owner: "your-github-username"  # Replace with actual GitHub username/org
  name: "cloud-security-platform"  # Replace with actual repository name
  push:
    branch: "^develop$"

# Build configuration
filename: "ci-cd/cloudbuild.yaml"

# Substitutions for staging environment
substitutions:
  _ENVIRONMENT: "staging"
  _CLUSTER_NAME: "security-platform-cluster"
  _CLUSTER_LOCATION: "asia-southeast1"
  _NAMESPACE: "security-platform"

# Service account for the build
serviceAccount: "projects/security-platform-prod/serviceAccounts/<EMAIL>"

# No approval required for staging
approvalConfig:
  approvalRequired: false

# Include build logs in Cloud Logging
includeBuildLogs: INCLUDE_BUILD_LOGS_WITH_STATUS

# Tags for organization
tags:
  - "security-platform"
  - "staging"
  - "develop-branch"

# Filter to only trigger on specific file changes
ignoredFiles:
  - "docs/**"
  - "README.md"
  - "*.md"
  - ".gitignore"

includedFiles:
  - "src/**"
  - "docker/**"
  - "kubernetes/**"
  - "ci-cd/**"
  - "terraform/**"
