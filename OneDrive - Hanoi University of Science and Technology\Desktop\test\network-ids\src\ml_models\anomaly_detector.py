"""
Anomaly Detector Module
Module ph<PERSON>t hiện bất thường trong network traffic
"""

import sys
import numpy as np
import pandas as pd
from pathlib import Path
import joblib
import logging
from datetime import datetime
from sklearn.ensemble import IsolationForest
from sklearn.svm import OneClassSVM
from sklearn.preprocessing import StandardScaler, MinMaxScaler
from sklearn.metrics import classification_report, confusion_matrix
from sklearn.model_selection import train_test_split
import matplotlib.pyplot as plt
import seaborn as sns

# Add parent directory to path
sys.path.append(str(Path(__file__).parent.parent.parent))

try:
    import tensorflow as tf
    from tensorflow import keras
    from tensorflow.keras import layers
    TENSORFLOW_AVAILABLE = True
except ImportError:
    TENSORFLOW_AVAILABLE = False
    print("⚠️  TensorFlow không có sẵn. Autoencoder sẽ không hoạt động.")

import config


class AnomalyDetector:
    """Class phát hiện anomaly trong network traffic"""
    
    def __init__(self, algorithm='isolation_forest'):
        """
        Khởi tạo AnomalyDetector
        
        Args:
            algorithm (str): Thu<PERSON><PERSON> toán ('isolation_forest', 'one_class_svm', 'autoencoder')
        """
        self.algorithm = algorithm
        self.model = None
        self.scaler = StandardScaler()
        self.is_trained = False
        self.feature_names = []
        self.logger = logging.getLogger(__name__)
        
        # Thresholds
        self.contamination = config.ANOMALY_CONFIG['contamination']
        self.threshold = config.ANOMALY_CONFIG['threshold']
        
    def prepare_data(self, data, target_column=None):
        """
        Chuẩn bị dữ liệu cho training
        
        Args:
            data (pd.DataFrame): Raw data
            target_column (str): Tên cột target (nếu có)
            
        Returns:
            tuple: (X, y) processed data
        """
        try:
            # Remove non-numeric columns
            numeric_columns = data.select_dtypes(include=[np.number]).columns
            X = data[numeric_columns].copy()
            
            # Handle missing values
            X = X.fillna(X.mean())
            
            # Remove infinite values
            X = X.replace([np.inf, -np.inf], np.nan)
            X = X.fillna(X.mean())
            
            # Store feature names
            self.feature_names = list(X.columns)
            
            # Prepare target if available
            y = None
            if target_column and target_column in data.columns:
                y = data[target_column].values
                # Convert to binary (1 for anomaly, -1 for normal)
                y = np.where(y == 'anomaly', 1, -1)
            
            self.logger.info(f"Đã chuẩn bị {X.shape[0]} samples với {X.shape[1]} features")
            
            return X, y
            
        except Exception as e:
            self.logger.error(f"Lỗi khi chuẩn bị dữ liệu: {e}")
            return None, None
    
    def train(self, X, y=None):
        """
        Train anomaly detection model
        
        Args:
            X (pd.DataFrame): Training features
            y (array): Labels (optional, for evaluation)
            
        Returns:
            dict: Training results
        """
        try:
            # Scale features
            X_scaled = self.scaler.fit_transform(X)
            
            # Initialize model based on algorithm
            if self.algorithm == 'isolation_forest':
                self.model = IsolationForest(
                    contamination=self.contamination,
                    random_state=config.ML_CONFIG['random_state'],
                    n_jobs=-1
                )
            
            elif self.algorithm == 'one_class_svm':
                self.model = OneClassSVM(
                    nu=self.contamination,
                    kernel='rbf',
                    gamma='scale'
                )
            
            elif self.algorithm == 'autoencoder':
                if not TENSORFLOW_AVAILABLE:
                    raise ValueError("TensorFlow không có sẵn cho autoencoder")
                self.model = self._build_autoencoder(X_scaled.shape[1])
            
            else:
                raise ValueError(f"Thuật toán không được hỗ trợ: {self.algorithm}")
            
            # Train model
            self.logger.info(f"Training {self.algorithm} model...")
            start_time = datetime.now()
            
            if self.algorithm == 'autoencoder':
                # Train autoencoder
                history = self.model.fit(
                    X_scaled, X_scaled,
                    epochs=100,
                    batch_size=32,
                    validation_split=0.2,
                    verbose=0
                )
                training_loss = history.history['loss'][-1]
            else:
                # Train traditional ML models
                self.model.fit(X_scaled)
                training_loss = None
            
            end_time = datetime.now()
            training_time = (end_time - start_time).total_seconds()
            
            self.is_trained = True
            
            # Evaluate if labels are available
            results = {
                'algorithm': self.algorithm,
                'training_time': training_time,
                'training_samples': X.shape[0],
                'features': X.shape[1]
            }
            
            if training_loss:
                results['training_loss'] = training_loss
            
            if y is not None:
                predictions = self.predict(X)
                # Convert predictions to match y format
                pred_binary = np.where(predictions == 'anomaly', 1, -1)
                
                results['accuracy'] = np.mean(pred_binary == y)
                results['classification_report'] = classification_report(y, pred_binary)
                results['confusion_matrix'] = confusion_matrix(y, pred_binary)
            
            self.logger.info(f"Training hoàn thành trong {training_time:.2f} giây")
            
            return results
            
        except Exception as e:
            self.logger.error(f"Lỗi khi training model: {e}")
            return None
    
    def _build_autoencoder(self, input_dim):
        """Xây dựng autoencoder model"""
        # Encoder
        input_layer = keras.Input(shape=(input_dim,))
        encoded = layers.Dense(64, activation='relu')(input_layer)
        encoded = layers.Dense(32, activation='relu')(encoded)
        encoded = layers.Dense(16, activation='relu')(encoded)
        
        # Decoder
        decoded = layers.Dense(32, activation='relu')(encoded)
        decoded = layers.Dense(64, activation='relu')(decoded)
        decoded = layers.Dense(input_dim, activation='linear')(decoded)
        
        # Autoencoder
        autoencoder = keras.Model(input_layer, decoded)
        autoencoder.compile(optimizer='adam', loss='mse')
        
        return autoencoder
    
    def predict(self, X):
        """
        Predict anomalies
        
        Args:
            X (pd.DataFrame): Features to predict
            
        Returns:
            array: Predictions ('normal' or 'anomaly')
        """
        if not self.is_trained:
            raise ValueError("Model chưa được train")
        
        try:
            # Scale features
            X_scaled = self.scaler.transform(X)
            
            if self.algorithm == 'autoencoder':
                # Calculate reconstruction error
                reconstructed = self.model.predict(X_scaled, verbose=0)
                mse = np.mean(np.power(X_scaled - reconstructed, 2), axis=1)
                
                # Determine threshold (can be improved with validation set)
                threshold = np.percentile(mse, (1 - self.contamination) * 100)
                predictions = np.where(mse > threshold, -1, 1)
            
            else:
                # Traditional ML predictions
                predictions = self.model.predict(X_scaled)
            
            # Convert to readable format
            result = np.where(predictions == -1, 'anomaly', 'normal')
            
            return result
            
        except Exception as e:
            self.logger.error(f"Lỗi khi predict: {e}")
            return None
    
    def predict_proba(self, X):
        """
        Predict anomaly probabilities/scores
        
        Args:
            X (pd.DataFrame): Features to predict
            
        Returns:
            array: Anomaly scores
        """
        if not self.is_trained:
            raise ValueError("Model chưa được train")
        
        try:
            X_scaled = self.scaler.transform(X)
            
            if self.algorithm == 'isolation_forest':
                # Decision function gives anomaly scores
                scores = self.model.decision_function(X_scaled)
                # Convert to probabilities (higher = more anomalous)
                probabilities = 1 / (1 + np.exp(scores))
            
            elif self.algorithm == 'one_class_svm':
                scores = self.model.decision_function(X_scaled)
                probabilities = 1 / (1 + np.exp(scores))
            
            elif self.algorithm == 'autoencoder':
                reconstructed = self.model.predict(X_scaled, verbose=0)
                mse = np.mean(np.power(X_scaled - reconstructed, 2), axis=1)
                # Normalize MSE to probabilities
                probabilities = (mse - mse.min()) / (mse.max() - mse.min())
            
            return probabilities
            
        except Exception as e:
            self.logger.error(f"Lỗi khi predict probabilities: {e}")
            return None
    
    def get_feature_importance(self):
        """
        Lấy feature importance (chỉ cho Isolation Forest)
        
        Returns:
            dict: Feature importance scores
        """
        if self.algorithm != 'isolation_forest' or not self.is_trained:
            return None
        
        try:
            # Isolation Forest doesn't have direct feature importance
            # We can estimate it by permutation importance
            self.logger.warning("Feature importance cho Isolation Forest cần được tính toán riêng")
            return None
            
        except Exception as e:
            self.logger.error(f"Lỗi khi lấy feature importance: {e}")
            return None
    
    def save_model(self, model_path, scaler_path):
        """
        Lưu model và scaler
        
        Args:
            model_path (str): Đường dẫn lưu model
            scaler_path (str): Đường dẫn lưu scaler
        """
        try:
            if not self.is_trained:
                raise ValueError("Model chưa được train")
            
            if self.algorithm == 'autoencoder':
                self.model.save(model_path)
            else:
                joblib.dump(self.model, model_path)
            
            joblib.dump(self.scaler, scaler_path)
            
            # Save metadata
            metadata = {
                'algorithm': self.algorithm,
                'feature_names': self.feature_names,
                'contamination': self.contamination,
                'threshold': self.threshold
            }
            
            metadata_path = Path(model_path).parent / 'anomaly_metadata.json'
            import json
            with open(metadata_path, 'w') as f:
                json.dump(metadata, f, indent=2)
            
            self.logger.info(f"Model đã lưu vào: {model_path}")
            
        except Exception as e:
            self.logger.error(f"Lỗi khi lưu model: {e}")
    
    def load_model(self, model_path, scaler_path):
        """
        Load model và scaler đã train
        
        Args:
            model_path (str): Đường dẫn model
            scaler_path (str): Đường dẫn scaler
        """
        try:
            if self.algorithm == 'autoencoder':
                if not TENSORFLOW_AVAILABLE:
                    raise ValueError("TensorFlow không có sẵn")
                self.model = keras.models.load_model(model_path)
            else:
                self.model = joblib.load(model_path)
            
            self.scaler = joblib.load(scaler_path)
            
            # Load metadata
            metadata_path = Path(model_path).parent / 'anomaly_metadata.json'
            if metadata_path.exists():
                import json
                with open(metadata_path, 'r') as f:
                    metadata = json.load(f)
                
                self.feature_names = metadata.get('feature_names', [])
                self.contamination = metadata.get('contamination', self.contamination)
                self.threshold = metadata.get('threshold', self.threshold)
            
            self.is_trained = True
            self.logger.info(f"Đã load model từ: {model_path}")
            
        except Exception as e:
            self.logger.error(f"Lỗi khi load model: {e}")
    
    def plot_anomaly_scores(self, X, predictions, scores):
        """
        Vẽ biểu đồ anomaly scores
        
        Args:
            X (pd.DataFrame): Features
            predictions (array): Predictions
            scores (array): Anomaly scores
        """
        try:
            plt.figure(figsize=(12, 8))
            
            # Plot 1: Score distribution
            plt.subplot(2, 2, 1)
            plt.hist(scores, bins=50, alpha=0.7, edgecolor='black')
            plt.axvline(self.threshold, color='red', linestyle='--', label='Threshold')
            plt.xlabel('Anomaly Score')
            plt.ylabel('Frequency')
            plt.title('Anomaly Score Distribution')
            plt.legend()
            
            # Plot 2: Predictions
            plt.subplot(2, 2, 2)
            unique, counts = np.unique(predictions, return_counts=True)
            plt.pie(counts, labels=unique, autopct='%1.1f%%')
            plt.title('Prediction Distribution')
            
            # Plot 3: Score vs Index
            plt.subplot(2, 2, 3)
            colors = ['red' if p == 'anomaly' else 'blue' for p in predictions]
            plt.scatter(range(len(scores)), scores, c=colors, alpha=0.6)
            plt.axhline(self.threshold, color='red', linestyle='--')
            plt.xlabel('Sample Index')
            plt.ylabel('Anomaly Score')
            plt.title('Anomaly Scores (Red=Anomaly, Blue=Normal)')
            
            # Plot 4: Feature correlation (if applicable)
            if len(self.feature_names) > 1:
                plt.subplot(2, 2, 4)
                corr_matrix = X[self.feature_names[:10]].corr()  # Top 10 features
                sns.heatmap(corr_matrix, annot=True, cmap='coolwarm', center=0)
                plt.title('Feature Correlation')
            
            plt.tight_layout()
            plt.show()
            
        except Exception as e:
            self.logger.error(f"Lỗi khi vẽ biểu đồ: {e}")


if __name__ == "__main__":
    # Test anomaly detector
    logging.basicConfig(level=logging.INFO)
    
    # Create sample data
    np.random.seed(42)
    normal_data = np.random.normal(0, 1, (1000, 10))
    anomaly_data = np.random.normal(3, 1, (100, 10))
    
    X = np.vstack([normal_data, anomaly_data])
    y = np.array(['normal'] * 1000 + ['anomaly'] * 100)
    
    df = pd.DataFrame(X, columns=[f'feature_{i}' for i in range(10)])
    df['label'] = y
    
    # Test detector
    detector = AnomalyDetector('isolation_forest')
    X_processed, y_processed = detector.prepare_data(df, 'label')
    
    if X_processed is not None:
        results = detector.train(X_processed, y_processed)
        print("Training results:", results)
        
        # Test predictions
        predictions = detector.predict(X_processed[:100])
        scores = detector.predict_proba(X_processed[:100])
        
        print(f"Sample predictions: {predictions[:10]}")
        print(f"Sample scores: {scores[:10]}")
    
    print("✅ Anomaly Detector test completed!")
