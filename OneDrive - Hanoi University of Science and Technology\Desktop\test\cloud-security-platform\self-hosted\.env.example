# Cloud Security Platform - Self-Hosted Configuration
# Copy this file to .env and update the values

# =============================================================================
# DATABASE CONFIGURATION
# =============================================================================
DB_HOST=postgresql
DB_PORT=5432
DB_NAME=security_platform
DB_USER=security_user
DB_PASSWORD=your_secure_database_password_here

# =============================================================================
# REDIS CONFIGURATION
# =============================================================================
REDIS_HOST=redis
REDIS_PORT=6379
REDIS_PASSWORD=

# =============================================================================
# MINIO OBJECT STORAGE CONFIGURATION
# =============================================================================
MINIO_ENDPOINT=minio:9000
MINIO_ACCESS_KEY=minioadmin
MINIO_SECRET_KEY=minioadmin123_change_this
MINIO_SECURE=false

# =============================================================================
# APPLICATION SECRETS
# =============================================================================
SECRET_KEY=your_super_secret_key_change_this_in_production
JWT_SECRET=your_jwt_secret_key_change_this
API_KEY=your_api_key_for_external_access

# =============================================================================
# FLASK CONFIGURATION
# =============================================================================
FLASK_ENV=production
FLASK_DEBUG=false

# =============================================================================
# MONITORING CONFIGURATION
# =============================================================================
GRAFANA_PASSWORD=admin123_change_this
PROMETHEUS_RETENTION=30d

# =============================================================================
# ELASTICSEARCH CONFIGURATION
# =============================================================================
ELASTICSEARCH_HOST=elasticsearch
ELASTICSEARCH_PORT=9200
ELASTICSEARCH_USERNAME=
ELASTICSEARCH_PASSWORD=

# =============================================================================
# SSL/TLS CONFIGURATION
# =============================================================================
SSL_ENABLED=false
SSL_CERT_PATH=/etc/nginx/ssl/cert.pem
SSL_KEY_PATH=/etc/nginx/ssl/key.pem

# =============================================================================
# NETWORK CONFIGURATION
# =============================================================================
EXTERNAL_IP=localhost
DOMAIN_NAME=security-platform.local

# =============================================================================
# MALWARE DETECTION CONFIGURATION
# =============================================================================
MALWARE_MAX_FILE_SIZE=100MB
MALWARE_ALLOWED_EXTENSIONS=exe,dll,bin,com,scr
MALWARE_MODEL_PATH=/app/models
MALWARE_UPLOAD_PATH=/app/uploads

# =============================================================================
# NETWORK IDS CONFIGURATION
# =============================================================================
IDS_INTERFACE=eth0
IDS_CAPTURE_FILTER=
IDS_PACKET_BUFFER_SIZE=1000
IDS_ANALYSIS_INTERVAL=60

# =============================================================================
# DASHBOARD CONFIGURATION
# =============================================================================
DASHBOARD_REFRESH_INTERVAL=30
DASHBOARD_MAX_ALERTS=1000
DASHBOARD_THEME=dark

# =============================================================================
# BACKUP CONFIGURATION
# =============================================================================
BACKUP_ENABLED=true
BACKUP_SCHEDULE=0 2 * * *
BACKUP_RETENTION_DAYS=30
BACKUP_PATH=/backup

# =============================================================================
# LOGGING CONFIGURATION
# =============================================================================
LOG_LEVEL=INFO
LOG_FORMAT=json
LOG_MAX_SIZE=100MB
LOG_BACKUP_COUNT=5

# =============================================================================
# PERFORMANCE TUNING
# =============================================================================
WORKER_PROCESSES=4
WORKER_CONNECTIONS=1000
MAX_REQUESTS_PER_CHILD=1000

# =============================================================================
# SECURITY CONFIGURATION
# =============================================================================
ENABLE_RATE_LIMITING=true
RATE_LIMIT_PER_MINUTE=100
ENABLE_CORS=false
ALLOWED_ORIGINS=http://localhost:3000,http://localhost:8080

# =============================================================================
# NOTIFICATION CONFIGURATION
# =============================================================================
SMTP_HOST=
SMTP_PORT=587
SMTP_USERNAME=
SMTP_PASSWORD=
SMTP_USE_TLS=true
EMAIL_FROM=<EMAIL>

SLACK_WEBHOOK_URL=
DISCORD_WEBHOOK_URL=

# =============================================================================
# DEVELOPMENT/DEBUG CONFIGURATION
# =============================================================================
DEBUG_MODE=false
ENABLE_PROFILING=false
MOCK_EXTERNAL_APIS=false

# =============================================================================
# RESOURCE LIMITS
# =============================================================================
MAX_MEMORY_USAGE=2GB
MAX_CPU_USAGE=80%
MAX_DISK_USAGE=80%

# =============================================================================
# TIMEZONE CONFIGURATION
# =============================================================================
TZ=Asia/Ho_Chi_Minh

# =============================================================================
# CUSTOM CONFIGURATION
# =============================================================================
# Add your custom environment variables here
