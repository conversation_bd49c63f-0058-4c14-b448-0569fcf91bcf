"""
Malware Classifier
Module chứa các ML models để phân loại malware
"""

import pandas as pd
import numpy as np
import joblib
from pathlib import Path
import logging
from sklearn.model_selection import train_test_split, cross_val_score, GridSearchCV
from sklearn.ensemble import RandomForestClassifier
from sklearn.svm import SVC
from sklearn.preprocessing import StandardScaler
from sklearn.metrics import classification_report, confusion_matrix, accuracy_score
from sklearn.metrics import precision_score, recall_score, f1_score, roc_auc_score
import matplotlib.pyplot as plt
import seaborn as sns

import sys
from pathlib import Path
sys.path.append(str(Path(__file__).parent.parent))

from feature_extraction.pe_analyzer import PEAnalyzer


class MalwareClassifier:
    """Class chính để train và sử dụng ML models phân loại malware"""
    
    def __init__(self, model_type='random_forest'):
        """
        Khởi tạo classifier
        
        Args:
            model_type (str): Loại model ('random_forest', 'svm')
        """
        self.model_type = model_type
        self.model = None
        self.scaler = StandardScaler()
        self.feature_names = PEAnalyzer.get_feature_names()
        self.is_trained = False
        self.logger = logging.getLogger(__name__)
        
    def load_dataset(self, dataset_path):
        """
        Load dataset từ CSV file
        
        Args:
            dataset_path (str): Đường dẫn đến file dataset
            
        Returns:
            tuple: (X, y) features và labels
        """
        try:
            df = pd.read_csv(dataset_path)
            self.logger.info(f"Đã load dataset với {len(df)} samples")
            
            # Tách features và labels
            feature_columns = [col for col in df.columns if col in self.feature_names]
            X = df[feature_columns].values
            y = (df['label'] == 'malware').astype(int)  # 1 for malware, 0 for benign
            
            self.logger.info(f"Features shape: {X.shape}")
            self.logger.info(f"Labels distribution: Malware={sum(y)}, Benign={len(y)-sum(y)}")
            
            return X, y
            
        except Exception as e:
            self.logger.error(f"Lỗi khi load dataset: {e}")
            return None, None
    
    def train(self, X, y, test_size=0.2, random_state=42):
        """
        Train model với dữ liệu
        
        Args:
            X (array): Features
            y (array): Labels
            test_size (float): Tỷ lệ test set
            random_state (int): Random seed
            
        Returns:
            dict: Kết quả training
        """
        try:
            # Split data
            X_train, X_test, y_train, y_test = train_test_split(
                X, y, test_size=test_size, random_state=random_state, stratify=y
            )
            
            # Scale features
            X_train_scaled = self.scaler.fit_transform(X_train)
            X_test_scaled = self.scaler.transform(X_test)
            
            # Initialize model
            if self.model_type == 'random_forest':
                self.model = RandomForestClassifier(
                    n_estimators=100,
                    max_depth=20,
                    min_samples_split=5,
                    min_samples_leaf=2,
                    random_state=random_state,
                    n_jobs=-1
                )
            elif self.model_type == 'svm':
                self.model = SVC(
                    kernel='rbf',
                    C=1.0,
                    gamma='scale',
                    random_state=random_state,
                    probability=True
                )
            else:
                raise ValueError(f"Unsupported model type: {self.model_type}")
            
            # Train model
            self.logger.info(f"Training {self.model_type} model...")
            self.model.fit(X_train_scaled, y_train)
            
            # Evaluate
            train_pred = self.model.predict(X_train_scaled)
            test_pred = self.model.predict(X_test_scaled)
            
            # Calculate metrics
            results = {
                'train_accuracy': accuracy_score(y_train, train_pred),
                'test_accuracy': accuracy_score(y_test, test_pred),
                'precision': precision_score(y_test, test_pred),
                'recall': recall_score(y_test, test_pred),
                'f1_score': f1_score(y_test, test_pred),
                'confusion_matrix': confusion_matrix(y_test, test_pred),
                'classification_report': classification_report(y_test, test_pred)
            }
            
            # ROC AUC if model supports probability
            if hasattr(self.model, 'predict_proba'):
                test_proba = self.model.predict_proba(X_test_scaled)[:, 1]
                results['roc_auc'] = roc_auc_score(y_test, test_proba)
            
            self.is_trained = True
            
            # Log results
            self.logger.info("Training completed!")
            self.logger.info(f"Test Accuracy: {results['test_accuracy']:.4f}")
            self.logger.info(f"Precision: {results['precision']:.4f}")
            self.logger.info(f"Recall: {results['recall']:.4f}")
            self.logger.info(f"F1-Score: {results['f1_score']:.4f}")
            if 'roc_auc' in results:
                self.logger.info(f"ROC AUC: {results['roc_auc']:.4f}")
            
            return results
            
        except Exception as e:
            self.logger.error(f"Lỗi khi training model: {e}")
            return None
    
    def cross_validate(self, X, y, cv=5):
        """
        Thực hiện cross validation
        
        Args:
            X (array): Features
            y (array): Labels
            cv (int): Số folds
            
        Returns:
            dict: Kết quả cross validation
        """
        try:
            if self.model is None:
                raise ValueError("Model chưa được khởi tạo")
            
            # Scale features
            X_scaled = self.scaler.fit_transform(X)
            
            # Cross validation
            cv_scores = cross_val_score(self.model, X_scaled, y, cv=cv, scoring='accuracy')
            
            results = {
                'cv_scores': cv_scores,
                'mean_accuracy': cv_scores.mean(),
                'std_accuracy': cv_scores.std()
            }
            
            self.logger.info(f"Cross Validation Results:")
            self.logger.info(f"Mean Accuracy: {results['mean_accuracy']:.4f} (+/- {results['std_accuracy']*2:.4f})")
            
            return results
            
        except Exception as e:
            self.logger.error(f"Lỗi khi cross validation: {e}")
            return None
    
    def predict(self, file_path):
        """
        Predict một file PE
        
        Args:
            file_path (str): Đường dẫn file PE
            
        Returns:
            dict: Kết quả prediction
        """
        try:
            if not self.is_trained:
                raise ValueError("Model chưa được train")
            
            # Extract features
            analyzer = PEAnalyzer()
            features = analyzer.analyze_file(file_path)
            
            if features is None:
                return None
            
            feature_vector = analyzer.get_feature_vector()
            if feature_vector is None:
                return None
            
            # Reshape và scale
            X = np.array(feature_vector).reshape(1, -1)
            X_scaled = self.scaler.transform(X)
            
            # Predict
            prediction = self.model.predict(X_scaled)[0]
            
            result = {
                'file_path': file_path,
                'prediction': 'malware' if prediction == 1 else 'benign',
                'confidence': None
            }
            
            # Thêm confidence nếu model hỗ trợ
            if hasattr(self.model, 'predict_proba'):
                proba = self.model.predict_proba(X_scaled)[0]
                result['confidence'] = max(proba)
                result['malware_probability'] = proba[1]
                result['benign_probability'] = proba[0]
            
            return result
            
        except Exception as e:
            self.logger.error(f"Lỗi khi predict file {file_path}: {e}")
            return None
    
    def save_model(self, model_path, scaler_path):
        """
        Lưu model và scaler
        
        Args:
            model_path (str): Đường dẫn lưu model
            scaler_path (str): Đường dẫn lưu scaler
        """
        try:
            if not self.is_trained:
                raise ValueError("Model chưa được train")
            
            joblib.dump(self.model, model_path)
            joblib.dump(self.scaler, scaler_path)
            
            self.logger.info(f"Model đã lưu vào: {model_path}")
            self.logger.info(f"Scaler đã lưu vào: {scaler_path}")
            
        except Exception as e:
            self.logger.error(f"Lỗi khi lưu model: {e}")
    
    def load_model(self, model_path, scaler_path):
        """
        Load model và scaler đã train
        
        Args:
            model_path (str): Đường dẫn model
            scaler_path (str): Đường dẫn scaler
        """
        try:
            self.model = joblib.load(model_path)
            self.scaler = joblib.load(scaler_path)
            self.is_trained = True
            
            self.logger.info(f"Đã load model từ: {model_path}")
            self.logger.info(f"Đã load scaler từ: {scaler_path}")
            
        except Exception as e:
            self.logger.error(f"Lỗi khi load model: {e}")
    
    def get_feature_importance(self):
        """
        Lấy feature importance (chỉ cho Random Forest)
        
        Returns:
            dict: Feature importance
        """
        try:
            if not self.is_trained:
                raise ValueError("Model chưa được train")
            
            if self.model_type == 'random_forest':
                importance = self.model.feature_importances_
                feature_importance = dict(zip(self.feature_names, importance))
                
                # Sort by importance
                sorted_features = sorted(feature_importance.items(), 
                                       key=lambda x: x[1], reverse=True)
                
                self.logger.info("Top 10 important features:")
                for feature, importance in sorted_features[:10]:
                    self.logger.info(f"  {feature}: {importance:.4f}")
                
                return dict(sorted_features)
            else:
                self.logger.warning("Feature importance chỉ có sẵn cho Random Forest")
                return None
                
        except Exception as e:
            self.logger.error(f"Lỗi khi lấy feature importance: {e}")
            return None


if __name__ == "__main__":
    # Setup logging
    logging.basicConfig(level=logging.INFO, 
                       format='%(asctime)s - %(levelname)s - %(message)s')
    
    # Test classifier
    classifier = MalwareClassifier('random_forest')
    
    print("MalwareClassifier đã sẵn sàng!")
    print("Sử dụng classifier.load_dataset() để load dữ liệu và classifier.train() để train model.")
