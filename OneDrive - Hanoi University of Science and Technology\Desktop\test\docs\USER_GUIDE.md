# Hướng dẫn Sử dụng - AI Malware Detection System

## Tổng quan

AI Malware Detection System là một hệ thống phát hiện phần mềm độc hại sử dụng Machine Learning. Hệ thống có thể phân tích các file PE (Windows executables) và xác định xem chúng có phải là malware hay không.

## Giao diện Web

### Truy cập hệ thống

1. Khởi động server:
   ```bash
   python src/api/app.py
   ```

2. Mở trình duyệt và truy cập: `http://localhost:5000`

### Các thành phần giao diện

#### 1. Navigation Bar
- **AI Malware Detection**: Logo và tên hệ thống
- **Kiểm tra File**: Link đến phần upload
- **Giới thiệu**: Thông tin về hệ thống
- **Status Indicator**: <PERSON><PERSON><PERSON> thị trạng thái hệ thống
  - 🟢 **Sẵn sàng**: Model đã load và sẵn sàng phân tích
  - 🟡 **Model chưa load**: Cần train model trước
  - 🔴 **Offline**: Hệ thống gặp lỗi

#### 2. Hero Section
- Giới thiệu tổng quan về hệ thống
- Nút "Bắt đầu kiểm tra" để chuyển đến phần upload

#### 3. Upload Section
- **File Input**: Chọn file PE để phân tích
- **Analyze Button**: Bắt đầu quá trình phân tích
- **Progress Bar**: Hiển thị tiến trình phân tích
- **Alert Messages**: Thông báo lỗi hoặc thành công

#### 4. Results Section
- Hiển thị kết quả phân tích chi tiết
- Thông tin file và hash values
- Đặc trưng kỹ thuật của file

## Cách sử dụng

### Phân tích File PE

1. **Chọn file**:
   - Click vào "Choose file" trong Upload Section
   - Chọn file PE (.exe, .dll, .sys, .scr, .com, .pif)
   - File tối đa 50MB

2. **Bắt đầu phân tích**:
   - Click nút "Phân tích File"
   - Hệ thống sẽ hiển thị progress bar
   - Chờ kết quả (thường 1-5 giây)

3. **Xem kết quả**:
   - Kết quả sẽ hiển thị trong Results Section
   - Bao gồm: prediction, confidence, file info, features

### Hiểu kết quả phân tích

#### Prediction Result
- **🛡️ FILE AN TOÀN (BENIGN)**: File được xác định là an toàn
- **🦠 MALWARE PHÁT HIỆN**: File được xác định là độc hại

#### Confidence Score
- **Cao (80-100%)**: 🟢 Kết quả rất tin cậy
- **Trung bình (60-79%)**: 🟡 Kết quả khá tin cậy
- **Thấp (0-59%)**: 🔴 Kết quả cần xem xét thêm

#### File Information
- **Tên file**: Tên file đã upload
- **Kích thước**: Dung lượng file
- **Thời gian phân tích**: Thời gian xử lý
- **Timestamp**: Thời điểm phân tích

#### Hash Values
- **MD5**: Hash MD5 của file
- **SHA256**: Hash SHA256 của file
- Dùng để tra cứu trên VirusTotal hoặc các dịch vụ khác

#### Đặc trưng File (Features)
- **File Entropy**: Độ ngẫu nhiên của dữ liệu (0-8)
- **Số Sections**: Số section trong PE file
- **Số DLL Import**: Số thư viện được import
- **Số Function Import**: Số function được import
- **Suspicious Imports**: Số import đáng nghi
- **Suspicious Strings**: Số string đáng nghi

## API Usage

### Endpoints

#### 1. GET /api/status
Kiểm tra trạng thái hệ thống

```bash
curl http://localhost:5000/api/status
```

Response:
```json
{
  "status": "online",
  "timestamp": "2025-07-15T10:30:00",
  "model_loaded": true,
  "version": "1.0.0"
}
```

#### 2. POST /api/upload
Upload và phân tích file

```bash
curl -X POST -F "file=@sample.exe" http://localhost:5000/api/upload
```

Response:
```json
{
  "success": true,
  "filename": "sample.exe",
  "file_size": 12345,
  "md5": "abc123...",
  "sha256": "def456...",
  "prediction": "benign",
  "confidence": 0.95,
  "malware_probability": 0.05,
  "benign_probability": 0.95,
  "analysis_time": 1.234,
  "timestamp": "2025-07-15T10:30:00",
  "features": {
    "file_entropy": 6.5,
    "section_count": 4,
    "import_dll_count": 8,
    "import_function_count": 45,
    "suspicious_import_count": 2,
    "suspicious_string_count": 1
  }
}
```

#### 3. GET /api/model/info
Thông tin về model

```bash
curl http://localhost:5000/api/model/info
```

Response:
```json
{
  "model_type": "random_forest",
  "is_trained": true,
  "feature_count": 20,
  "feature_names": ["file_size", "is_dll", ...],
  "top_features": [
    {"name": "file_entropy", "importance": 0.15},
    {"name": "suspicious_import_count", "importance": 0.12}
  ]
}
```

## Command Line Usage

### Training Model

```bash
# Tạo sample dataset
python src/models/train.py --create-sample

# Train với custom dataset
python src/models/train.py \
  --malware-dir data/samples/malware \
  --benign-dir data/samples/benign \
  --model-type random_forest \
  --output-dir data/models

# Train với existing dataset
python src/models/train.py \
  --dataset data/datasets/malware_dataset.csv \
  --model-type svm
```

### Batch Processing

```python
from src.feature_extraction.batch_processor import BatchProcessor

# Xử lý thư mục files
processor = BatchProcessor()
dataset = processor.create_dataset(
    malware_dir="data/samples/malware",
    benign_dir="data/samples/benign", 
    output_file="data/datasets/my_dataset.csv"
)

# Validate dataset
stats = processor.validate_dataset("data/datasets/my_dataset.csv")
print(stats)
```

### Single File Analysis

```python
from src.feature_extraction.pe_analyzer import PEAnalyzer
from src.models.classifier import MalwareClassifier

# Analyze single file
analyzer = PEAnalyzer()
features = analyzer.analyze_file("sample.exe")
print(features)

# Predict with trained model
classifier = MalwareClassifier()
classifier.load_model("data/models/random_forest_model.joblib", 
                     "data/models/random_forest_scaler.joblib")
result = classifier.predict("sample.exe")
print(result)
```

## Best Practices

### Sử dụng an toàn

1. **Chỉ phân tích file trong môi trường an toàn**
   - Sử dụng máy ảo hoặc sandbox
   - Không chạy file malware trên hệ thống chính

2. **Backup dữ liệu quan trọng**
   - Backup trước khi phân tích file lạ
   - Sử dụng antivirus song song

3. **Xác minh kết quả**
   - So sánh với VirusTotal
   - Sử dụng nhiều công cụ phân tích
   - Kiểm tra confidence score

### Tối ưu performance

1. **Batch processing cho nhiều files**
   ```python
   processor = BatchProcessor(n_workers=4)
   ```

2. **Sử dụng SSD cho database**
3. **Tăng RAM cho model lớn**
4. **Sử dụng GPU cho Deep Learning models**

### Cải thiện độ chính xác

1. **Cập nhật dataset thường xuyên**
2. **Retrain model với data mới**
3. **Fine-tune hyperparameters**
4. **Sử dụng ensemble methods**

## Troubleshooting

### Lỗi thường gặp

#### "Model chưa được load"
```bash
# Giải pháp: Train model trước
python src/models/train.py --create-sample
# Thêm malware samples vào data/samples/malware/
python src/models/train.py --malware-dir data/samples/malware --benign-dir data/samples/benign
```

#### "File extension không được hỗ trợ"
- Chỉ chấp nhận: .exe, .dll, .sys, .scr, .com, .pif
- Đổi tên file nếu cần thiết

#### "File quá lớn"
- Giới hạn mặc định: 50MB
- Thay đổi trong config.py: `MAX_FILE_SIZE`

#### "Không thể phân tích file"
- File có thể bị corrupt
- Không phải PE file hợp lệ
- Kiểm tra với PE viewer tools

### Performance Issues

#### Phân tích chậm
- Kiểm tra CPU/RAM usage
- Giảm số worker processes
- Sử dụng SSD

#### Web interface không responsive
- Kiểm tra network connection
- Clear browser cache
- Restart web server

## Advanced Features

### Custom Models

```python
# Train custom model
from sklearn.ensemble import GradientBoostingClassifier

classifier = MalwareClassifier()
classifier.model = GradientBoostingClassifier()
# Train và save model
```

### Feature Engineering

```python
# Thêm custom features
class CustomPEAnalyzer(PEAnalyzer):
    def _extract_custom_features(self, pe):
        # Implement custom feature extraction
        pass
```

### Integration

```python
# Integrate với existing systems
import requests

def scan_file_with_api(file_path):
    with open(file_path, 'rb') as f:
        response = requests.post(
            'http://localhost:5000/api/upload',
            files={'file': f}
        )
    return response.json()
```

## Support

- **Documentation**: Xem thư mục `docs/`
- **Tests**: Chạy `python run_tests.py`
- **Logs**: Kiểm tra `logs/malware_detection.log`
- **Database**: SQLite browser để xem `data/malware_detection.db`
