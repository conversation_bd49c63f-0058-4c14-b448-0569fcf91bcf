apiVersion: v1
kind: ConfigMap
metadata:
  name: prometheus-rules
  namespace: security-platform
  labels:
    app: prometheus
    component: alerting
data:
  security-platform-rules.yml: |
    groups:
    - name: security-platform.rules
      rules:
      
      # Application Health Alerts
      - alert: ServiceDown
        expr: up{job=~"malware-detection|network-ids|security-dashboard"} == 0
        for: 1m
        labels:
          severity: critical
          service: "{{ $labels.job }}"
        annotations:
          summary: "Service {{ $labels.job }} is down"
          description: "Service {{ $labels.job }} has been down for more than 1 minute."
          runbook_url: "https://docs.security-platform.com/runbooks/service-down"
      
      - alert: HighErrorRate
        expr: rate(flask_http_request_exceptions_total[5m]) > 0.1
        for: 2m
        labels:
          severity: warning
          service: "{{ $labels.job }}"
        annotations:
          summary: "High error rate on {{ $labels.job }}"
          description: "Error rate is {{ $value }} errors per second on {{ $labels.job }}"
      
      - alert: HighResponseTime
        expr: histogram_quantile(0.95, rate(flask_http_request_duration_seconds_bucket[5m])) > 2
        for: 5m
        labels:
          severity: warning
          service: "{{ $labels.job }}"
        annotations:
          summary: "High response time on {{ $labels.job }}"
          description: "95th percentile response time is {{ $value }}s on {{ $labels.job }}"
      
      # Security-specific Alerts
      - alert: HighMalwareDetectionRate
        expr: rate(malware_detections_total[5m]) > 10
        for: 2m
        labels:
          severity: critical
          category: security
        annotations:
          summary: "High malware detection rate"
          description: "Malware detection rate is {{ $value }} detections per second"
          action: "Investigate potential malware outbreak"
      
      - alert: NetworkIntrusionDetected
        expr: rate(network_intrusions_total[1m]) > 0
        for: 0m
        labels:
          severity: critical
          category: security
        annotations:
          summary: "Network intrusion detected"
          description: "{{ $value }} network intrusions detected in the last minute"
          action: "Immediate investigation required"
      
      - alert: AnomalousNetworkTraffic
        expr: rate(network_anomalies_total[5m]) > 5
        for: 3m
        labels:
          severity: warning
          category: security
        annotations:
          summary: "Anomalous network traffic detected"
          description: "{{ $value }} network anomalies per second detected"
          action: "Review network traffic patterns"
      
      - alert: FailedAuthenticationAttempts
        expr: rate(authentication_failures_total[5m]) > 1
        for: 2m
        labels:
          severity: warning
          category: security
        annotations:
          summary: "High failed authentication rate"
          description: "{{ $value }} failed authentication attempts per second"
          action: "Check for brute force attacks"
      
      # Infrastructure Alerts
      - alert: HighCPUUsage
        expr: rate(container_cpu_usage_seconds_total[5m]) * 100 > 80
        for: 5m
        labels:
          severity: warning
          category: infrastructure
        annotations:
          summary: "High CPU usage on {{ $labels.pod }}"
          description: "CPU usage is {{ $value }}% on pod {{ $labels.pod }}"
      
      - alert: HighMemoryUsage
        expr: (container_memory_usage_bytes / container_spec_memory_limit_bytes) * 100 > 85
        for: 5m
        labels:
          severity: warning
          category: infrastructure
        annotations:
          summary: "High memory usage on {{ $labels.pod }}"
          description: "Memory usage is {{ $value }}% on pod {{ $labels.pod }}"
      
      - alert: PodCrashLooping
        expr: rate(kube_pod_container_status_restarts_total[15m]) > 0
        for: 5m
        labels:
          severity: critical
          category: infrastructure
        annotations:
          summary: "Pod {{ $labels.pod }} is crash looping"
          description: "Pod {{ $labels.pod }} has restarted {{ $value }} times in the last 15 minutes"
      
      - alert: PersistentVolumeUsage
        expr: (kubelet_volume_stats_used_bytes / kubelet_volume_stats_capacity_bytes) * 100 > 85
        for: 5m
        labels:
          severity: warning
          category: storage
        annotations:
          summary: "High persistent volume usage"
          description: "PV {{ $labels.persistentvolumeclaim }} usage is {{ $value }}%"
      
      # Database Alerts
      - alert: DatabaseConnectionFailure
        expr: rate(database_connection_errors_total[5m]) > 0.1
        for: 2m
        labels:
          severity: critical
          category: database
        annotations:
          summary: "Database connection failures"
          description: "{{ $value }} database connection failures per second"
      
      - alert: SlowDatabaseQueries
        expr: histogram_quantile(0.95, rate(database_query_duration_seconds_bucket[5m])) > 1
        for: 5m
        labels:
          severity: warning
          category: database
        annotations:
          summary: "Slow database queries"
          description: "95th percentile query time is {{ $value }}s"
      
      # Model Performance Alerts
      - alert: ModelAccuracyDrop
        expr: model_accuracy < 0.85
        for: 10m
        labels:
          severity: warning
          category: ml-model
        annotations:
          summary: "ML model accuracy dropped"
          description: "Model {{ $labels.model_name }} accuracy is {{ $value }}"
          action: "Consider model retraining"
      
      - alert: ModelPredictionLatency
        expr: histogram_quantile(0.95, rate(model_prediction_duration_seconds_bucket[5m])) > 5
        for: 5m
        labels:
          severity: warning
          category: ml-model
        annotations:
          summary: "High model prediction latency"
          description: "95th percentile prediction time is {{ $value }}s for {{ $labels.model_name }}"
      
      # API Gateway Alerts
      - alert: APIGatewayDown
        expr: up{job="api-gateway"} == 0
        for: 1m
        labels:
          severity: critical
          category: gateway
        annotations:
          summary: "API Gateway is down"
          description: "API Gateway has been down for more than 1 minute"
      
      - alert: HighAPILatency
        expr: histogram_quantile(0.95, rate(nginx_http_request_duration_seconds_bucket[5m])) > 3
        for: 5m
        labels:
          severity: warning
          category: gateway
        annotations:
          summary: "High API Gateway latency"
          description: "95th percentile API response time is {{ $value }}s"
      
      # Kubernetes Cluster Alerts
      - alert: NodeNotReady
        expr: kube_node_status_condition{condition="Ready",status="true"} == 0
        for: 5m
        labels:
          severity: critical
          category: cluster
        annotations:
          summary: "Node {{ $labels.node }} is not ready"
          description: "Node {{ $labels.node }} has been not ready for more than 5 minutes"
      
      - alert: TooManyPods
        expr: kube_node_status_allocatable{resource="pods"} - kube_node_status_capacity{resource="pods"} < 5
        for: 5m
        labels:
          severity: warning
          category: cluster
        annotations:
          summary: "Node {{ $labels.node }} is running out of pod capacity"
          description: "Node {{ $labels.node }} has less than 5 available pod slots"
      
      # Storage Alerts
      - alert: PersistentVolumeClaimPending
        expr: kube_persistentvolumeclaim_status_phase{phase="Pending"} == 1
        for: 5m
        labels:
          severity: warning
          category: storage
        annotations:
          summary: "PVC {{ $labels.persistentvolumeclaim }} is pending"
          description: "PVC {{ $labels.persistentvolumeclaim }} has been pending for more than 5 minutes"
      
      # Network Alerts
      - alert: HighNetworkErrorRate
        expr: rate(container_network_receive_errors_total[5m]) > 0.01
        for: 5m
        labels:
          severity: warning
          category: network
        annotations:
          summary: "High network error rate on {{ $labels.pod }}"
          description: "Network error rate is {{ $value }} errors per second on {{ $labels.pod }}"
      
      # Business Logic Alerts
      - alert: LowThreatDetectionRate
        expr: rate(threats_detected_total[1h]) < 0.001
        for: 30m
        labels:
          severity: info
          category: business
        annotations:
          summary: "Unusually low threat detection rate"
          description: "Threat detection rate is {{ $value }} per second over the last hour"
          action: "Verify system is processing traffic correctly"
      
      - alert: FileUploadFailures
        expr: rate(file_upload_failures_total[5m]) > 0.1
        for: 2m
        labels:
          severity: warning
          category: business
        annotations:
          summary: "High file upload failure rate"
          description: "File upload failure rate is {{ $value }} failures per second"
      
      # Backup and Recovery Alerts
      - alert: BackupFailure
        expr: time() - backup_last_success_timestamp > 86400
        for: 0m
        labels:
          severity: critical
          category: backup
        annotations:
          summary: "Backup has not succeeded in 24 hours"
          description: "Last successful backup was {{ $value | humanizeDuration }} ago"
          action: "Check backup system immediately"
