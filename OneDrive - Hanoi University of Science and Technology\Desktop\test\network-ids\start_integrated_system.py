"""
Integrated System Startup Script
Script khởi động cả hai hệ thống bảo mật
"""

import sys
import os
import time
import subprocess
import threading
from pathlib import Path
import argparse
import logging


class IntegratedSystemManager:
    """Manager để khởi động và quản lý cả hai hệ thống"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.processes = {}
        self.base_dir = Path(__file__).parent
        self.malware_dir = self.base_dir.parent / "ai-malware-detection"
        
    def check_dependencies(self):
        """Kiểm tra dependencies và môi trường"""
        print("🔍 Checking system dependencies...")
        
        # Check Python version
        if sys.version_info < (3, 8):
            print("❌ Python 3.8+ required")
            return False
        
        print(f"✅ Python {sys.version_info.major}.{sys.version_info.minor}")
        
        # Check if malware detection system exists
        if not self.malware_dir.exists():
            print(f"❌ AI Malware Detection system not found at: {self.malware_dir}")
            return False
        
        print("✅ AI Malware Detection system found")
        
        # Check Network IDS structure
        required_dirs = [
            self.base_dir / "src",
            self.base_dir / "src" / "api",
            self.base_dir / "src" / "monitoring",
            self.base_dir / "data"
        ]
        
        for dir_path in required_dirs:
            if not dir_path.exists():
                print(f"❌ Required directory not found: {dir_path}")
                return False
        
        print("✅ Network IDS structure verified")
        
        return True
    
    def setup_environment(self):
        """Setup môi trường cho cả hai hệ thống"""
        print("⚙️  Setting up environment...")
        
        try:
            # Create necessary directories for Network IDS
            import config
            config.create_directories()
            print("✅ Network IDS directories created")
            
            # Check if malware detection models exist
            malware_models_dir = self.malware_dir / "data" / "models"
            if malware_models_dir.exists() and list(malware_models_dir.glob("*.joblib")):
                print("✅ Malware detection models found")
            else:
                print("⚠️  Malware detection models not found - will need training")
            
            # Check if Network IDS models exist
            network_models_dir = self.base_dir / "data" / "models"
            if network_models_dir.exists() and list(network_models_dir.glob("*.joblib")):
                print("✅ Network IDS models found")
            else:
                print("⚠️  Network IDS models not found - will need training")
            
            return True
            
        except Exception as e:
            print(f"❌ Environment setup failed: {e}")
            return False
    
    def start_malware_detection_api(self):
        """Khởi động Malware Detection API"""
        print("🚀 Starting AI Malware Detection API...")
        
        try:
            # Change to malware detection directory
            malware_api_script = self.malware_dir / "src" / "api" / "app.py"
            
            if not malware_api_script.exists():
                print(f"❌ Malware API script not found: {malware_api_script}")
                return False
            
            # Start malware detection API
            process = subprocess.Popen([
                sys.executable, str(malware_api_script)
            ], cwd=str(self.malware_dir))
            
            self.processes['malware_api'] = process
            
            # Wait a bit and check if process is still running
            time.sleep(3)
            if process.poll() is None:
                print("✅ AI Malware Detection API started (PID: {})".format(process.pid))
                return True
            else:
                print("❌ AI Malware Detection API failed to start")
                return False
                
        except Exception as e:
            print(f"❌ Error starting Malware Detection API: {e}")
            return False
    
    def start_network_ids_api(self):
        """Khởi động Network IDS API"""
        print("🚀 Starting Network IDS API...")
        
        try:
            # Start Network IDS API
            ids_api_script = self.base_dir / "src" / "api" / "ids_api.py"
            
            if not ids_api_script.exists():
                print(f"❌ Network IDS API script not found: {ids_api_script}")
                return False
            
            process = subprocess.Popen([
                sys.executable, str(ids_api_script)
            ], cwd=str(self.base_dir))
            
            self.processes['network_ids_api'] = process
            
            # Wait a bit and check if process is still running
            time.sleep(3)
            if process.poll() is None:
                print("✅ Network IDS API started (PID: {})".format(process.pid))
                return True
            else:
                print("❌ Network IDS API failed to start")
                return False
                
        except Exception as e:
            print(f"❌ Error starting Network IDS API: {e}")
            return False
    
    def train_models_if_needed(self):
        """Train models nếu chưa có"""
        print("🧠 Checking and training models if needed...")
        
        try:
            # Train malware detection models
            malware_models_dir = self.malware_dir / "data" / "models"
            if not malware_models_dir.exists() or not list(malware_models_dir.glob("*.joblib")):
                print("🔄 Training malware detection models...")
                
                train_script = self.malware_dir / "src" / "models" / "train.py"
                if train_script.exists():
                    result = subprocess.run([
                        sys.executable, str(train_script), "--create-sample"
                    ], cwd=str(self.malware_dir), capture_output=True, text=True)
                    
                    if result.returncode == 0:
                        print("✅ Malware detection models trained")
                    else:
                        print(f"⚠️  Malware model training had issues: {result.stderr}")
                else:
                    print("⚠️  Malware training script not found")
            
            # Train Network IDS models
            network_models_dir = self.base_dir / "data" / "models"
            if not network_models_dir.exists() or not list(network_models_dir.glob("*.joblib")):
                print("🔄 Training Network IDS models...")
                
                train_script = self.base_dir / "src" / "ml_models" / "train_models.py"
                if train_script.exists():
                    # Create sample dataset first
                    subprocess.run([
                        sys.executable, str(train_script), "--create-sample"
                    ], cwd=str(self.base_dir))
                    
                    # Train both models
                    result = subprocess.run([
                        sys.executable, str(train_script), "--model", "both"
                    ], cwd=str(self.base_dir), capture_output=True, text=True)
                    
                    if result.returncode == 0:
                        print("✅ Network IDS models trained")
                    else:
                        print(f"⚠️  Network IDS model training had issues: {result.stderr}")
                else:
                    print("⚠️  Network IDS training script not found")
            
            return True
            
        except Exception as e:
            print(f"❌ Error in model training: {e}")
            return False
    
    def wait_for_apis(self):
        """Đợi APIs khởi động hoàn toàn"""
        print("⏳ Waiting for APIs to be ready...")
        
        import requests
        
        # Wait for malware detection API
        for i in range(30):  # Wait up to 30 seconds
            try:
                response = requests.get('http://localhost:5000/api/status', timeout=2)
                if response.status_code == 200:
                    print("✅ Malware Detection API is ready")
                    break
            except:
                pass
            time.sleep(1)
        else:
            print("⚠️  Malware Detection API not responding")
        
        # Wait for Network IDS API
        for i in range(30):  # Wait up to 30 seconds
            try:
                response = requests.get('http://localhost:5001/api/status', timeout=2)
                if response.status_code == 200:
                    print("✅ Network IDS API is ready")
                    break
            except:
                pass
            time.sleep(1)
        else:
            print("⚠️  Network IDS API not responding")
    
    def show_system_info(self):
        """Hiển thị thông tin hệ thống"""
        print("\n" + "="*60)
        print("🎉 INTEGRATED SECURITY SYSTEM STARTED")
        print("="*60)
        print("🌐 Web Interfaces:")
        print("   • AI Malware Detection: http://localhost:5000")
        print("   • Network IDS Dashboard: http://localhost:5001")
        print("\n📡 API Endpoints:")
        print("   • Malware Detection API: http://localhost:5000/api/")
        print("   • Network IDS API: http://localhost:5001/api/")
        print("\n🔧 Management:")
        print("   • Press Ctrl+C to stop all services")
        print("   • Check logs in respective log directories")
        print("="*60)
    
    def start_integrated_system(self, train_models=False):
        """Khởi động hệ thống tích hợp"""
        print("🚀 Starting Integrated Security System...")
        print("="*50)
        
        try:
            # Check dependencies
            if not self.check_dependencies():
                return False
            
            # Setup environment
            if not self.setup_environment():
                return False
            
            # Train models if requested
            if train_models:
                if not self.train_models_if_needed():
                    print("⚠️  Model training had issues, continuing anyway...")
            
            # Start APIs
            malware_started = self.start_malware_detection_api()
            time.sleep(2)  # Give it a moment
            
            network_started = self.start_network_ids_api()
            
            if not malware_started and not network_started:
                print("❌ Failed to start any APIs")
                return False
            
            # Wait for APIs to be ready
            self.wait_for_apis()
            
            # Show system info
            self.show_system_info()
            
            return True
            
        except Exception as e:
            print(f"❌ Error starting integrated system: {e}")
            return False
    
    def stop_all_processes(self):
        """Dừng tất cả processes"""
        print("\n⏹️  Stopping all services...")
        
        for name, process in self.processes.items():
            try:
                if process.poll() is None:  # Process is still running
                    print(f"   Stopping {name}...")
                    process.terminate()
                    
                    # Wait for graceful shutdown
                    try:
                        process.wait(timeout=5)
                    except subprocess.TimeoutExpired:
                        print(f"   Force killing {name}...")
                        process.kill()
                        process.wait()
                    
                    print(f"   ✅ {name} stopped")
            except Exception as e:
                print(f"   ❌ Error stopping {name}: {e}")
        
        print("🏁 All services stopped")
    
    def run_and_wait(self, train_models=False):
        """Chạy hệ thống và đợi user interrupt"""
        if self.start_integrated_system(train_models):
            try:
                # Keep running until interrupted
                while True:
                    time.sleep(1)
                    
                    # Check if any process died
                    for name, process in self.processes.items():
                        if process.poll() is not None:
                            print(f"⚠️  {name} process died unexpectedly")
                    
            except KeyboardInterrupt:
                print("\n👋 Shutdown requested by user")
            finally:
                self.stop_all_processes()
        else:
            print("❌ Failed to start integrated system")


def main():
    """Main function"""
    parser = argparse.ArgumentParser(description='Integrated Security System Startup')
    parser.add_argument('--train-models', action='store_true',
                       help='Train ML models if not present')
    parser.add_argument('--verbose', '-v', action='store_true',
                       help='Enable verbose logging')
    
    args = parser.parse_args()
    
    # Setup logging
    log_level = logging.DEBUG if args.verbose else logging.INFO
    logging.basicConfig(
        level=log_level,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    # Start system
    manager = IntegratedSystemManager()
    manager.run_and_wait(train_models=args.train_models)


if __name__ == "__main__":
    main()
