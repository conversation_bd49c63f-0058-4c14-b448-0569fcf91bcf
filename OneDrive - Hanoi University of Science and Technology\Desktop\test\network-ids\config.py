"""
Configuration file cho Network Intrusion Detection System
"""

import os
from pathlib import Path

# Base directory
BASE_DIR = Path(__file__).parent.absolute()

# Database configuration
DATABASE_URL = f"sqlite:///{BASE_DIR}/data/network_ids.db"

# Model directories
MODEL_DIR = BASE_DIR / "data" / "models"
ANOMALY_MODEL_PATH = MODEL_DIR / "anomaly_detector.joblib"
INTRUSION_MODEL_PATH = MODEL_DIR / "intrusion_classifier.joblib"
SCALER_PATH = MODEL_DIR / "feature_scaler.joblib"

# Data directories
DATA_DIR = BASE_DIR / "data"
PCAP_DIR = DATA_DIR / "pcap"
DATASETS_DIR = DATA_DIR / "datasets"
LOGS_DIR = BASE_DIR / "logs"

# Network configuration
NETWORK_CONFIG = {
    'default_interface': 'Wi-Fi',  # Windows default
    'promiscuous_mode': False,  # Set to False for Windows compatibility
    'buffer_size': 65536,
    'timeout': 1000,  # milliseconds
    'max_packets': 10000,  # Maximum packets to capture in one session
    'packet_filter': '',  # BPF filter (empty = capture all)
}

# Traffic analysis settings
TRAFFIC_CONFIG = {
    'feature_window': 60,  # seconds
    'flow_timeout': 300,  # seconds
    'max_flows': 100000,
    'sampling_rate': 1.0,  # 1.0 = capture all packets
    'protocols': ['TCP', 'UDP', 'ICMP', 'HTTP', 'DNS', 'HTTPS']
}

# Machine Learning configuration
ML_CONFIG = {
    'anomaly_algorithm': 'isolation_forest',  # 'isolation_forest', 'one_class_svm', 'autoencoder'
    'intrusion_algorithm': 'random_forest',  # 'random_forest', 'svm', 'neural_network'
    'test_size': 0.2,
    'random_state': 42,
    'cv_folds': 5,
    'model_update_interval': 3600,  # seconds (1 hour)
}

# Anomaly Detection settings
ANOMALY_CONFIG = {
    'contamination': 0.1,  # Expected proportion of anomalies
    'threshold': 0.8,
    'window_size': 100,  # Number of packets to analyze together
    'min_samples': 1000,  # Minimum samples needed for training
}

# Intrusion Detection settings
INTRUSION_CONFIG = {
    'confidence_threshold': 0.7,
    'attack_types': [
        'normal', 'dos', 'probe', 'r2l', 'u2r',
        'port_scan', 'brute_force', 'sql_injection'
    ],
    'feature_importance_threshold': 0.01
}

# Real-time monitoring
MONITORING_CONFIG = {
    'update_interval': 1,  # seconds
    'alert_cooldown': 300,  # seconds (5 minutes)
    'max_alerts_per_minute': 10,
    'statistics_window': 3600,  # seconds (1 hour)
    'enable_real_time': True
}

# Alert system configuration
ALERT_CONFIG = {
    'enabled': True,
    'levels': {
        'critical': {'threshold': 0.9, 'color': '#dc3545'},
        'high': {'threshold': 0.8, 'color': '#fd7e14'},
        'medium': {'threshold': 0.6, 'color': '#ffc107'},
        'low': {'threshold': 0.4, 'color': '#28a745'}
    },
    'email': {
        'enabled': False,
        'smtp_server': 'smtp.gmail.com',
        'smtp_port': 587,
        'username': '',
        'password': '',
        'recipients': []
    },
    'slack': {
        'enabled': False,
        'webhook_url': '',
        'channel': '#security-alerts'
    },
    'sms': {
        'enabled': False,
        'twilio_sid': '',
        'twilio_token': '',
        'phone_numbers': []
    }
}

# Web application settings
FLASK_CONFIG = {
    'SECRET_KEY': 'network-ids-secret-key-change-in-production',
    'DEBUG': True,
    'HOST': '0.0.0.0',
    'PORT': 5001,  # Different from malware detection (5000)
    'THREADED': True
}

# Integration với AI Malware Detection System
INTEGRATION_CONFIG = {
    'malware_detection_api': 'http://localhost:5000',
    'enabled': True,
    'timeout': 30,  # seconds
    'retry_attempts': 3,
    'shared_database': True
}

# Logging configuration
LOGGING_CONFIG = {
    'level': 'INFO',
    'format': '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    'log_file': LOGS_DIR / 'network_ids.log',
    'max_file_size': 10 * 1024 * 1024,  # 10MB
    'backup_count': 5,
    'console_output': True
}

# Performance settings
PERFORMANCE_CONFIG = {
    'max_memory_usage': 1024 * 1024 * 1024,  # 1GB
    'max_cpu_usage': 80,  # percentage
    'cleanup_interval': 3600,  # seconds
    'max_log_size': 100 * 1024 * 1024,  # 100MB
    'enable_profiling': False
}

# Security settings
SECURITY_CONFIG = {
    'enable_encryption': False,
    'api_key_required': False,
    'rate_limiting': {
        'enabled': True,
        'requests_per_minute': 100
    },
    'data_anonymization': True,
    'audit_logging': True
}

# Dashboard configuration
DASHBOARD_CONFIG = {
    'refresh_interval': 5,  # seconds
    'max_chart_points': 1000,
    'default_time_range': 3600,  # seconds (1 hour)
    'enable_live_updates': True,
    'charts': {
        'traffic_volume': True,
        'protocol_distribution': True,
        'top_talkers': True,
        'alert_timeline': True,
        'attack_types': True
    }
}

# Feature extraction settings
FEATURE_CONFIG = {
    'network_features': [
        'packet_size', 'inter_arrival_time', 'protocol_type',
        'src_port', 'dst_port', 'tcp_flags', 'packet_count',
        'byte_count', 'duration', 'packets_per_second'
    ],
    'statistical_features': [
        'mean', 'std', 'min', 'max', 'median',
        'percentile_25', 'percentile_75', 'skewness', 'kurtosis'
    ],
    'time_window_features': True,
    'flow_features': True,
    'payload_features': False  # Set to True if deep packet inspection needed
}

# Dataset configuration
DATASET_CONFIG = {
    'kdd_cup_99': {
        'url': 'http://kdd.ics.uci.edu/databases/kddcup99/kddcup.data_10_percent.gz',
        'local_path': DATASETS_DIR / 'kdd_cup_99.csv'
    },
    'nsl_kdd': {
        'url': 'https://www.unb.ca/cic/datasets/nsl.html',
        'local_path': DATASETS_DIR / 'nsl_kdd.csv'
    },
    'cicids2017': {
        'url': 'https://www.unb.ca/cic/datasets/ids-2017.html',
        'local_path': DATASETS_DIR / 'cicids2017.csv'
    }
}


def create_directories():
    """Tạo các thư mục cần thiết nếu chưa tồn tại"""
    directories = [
        MODEL_DIR,
        PCAP_DIR,
        DATASETS_DIR,
        LOGS_DIR,
        BASE_DIR / 'uploads',
        BASE_DIR / 'temp'
    ]
    
    for directory in directories:
        directory.mkdir(parents=True, exist_ok=True)
    
    print("✅ Đã tạo các thư mục cần thiết cho Network IDS!")


def validate_config():
    """Kiểm tra tính hợp lệ của configuration"""
    errors = []
    
    # Check network interface
    try:
        import psutil
        interfaces = psutil.net_if_addrs().keys()
        if NETWORK_CONFIG['default_interface'] not in interfaces:
            errors.append(f"Network interface '{NETWORK_CONFIG['default_interface']}' not found")
    except ImportError:
        errors.append("psutil not installed - cannot validate network interface")
    
    # Check database path
    if not DATABASE_URL.startswith('sqlite://'):
        errors.append("Only SQLite database is currently supported")
    
    # Check required directories
    required_dirs = [MODEL_DIR, DATA_DIR, LOGS_DIR]
    for dir_path in required_dirs:
        if not dir_path.parent.exists():
            errors.append(f"Parent directory does not exist: {dir_path.parent}")
    
    return errors


def get_network_interfaces():
    """Lấy danh sách network interfaces có sẵn"""
    try:
        import psutil
        interfaces = []
        for interface, addrs in psutil.net_if_addrs().items():
            for addr in addrs:
                if addr.family == 2:  # IPv4
                    interfaces.append({
                        'name': interface,
                        'ip': addr.address,
                        'netmask': addr.netmask
                    })
                    break
        return interfaces
    except ImportError:
        return [{'name': 'eth0', 'ip': '0.0.0.0', 'netmask': '*************'}]


if __name__ == "__main__":
    # Tạo directories
    create_directories()
    
    # Validate configuration
    errors = validate_config()
    if errors:
        print("⚠️  Configuration errors found:")
        for error in errors:
            print(f"   - {error}")
    else:
        print("✅ Configuration validated successfully!")
    
    # Show available network interfaces
    print("\n🌐 Available network interfaces:")
    interfaces = get_network_interfaces()
    for interface in interfaces:
        print(f"   - {interface['name']}: {interface['ip']}")
    
    print(f"\n📁 Base directory: {BASE_DIR}")
    print(f"💾 Database: {DATABASE_URL}")
    print(f"🌐 Web server: http://{FLASK_CONFIG['HOST']}:{FLASK_CONFIG['PORT']}")
    print(f"🔗 Malware detection integration: {INTEGRATION_CONFIG['malware_detection_api']}")
