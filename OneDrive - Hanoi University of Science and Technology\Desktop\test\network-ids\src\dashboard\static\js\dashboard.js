// Network IDS Dashboard JavaScript

class NetworkIDSDashboard {
    constructor() {
        this.isMonitoring = false;
        this.charts = {};
        this.updateInterval = null;
        this.alertSeverityFilter = '';
        
        this.init();
    }
    
    init() {
        this.setupEventListeners();
        this.initializeCharts();
        this.loadInterfaces();
        this.checkSystemStatus();
        this.startPeriodicUpdates();
    }
    
    setupEventListeners() {
        // Monitoring controls
        document.getElementById('start-monitoring').addEventListener('click', () => {
            this.startMonitoring();
        });
        
        document.getElementById('stop-monitoring').addEventListener('click', () => {
            this.stopMonitoring();
        });
        
        // Alert severity filter
        document.getElementById('alert-severity-filter').addEventListener('change', (e) => {
            this.alertSeverityFilter = e.target.value;
            this.loadAlerts();
        });
        
        // Model training
        document.getElementById('train-models').addEventListener('click', () => {
            this.showTrainingModal();
        });
        
        document.getElementById('start-training').addEventListener('click', () => {
            this.startTraining();
        });
    }
    
    initializeCharts() {
        // Traffic Volume Chart
        const trafficCtx = document.getElementById('traffic-chart').getContext('2d');
        this.charts.traffic = new Chart(trafficCtx, {
            type: 'line',
            data: {
                labels: [],
                datasets: [{
                    label: 'Packets/sec',
                    data: [],
                    borderColor: '#007bff',
                    backgroundColor: 'rgba(0, 123, 255, 0.1)',
                    tension: 0.4,
                    fill: true
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    y: {
                        beginAtZero: true
                    }
                },
                plugins: {
                    legend: {
                        display: false
                    }
                }
            }
        });
        
        // Protocol Distribution Chart
        const protocolCtx = document.getElementById('protocol-chart').getContext('2d');
        this.charts.protocol = new Chart(protocolCtx, {
            type: 'doughnut',
            data: {
                labels: ['TCP', 'UDP', 'ICMP', 'Other'],
                datasets: [{
                    data: [0, 0, 0, 0],
                    backgroundColor: [
                        '#007bff',
                        '#28a745',
                        '#ffc107',
                        '#dc3545'
                    ]
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'bottom'
                    }
                }
            }
        });
    }
    
    async loadInterfaces() {
        try {
            const response = await fetch('/api/interfaces');
            const data = await response.json();
            
            const select = document.getElementById('interface-select');
            select.innerHTML = '';
            
            data.interfaces.forEach(iface => {
                const option = document.createElement('option');
                option.value = iface.name;
                option.textContent = `${iface.name} (${iface.addresses[0]?.address || 'N/A'})`;
                if (iface.name === data.default) {
                    option.selected = true;
                }
                select.appendChild(option);
            });
        } catch (error) {
            console.error('Error loading interfaces:', error);
            this.showError('Failed to load network interfaces');
        }
    }
    
    async checkSystemStatus() {
        try {
            const response = await fetch('/api/status');
            const data = await response.json();
            
            const statusIndicator = document.getElementById('status-indicator');
            const statusText = document.getElementById('status-text');
            
            if (data.status === 'online') {
                statusIndicator.className = 'fas fa-circle text-success';
                statusText.textContent = 'Online';
                
                // Update monitoring status
                this.isMonitoring = data.components.network_monitor;
                this.updateMonitoringControls();
                
                // Update model status
                this.updateModelStatus(data.components.models_loaded);
            } else {
                statusIndicator.className = 'fas fa-circle text-danger';
                statusText.textContent = 'Offline';
            }
        } catch (error) {
            console.error('Error checking system status:', error);
            const statusIndicator = document.getElementById('status-indicator');
            const statusText = document.getElementById('status-text');
            statusIndicator.className = 'fas fa-circle text-danger';
            statusText.textContent = 'Error';
        }
    }
    
    async startMonitoring() {
        const interface = document.getElementById('interface-select').value;
        
        try {
            const response = await fetch('/api/monitoring/start', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ interface })
            });
            
            const data = await response.json();
            
            if (data.success) {
                this.isMonitoring = true;
                this.updateMonitoringControls();
                this.showSuccess('Monitoring started successfully');
            } else {
                this.showError(data.error || 'Failed to start monitoring');
            }
        } catch (error) {
            console.error('Error starting monitoring:', error);
            this.showError('Failed to start monitoring');
        }
    }
    
    async stopMonitoring() {
        try {
            const response = await fetch('/api/monitoring/stop', {
                method: 'POST'
            });
            
            const data = await response.json();
            
            if (data.success) {
                this.isMonitoring = false;
                this.updateMonitoringControls();
                this.showSuccess('Monitoring stopped successfully');
            } else {
                this.showError(data.error || 'Failed to stop monitoring');
            }
        } catch (error) {
            console.error('Error stopping monitoring:', error);
            this.showError('Failed to stop monitoring');
        }
    }
    
    updateMonitoringControls() {
        const startBtn = document.getElementById('start-monitoring');
        const stopBtn = document.getElementById('stop-monitoring');
        
        if (this.isMonitoring) {
            startBtn.disabled = true;
            stopBtn.disabled = false;
        } else {
            startBtn.disabled = false;
            stopBtn.disabled = true;
        }
    }
    
    async loadStatistics() {
        try {
            const response = await fetch('/api/monitoring/stats');
            const data = await response.json();
            
            // Update statistics cards
            document.getElementById('total-packets').textContent = 
                data.network.total_packets || 0;
            document.getElementById('active-alerts').textContent = 
                data.alerts.total_alerts || 0;
            document.getElementById('anomalies-detected').textContent = 
                data.detection.anomalies_detected || 0;
            document.getElementById('intrusions-detected').textContent = 
                data.detection.intrusions_detected || 0;
            
            // Update charts
            this.updateTrafficChart(data.network);
            this.updateProtocolChart(data.network.protocol_distribution || {});
            
            // Update top talkers
            this.updateTopTalkers(data.network.top_talkers || {});
            
        } catch (error) {
            console.error('Error loading statistics:', error);
        }
    }
    
    updateTrafficChart(networkData) {
        if (!networkData.packets_per_second) return;
        
        const chart = this.charts.traffic;
        const now = new Date().toLocaleTimeString();
        
        // Add new data point
        chart.data.labels.push(now);
        chart.data.datasets[0].data.push(networkData.packets_per_second);
        
        // Keep only last 20 data points
        if (chart.data.labels.length > 20) {
            chart.data.labels.shift();
            chart.data.datasets[0].data.shift();
        }
        
        chart.update('none');
    }
    
    updateProtocolChart(protocolData) {
        const chart = this.charts.protocol;
        const total = Object.values(protocolData).reduce((sum, count) => sum + count, 0);
        
        if (total === 0) return;
        
        chart.data.datasets[0].data = [
            protocolData.TCP || 0,
            protocolData.UDP || 0,
            protocolData.ICMP || 0,
            total - (protocolData.TCP || 0) - (protocolData.UDP || 0) - (protocolData.ICMP || 0)
        ];
        
        chart.update('none');
    }
    
    updateTopTalkers(topTalkersData) {
        const container = document.getElementById('top-talkers-list');
        
        if (Object.keys(topTalkersData).length === 0) {
            container.innerHTML = '<p class="text-muted">No data available</p>';
            return;
        }
        
        const sortedTalkers = Object.entries(topTalkersData)
            .sort(([,a], [,b]) => b - a)
            .slice(0, 10);
        
        container.innerHTML = sortedTalkers.map(([ip, count]) => `
            <div class="top-talker-item">
                <span class="top-talker-ip">${ip}</span>
                <span class="top-talker-count">${count}</span>
            </div>
        `).join('');
    }
    
    async loadAlerts() {
        try {
            const params = new URLSearchParams();
            if (this.alertSeverityFilter) {
                params.append('severity', this.alertSeverityFilter);
            }
            params.append('limit', '50');
            
            const response = await fetch(`/api/alerts?${params}`);
            const data = await response.json();
            
            this.updateAlertsTable(data.alerts || []);
        } catch (error) {
            console.error('Error loading alerts:', error);
        }
    }
    
    updateAlertsTable(alerts) {
        const tbody = document.getElementById('alerts-table-body');
        
        if (alerts.length === 0) {
            tbody.innerHTML = '<tr><td colspan="6" class="text-center">No alerts</td></tr>';
            return;
        }
        
        tbody.innerHTML = alerts.map(alert => `
            <tr class="${alert.acknowledged ? 'table-secondary' : ''}">
                <td>${new Date(alert.timestamp).toLocaleString()}</td>
                <td>${alert.type}</td>
                <td><span class="severity-${alert.severity}">${alert.severity.toUpperCase()}</span></td>
                <td class="text-monospace">${alert.source_ip}</td>
                <td>${alert.message}</td>
                <td>
                    ${!alert.acknowledged ? 
                        `<button class="btn btn-sm btn-outline-primary" onclick="dashboard.acknowledgeAlert('${alert.id}')">
                            <i class="fas fa-check"></i>
                        </button>` : 
                        '<span class="text-muted">Acknowledged</span>'
                    }
                </td>
            </tr>
        `).join('');
    }
    
    async acknowledgeAlert(alertId) {
        try {
            const response = await fetch(`/api/alerts/${alertId}/acknowledge`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    acknowledged_by: 'dashboard_user'
                })
            });
            
            const data = await response.json();
            
            if (data.success) {
                this.showSuccess('Alert acknowledged');
                this.loadAlerts(); // Refresh alerts table
            } else {
                this.showError(data.error || 'Failed to acknowledge alert');
            }
        } catch (error) {
            console.error('Error acknowledging alert:', error);
            this.showError('Failed to acknowledge alert');
        }
    }
    
    updateModelStatus(modelsLoaded) {
        const anomalyStatus = document.getElementById('anomaly-model-status');
        const intrusionStatus = document.getElementById('intrusion-model-status');
        
        // Update anomaly model status
        if (modelsLoaded.anomaly_detector) {
            anomalyStatus.className = 'fas fa-circle text-success me-2';
        } else {
            anomalyStatus.className = 'fas fa-circle text-danger me-2';
        }
        
        // Update intrusion model status
        if (modelsLoaded.intrusion_classifier) {
            intrusionStatus.className = 'fas fa-circle text-success me-2';
        } else {
            intrusionStatus.className = 'fas fa-circle text-danger me-2';
        }
    }
    
    showTrainingModal() {
        const modal = new bootstrap.Modal(document.getElementById('training-modal'));
        modal.show();
    }
    
    async startTraining() {
        const modelType = document.getElementById('model-type').value;
        const algorithm = document.getElementById('algorithm').value;
        
        const progressDiv = document.getElementById('training-progress');
        const startBtn = document.getElementById('start-training');
        
        progressDiv.classList.remove('d-none');
        startBtn.disabled = true;
        
        try {
            const response = await fetch('/api/models/train', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    model_type: modelType,
                    algorithm: algorithm || undefined
                })
            });
            
            const data = await response.json();
            
            if (data.success) {
                this.showSuccess('Models trained successfully');
                this.checkSystemStatus(); // Refresh model status
            } else {
                this.showError('Model training failed');
            }
        } catch (error) {
            console.error('Error training models:', error);
            this.showError('Model training failed');
        } finally {
            progressDiv.classList.add('d-none');
            startBtn.disabled = false;
            
            // Close modal
            const modal = bootstrap.Modal.getInstance(document.getElementById('training-modal'));
            modal.hide();
        }
    }
    
    startPeriodicUpdates() {
        // Update every 5 seconds
        this.updateInterval = setInterval(() => {
            this.loadStatistics();
            this.loadAlerts();
        }, 5000);
        
        // Initial load
        this.loadStatistics();
        this.loadAlerts();
    }
    
    showSuccess(message) {
        this.showToast(message, 'success');
    }
    
    showError(message) {
        this.showToast(message, 'danger');
    }
    
    showToast(message, type) {
        // Create toast element
        const toast = document.createElement('div');
        toast.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
        toast.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
        toast.innerHTML = `
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;
        
        document.body.appendChild(toast);
        
        // Auto remove after 5 seconds
        setTimeout(() => {
            if (toast.parentNode) {
                toast.parentNode.removeChild(toast);
            }
        }, 5000);
    }
}

// Initialize dashboard when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.dashboard = new NetworkIDSDashboard();
});
