apiVersion: v1
kind: ServiceAccount
metadata:
  name: security-platform-sa
  namespace: security-platform
  labels:
    app: security-platform
    component: service-account
  annotations:
    iam.gke.io/gcp-service-account: <EMAIL>
---
apiVersion: rbac.authorization.k8s.io/v1
kind: Role
metadata:
  namespace: security-platform
  name: security-platform-role
  labels:
    app: security-platform
    component: rbac
rules:
# Allow reading ConfigMaps and Secrets
- apiGroups: [""]
  resources: ["configmaps", "secrets"]
  verbs: ["get", "list", "watch"]
# Allow reading and updating PersistentVolumeClaims
- apiGroups: [""]
  resources: ["persistentvolumeclaims"]
  verbs: ["get", "list", "watch", "update"]
# Allow reading Services and Endpoints
- apiGroups: [""]
  resources: ["services", "endpoints"]
  verbs: ["get", "list", "watch"]
# Allow reading Pods (for health checks and monitoring)
- apiGroups: [""]
  resources: ["pods"]
  verbs: ["get", "list", "watch"]
# Allow reading Events
- apiGroups: [""]
  resources: ["events"]
  verbs: ["get", "list", "watch"]
---
apiVersion: rbac.authorization.k8s.io/v1
kind: RoleBinding
metadata:
  name: security-platform-rolebinding
  namespace: security-platform
  labels:
    app: security-platform
    component: rbac
subjects:
- kind: ServiceAccount
  name: security-platform-sa
  namespace: security-platform
roleRef:
  kind: Role
  name: security-platform-role
  apiGroup: rbac.authorization.k8s.io
---
# ClusterRole for monitoring and metrics collection
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: security-platform-monitoring
  labels:
    app: security-platform
    component: monitoring
rules:
# Allow reading nodes and node metrics
- apiGroups: [""]
  resources: ["nodes", "nodes/metrics", "nodes/stats"]
  verbs: ["get", "list", "watch"]
# Allow reading pod metrics
- apiGroups: [""]
  resources: ["pods", "pods/log"]
  verbs: ["get", "list", "watch"]
# Allow reading metrics from metrics server
- apiGroups: ["metrics.k8s.io"]
  resources: ["nodes", "pods"]
  verbs: ["get", "list"]
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name: security-platform-monitoring-binding
  labels:
    app: security-platform
    component: monitoring
subjects:
- kind: ServiceAccount
  name: security-platform-sa
  namespace: security-platform
roleRef:
  kind: ClusterRole
  name: security-platform-monitoring
  apiGroup: rbac.authorization.k8s.io
---
# ServiceAccount for monitoring components
apiVersion: v1
kind: ServiceAccount
metadata:
  name: monitoring-sa
  namespace: security-platform
  labels:
    app: security-platform
    component: monitoring
---
# Role for monitoring components
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: monitoring-cluster-role
  labels:
    app: security-platform
    component: monitoring
rules:
# Prometheus needs to scrape metrics from all pods
- apiGroups: [""]
  resources: ["nodes", "nodes/proxy", "services", "endpoints", "pods"]
  verbs: ["get", "list", "watch"]
- apiGroups: ["extensions"]
  resources: ["ingresses"]
  verbs: ["get", "list", "watch"]
- apiGroups: [""]
  resources: ["configmaps"]
  verbs: ["get"]
- nonResourceURLs: ["/metrics"]
  verbs: ["get"]
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name: monitoring-cluster-role-binding
  labels:
    app: security-platform
    component: monitoring
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: monitoring-cluster-role
subjects:
- kind: ServiceAccount
  name: monitoring-sa
  namespace: security-platform
---
# Network Policy to secure the namespace
apiVersion: networking.k8s.io/v1
kind: NetworkPolicy
metadata:
  name: security-platform-network-policy
  namespace: security-platform
  labels:
    app: security-platform
    component: network-policy
spec:
  podSelector: {}
  policyTypes:
  - Ingress
  - Egress
  ingress:
  # Allow ingress from API Gateway
  - from:
    - podSelector:
        matchLabels:
          app: api-gateway
    ports:
    - protocol: TCP
      port: 5000  # Malware Detection
    - protocol: TCP
      port: 5001  # Network IDS
    - protocol: TCP
      port: 8080  # Dashboard
  # Allow ingress from monitoring
  - from:
    - podSelector:
        matchLabels:
          component: monitoring
    ports:
    - protocol: TCP
      port: 9090  # Metrics
  # Allow inter-service communication
  - from:
    - podSelector: {}
    ports:
    - protocol: TCP
      port: 5000
    - protocol: TCP
      port: 5001
    - protocol: TCP
      port: 8080
    - protocol: TCP
      port: 6379  # Redis
    - protocol: TCP
      port: 5432  # PostgreSQL
  egress:
  # Allow egress to Cloud SQL
  - to: []
    ports:
    - protocol: TCP
      port: 5432
  # Allow egress to Google APIs
  - to: []
    ports:
    - protocol: TCP
      port: 443
  # Allow egress to Redis
  - to:
    - podSelector:
        matchLabels:
          app: redis
    ports:
    - protocol: TCP
      port: 6379
  # Allow DNS resolution
  - to: []
    ports:
    - protocol: UDP
      port: 53
    - protocol: TCP
      port: 53
