# Hướng dẫn Cài đặt - AI Malware Detection System

## Y<PERSON><PERSON> c<PERSON>u <PERSON> thống

### Phần cứng
- **RAM**: T<PERSON><PERSON> thiểu 4GB, khuyến nghị 8GB+
- **CPU**: Dual-core 2.0GHz+, khuyến nghị Quad-core
- **Ổ cứng**: 2GB dung lượng trống
- **GPU**: <PERSON>h<PERSON><PERSON> bắt buộc (chỉ cần cho Deep Learning models)

### Phần mềm
- **Hệ điều hành**: Windows 10/11 (khuyến nghị)
- **Python**: 3.8 hoặc mới hơn
- **Git**: Để clone repository (tùy chọn)

## Cài đặt từng bước

### Bước 1: Chuẩn bị môi trường

1. **Cài đặt Python**
   ```bash
   # Tải Python từ https://python.org
   # Đ<PERSON><PERSON> bảo chọn "Add Python to PATH" khi cài đặt
   
   # Kiểm tra version
   python --version
   pip --version
   ```

2. **Tạo Virtual Environment** (khuyến nghị)
   ```bash
   # Tạo virtual environment
   python -m venv venv
   
   # Kích hoạt virtual environment
   # Windows:
   venv\Scripts\activate
   
   # Linux/Mac:
   source venv/bin/activate
   ```

### Bước 2: Tải và cài đặt dự án

1. **Clone hoặc tải dự án**
   ```bash
   # Nếu có Git:
   git clone <repository-url>
   cd ai-malware-detection
   
   # Hoặc tải ZIP và giải nén
   ```

2. **Cài đặt dependencies**
   ```bash
   # Cài đặt tất cả packages cần thiết
   pip install -r requirements.txt
   
   # Nếu gặp lỗi, thử cài từng package:
   pip install scikit-learn pandas numpy matplotlib seaborn
   pip install pefile python-magic
   pip install Flask Flask-CORS Flask-SQLAlchemy
   pip install SQLAlchemy loguru tqdm joblib
   ```

### Bước 3: Cấu hình hệ thống

1. **Tạo thư mục cần thiết**
   ```bash
   python config.py
   ```

2. **Khởi tạo database**
   ```bash
   python src/api/database.py
   ```

3. **Kiểm tra cài đặt**
   ```bash
   python run_tests.py --test pe_analyzer
   ```

### Bước 4: Chuẩn bị dữ liệu training

1. **Tạo sample dataset**
   ```bash
   python src/models/train.py --create-sample
   ```

2. **Thêm malware samples** (cần thiết để train model)
   - Tạo thư mục `data/samples/malware/`
   - Copy malware samples vào thư mục này
   - **Lưu ý**: Chỉ sử dụng malware samples từ nguồn tin cậy và trong môi trường an toàn

3. **Tạo dataset**
   ```bash
   python src/models/train.py --malware-dir data/samples/malware --benign-dir data/samples/benign
   ```

### Bước 5: Train model

1. **Train Random Forest model**
   ```bash
   python src/models/train.py --dataset data/datasets/malware_dataset.csv --model-type random_forest
   ```

2. **Train SVM model** (tùy chọn)
   ```bash
   python src/models/train.py --dataset data/datasets/malware_dataset.csv --model-type svm
   ```

### Bước 6: Khởi động hệ thống

1. **Chạy web server**
   ```bash
   python src/api/app.py
   ```

2. **Truy cập web interface**
   - Mở browser và truy cập: http://localhost:5000
   - Upload file PE để kiểm tra

## Xử lý Lỗi thường gặp

### Lỗi cài đặt packages

```bash
# Lỗi: Microsoft Visual C++ 14.0 is required
# Giải pháp: Cài đặt Visual Studio Build Tools
# Tải từ: https://visualstudio.microsoft.com/downloads/

# Lỗi: Failed building wheel for pefile
pip install --upgrade pip setuptools wheel
pip install pefile --no-cache-dir
```

### Lỗi import modules

```bash
# Lỗi: ModuleNotFoundError
# Giải pháp: Đảm bảo đang ở đúng thư mục và virtual environment
cd ai-malware-detection
venv\Scripts\activate  # Windows
python -c "import sys; print(sys.path)"
```

### Lỗi database

```bash
# Lỗi: Database locked
# Giải pháp: Xóa file database và tạo lại
del data\malware_detection.db
python src/api/database.py
```

### Lỗi model không load

```bash
# Lỗi: Model chưa được train
# Giải pháp: Train model trước khi chạy web server
python src/models/train.py --create-sample
# Thêm malware samples vào data/samples/malware/
python src/models/train.py --malware-dir data/samples/malware --benign-dir data/samples/benign
```

## Cấu hình nâng cao

### Thay đổi cấu hình

Chỉnh sửa file `config.py`:

```python
# Database
DATABASE_URL = "sqlite:///path/to/your/database.db"

# Web server
FLASK_CONFIG = {
    'HOST': '0.0.0.0',  # Cho phép truy cập từ xa
    'PORT': 8080,       # Thay đổi port
    'DEBUG': False      # Tắt debug mode cho production
}

# File upload
MAX_FILE_SIZE = 100 * 1024 * 1024  # 100MB
```

### Cấu hình logging

```python
# Trong config.py
LOGGING_CONFIG = {
    'level': 'DEBUG',  # DEBUG, INFO, WARNING, ERROR
    'log_file': BASE_DIR / 'logs' / 'app.log'
}
```

### Performance tuning

```python
# Trong config.py
MODEL_CONFIG = {
    'algorithm': 'random_forest',
    'n_jobs': -1,  # Sử dụng tất cả CPU cores
}

# Batch processing
BATCH_CONFIG = {
    'n_workers': 4,  # Số worker processes
    'chunk_size': 100  # Số files xử lý cùng lúc
}
```

## Kiểm tra cài đặt

### Chạy tests

```bash
# Chạy tất cả tests
python run_tests.py

# Chạy test cụ thể
python run_tests.py --test pe_analyzer
python run_tests.py --test api

# Chạy với verbosity thấp
python run_tests.py --verbosity 1
```

### Kiểm tra API

```bash
# Test API status
curl http://localhost:5000/api/status

# Test model info
curl http://localhost:5000/api/model/info
```

### Kiểm tra performance

```bash
# Benchmark PE analysis
python -c "
from src.feature_extraction.pe_analyzer import PEAnalyzer
import time
analyzer = PEAnalyzer()
start = time.time()
result = analyzer.analyze_file('C:/Windows/System32/notepad.exe')
print(f'Analysis time: {time.time() - start:.3f}s')
"
```

## Deployment

### Production setup

1. **Tắt debug mode**
   ```python
   # config.py
   FLASK_CONFIG['DEBUG'] = False
   ```

2. **Sử dụng production WSGI server**
   ```bash
   pip install gunicorn  # Linux/Mac
   pip install waitress  # Windows
   
   # Chạy với Waitress (Windows)
   waitress-serve --host=0.0.0.0 --port=5000 src.api.app:app
   ```

3. **Setup reverse proxy** (Nginx/Apache)
4. **Configure SSL/HTTPS**
5. **Setup monitoring và logging**

### Docker deployment

```dockerfile
# Dockerfile
FROM python:3.9-slim

WORKDIR /app
COPY requirements.txt .
RUN pip install -r requirements.txt

COPY . .
EXPOSE 5000

CMD ["python", "src/api/app.py"]
```

```bash
# Build và run
docker build -t malware-detection .
docker run -p 5000:5000 malware-detection
```

## Hỗ trợ

- **Documentation**: Xem thư mục `docs/`
- **Issues**: Tạo issue trên GitHub repository
- **Email**: [<EMAIL>]

## Cập nhật

```bash
# Cập nhật code
git pull origin main

# Cập nhật dependencies
pip install -r requirements.txt --upgrade

# Migrate database nếu cần
python src/api/database.py

# Retrain model với data mới
python src/models/train.py --dataset data/datasets/malware_dataset.csv
```
