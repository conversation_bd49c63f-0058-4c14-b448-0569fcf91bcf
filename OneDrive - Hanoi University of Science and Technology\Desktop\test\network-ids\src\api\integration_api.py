"""
Integration API Module
API để tích hợp Network IDS với AI Malware Detection System
"""

import sys
import requests
import json
from pathlib import Path
from datetime import datetime
import logging

# Add parent directory to path
sys.path.append(str(Path(__file__).parent.parent.parent))

import config


class MalwareDetectionIntegration:
    """Class tích hợp với AI Malware Detection System"""
    
    def __init__(self):
        self.malware_api_url = config.INTEGRATION_CONFIG['malware_detection_api']
        self.timeout = config.INTEGRATION_CONFIG['timeout']
        self.retry_attempts = config.INTEGRATION_CONFIG['retry_attempts']
        self.logger = logging.getLogger(__name__)
        
    def check_malware_api_status(self):
        """
        Kiểm tra trạng thái của Malware Detection API
        
        Returns:
            dict: Status information
        """
        try:
            response = requests.get(
                f"{self.malware_api_url}/api/status",
                timeout=self.timeout
            )
            
            if response.status_code == 200:
                return {
                    'status': 'online',
                    'data': response.json()
                }
            else:
                return {
                    'status': 'error',
                    'error': f"HTTP {response.status_code}"
                }
                
        except requests.exceptions.RequestException as e:
            self.logger.error(f"Lỗi khi kiểm tra malware API: {e}")
            return {
                'status': 'offline',
                'error': str(e)
            }
    
    def analyze_suspicious_file(self, file_data, filename, source_ip=None):
        """
        Gửi file đáng nghi đến malware detection system
        
        Args:
            file_data (bytes): File data
            filename (str): Tên file
            source_ip (str): IP nguồn (optional)
            
        Returns:
            dict: Analysis results
        """
        try:
            # Prepare file for upload
            files = {
                'file': (filename, file_data, 'application/octet-stream')
            }
            
            # Additional metadata
            data = {
                'source': 'network_ids',
                'source_ip': source_ip or 'unknown',
                'timestamp': datetime.now().isoformat()
            }
            
            # Send to malware detection API
            response = requests.post(
                f"{self.malware_api_url}/api/upload",
                files=files,
                data=data,
                timeout=self.timeout
            )
            
            if response.status_code == 200:
                result = response.json()
                self.logger.info(f"File {filename} analyzed successfully")
                return {
                    'success': True,
                    'result': result
                }
            else:
                self.logger.error(f"Malware analysis failed: HTTP {response.status_code}")
                return {
                    'success': False,
                    'error': f"HTTP {response.status_code}",
                    'details': response.text
                }
                
        except requests.exceptions.RequestException as e:
            self.logger.error(f"Lỗi khi gửi file đến malware API: {e}")
            return {
                'success': False,
                'error': str(e)
            }
    
    def get_malware_statistics(self):
        """
        Lấy thống kê từ malware detection system
        
        Returns:
            dict: Statistics data
        """
        try:
            response = requests.get(
                f"{self.malware_api_url}/api/statistics",
                timeout=self.timeout
            )
            
            if response.status_code == 200:
                return {
                    'success': True,
                    'data': response.json()
                }
            else:
                return {
                    'success': False,
                    'error': f"HTTP {response.status_code}"
                }
                
        except requests.exceptions.RequestException as e:
            self.logger.error(f"Lỗi khi lấy malware statistics: {e}")
            return {
                'success': False,
                'error': str(e)
            }
    
    def correlate_network_malware_events(self, network_alerts, time_window_minutes=60):
        """
        Correlate network alerts với malware detection events
        
        Args:
            network_alerts (list): Network alerts
            time_window_minutes (int): Time window for correlation
            
        Returns:
            dict: Correlation results
        """
        try:
            # Get recent malware detections
            malware_stats = self.get_malware_statistics()
            
            if not malware_stats['success']:
                return {
                    'success': False,
                    'error': 'Cannot get malware statistics'
                }
            
            correlations = []
            
            # Simple correlation based on IP addresses and time
            for alert in network_alerts:
                source_ip = alert.get('source_ip')
                alert_time = datetime.fromisoformat(alert['timestamp'])
                
                # Check if there are malware detections from same IP
                # This is a simplified correlation - in real scenario would be more sophisticated
                correlation = {
                    'network_alert': alert,
                    'malware_detections': [],
                    'correlation_score': 0.0,
                    'risk_level': 'low'
                }
                
                # Add correlation logic here based on actual malware API response format
                correlations.append(correlation)
            
            return {
                'success': True,
                'correlations': correlations,
                'total_correlations': len(correlations)
            }
            
        except Exception as e:
            self.logger.error(f"Lỗi trong correlation: {e}")
            return {
                'success': False,
                'error': str(e)
            }
    
    def send_network_context_to_malware_system(self, context_data):
        """
        Gửi network context đến malware detection system
        
        Args:
            context_data (dict): Network context information
            
        Returns:
            dict: Response from malware system
        """
        try:
            response = requests.post(
                f"{self.malware_api_url}/api/network-context",
                json=context_data,
                timeout=self.timeout,
                headers={'Content-Type': 'application/json'}
            )
            
            if response.status_code == 200:
                return {
                    'success': True,
                    'data': response.json()
                }
            else:
                return {
                    'success': False,
                    'error': f"HTTP {response.status_code}",
                    'details': response.text
                }
                
        except requests.exceptions.RequestException as e:
            self.logger.error(f"Lỗi khi gửi network context: {e}")
            return {
                'success': False,
                'error': str(e)
            }


class IntegratedThreatIntelligence:
    """Class tích hợp threat intelligence từ cả hai hệ thống"""
    
    def __init__(self):
        self.malware_integration = MalwareDetectionIntegration()
        self.logger = logging.getLogger(__name__)
    
    def generate_unified_threat_report(self, network_stats, detection_stats):
        """
        Tạo báo cáo threat tổng hợp
        
        Args:
            network_stats (dict): Network monitoring statistics
            detection_stats (dict): Detection statistics
            
        Returns:
            dict: Unified threat report
        """
        try:
            # Get malware statistics
            malware_stats = self.malware_integration.get_malware_statistics()
            
            # Create unified report
            report = {
                'timestamp': datetime.now().isoformat(),
                'report_type': 'unified_threat_intelligence',
                'network_security': {
                    'monitoring_status': network_stats.get('status', 'unknown'),
                    'total_packets': network_stats.get('total_packets', 0),
                    'alerts_count': len(network_stats.get('recent_alerts', [])),
                    'top_threats': self._extract_top_network_threats(network_stats),
                    'protocol_distribution': network_stats.get('protocol_distribution', {}),
                    'top_talkers': network_stats.get('top_talkers', {})
                },
                'malware_detection': {
                    'api_status': malware_stats['success'],
                    'statistics': malware_stats.get('data', {}) if malware_stats['success'] else None
                },
                'detection_performance': {
                    'total_analyzed': detection_stats.get('total_analyzed', 0),
                    'anomalies_detected': detection_stats.get('anomalies_detected', 0),
                    'intrusions_detected': detection_stats.get('intrusions_detected', 0),
                    'detection_rate': detection_stats.get('detection_rate', 0.0),
                    'models_status': detection_stats.get('models_loaded', {})
                },
                'risk_assessment': self._calculate_overall_risk(network_stats, detection_stats, malware_stats),
                'recommendations': self._generate_recommendations(network_stats, detection_stats, malware_stats)
            }
            
            return report
            
        except Exception as e:
            self.logger.error(f"Lỗi khi tạo unified report: {e}")
            return {
                'error': str(e),
                'timestamp': datetime.now().isoformat()
            }
    
    def _extract_top_network_threats(self, network_stats):
        """Extract top network threats from stats"""
        threats = []
        
        # Extract from recent alerts
        recent_alerts = network_stats.get('recent_alerts', [])
        threat_counts = {}
        
        for alert in recent_alerts:
            threat_type = alert.get('type', 'unknown')
            if threat_type not in threat_counts:
                threat_counts[threat_type] = {
                    'count': 0,
                    'severity_levels': {},
                    'source_ips': set()
                }
            
            threat_counts[threat_type]['count'] += 1
            
            severity = alert.get('severity', 'unknown')
            if severity not in threat_counts[threat_type]['severity_levels']:
                threat_counts[threat_type]['severity_levels'][severity] = 0
            threat_counts[threat_type]['severity_levels'][severity] += 1
            
            source_ip = alert.get('source_ip')
            if source_ip:
                threat_counts[threat_type]['source_ips'].add(source_ip)
        
        # Convert to list and sort by count
        for threat_type, data in threat_counts.items():
            data['source_ips'] = list(data['source_ips'])  # Convert set to list
            threats.append({
                'type': threat_type,
                'count': data['count'],
                'severity_distribution': data['severity_levels'],
                'unique_sources': len(data['source_ips']),
                'source_ips': data['source_ips'][:10]  # Top 10 source IPs
            })
        
        # Sort by count (descending)
        threats.sort(key=lambda x: x['count'], reverse=True)
        
        return threats[:10]  # Top 10 threats
    
    def _calculate_overall_risk(self, network_stats, detection_stats, malware_stats):
        """Calculate overall security risk level"""
        risk_factors = []
        
        # Network risk factors
        recent_alerts = network_stats.get('recent_alerts', [])
        critical_alerts = len([a for a in recent_alerts if a.get('severity') == 'critical'])
        high_alerts = len([a for a in recent_alerts if a.get('severity') == 'high'])
        
        if critical_alerts > 0:
            risk_factors.append(('critical_network_alerts', critical_alerts * 0.4))
        if high_alerts > 0:
            risk_factors.append(('high_network_alerts', high_alerts * 0.2))
        
        # Detection risk factors
        detection_rate = detection_stats.get('detection_rate', 0.0)
        if detection_rate > 0.1:  # More than 10% detection rate
            risk_factors.append(('high_detection_rate', detection_rate * 0.3))
        
        # Model status risk
        models_loaded = detection_stats.get('models_loaded', {})
        if not all(models_loaded.values()):
            risk_factors.append(('models_not_loaded', 0.2))
        
        # Calculate overall risk score (0-1)
        total_risk = sum(factor[1] for factor in risk_factors)
        risk_score = min(total_risk, 1.0)  # Cap at 1.0
        
        # Determine risk level
        if risk_score >= 0.8:
            risk_level = 'critical'
        elif risk_score >= 0.6:
            risk_level = 'high'
        elif risk_score >= 0.4:
            risk_level = 'medium'
        else:
            risk_level = 'low'
        
        return {
            'risk_score': risk_score,
            'risk_level': risk_level,
            'risk_factors': [{'factor': f[0], 'score': f[1]} for f in risk_factors]
        }
    
    def _generate_recommendations(self, network_stats, detection_stats, malware_stats):
        """Generate security recommendations"""
        recommendations = []
        
        # Check recent alerts
        recent_alerts = network_stats.get('recent_alerts', [])
        if len(recent_alerts) > 10:
            recommendations.append({
                'priority': 'high',
                'category': 'alerting',
                'message': f'High number of alerts ({len(recent_alerts)}). Review and investigate.',
                'action': 'Review alert patterns and investigate potential threats'
            })
        
        # Check model status
        models_loaded = detection_stats.get('models_loaded', {})
        if not models_loaded.get('anomaly_detector'):
            recommendations.append({
                'priority': 'medium',
                'category': 'models',
                'message': 'Anomaly detection model not loaded',
                'action': 'Train and load anomaly detection model'
            })
        
        if not models_loaded.get('intrusion_classifier'):
            recommendations.append({
                'priority': 'medium',
                'category': 'models',
                'message': 'Intrusion classification model not loaded',
                'action': 'Train and load intrusion classification model'
            })
        
        # Check malware integration
        if not malware_stats['success']:
            recommendations.append({
                'priority': 'low',
                'category': 'integration',
                'message': 'Malware detection system not accessible',
                'action': 'Check malware detection system connectivity'
            })
        
        # Check detection rate
        detection_rate = detection_stats.get('detection_rate', 0.0)
        if detection_rate > 0.2:  # More than 20%
            recommendations.append({
                'priority': 'high',
                'category': 'detection',
                'message': f'Very high detection rate ({detection_rate:.1%}). Possible attack in progress.',
                'action': 'Immediate investigation required'
            })
        
        return recommendations


if __name__ == "__main__":
    # Test integration
    logging.basicConfig(level=logging.INFO)
    
    integration = MalwareDetectionIntegration()
    
    # Test API status
    status = integration.check_malware_api_status()
    print(f"Malware API Status: {status}")
    
    # Test threat intelligence
    threat_intel = IntegratedThreatIntelligence()
    
    # Sample data for testing
    sample_network_stats = {
        'total_packets': 1000,
        'recent_alerts': [
            {
                'type': 'PORT_SCAN',
                'severity': 'high',
                'source_ip': '*************',
                'timestamp': datetime.now().isoformat()
            }
        ],
        'protocol_distribution': {'TCP': 800, 'UDP': 200}
    }
    
    sample_detection_stats = {
        'total_analyzed': 100,
        'anomalies_detected': 5,
        'intrusions_detected': 2,
        'detection_rate': 0.07,
        'models_loaded': {'anomaly_detector': True, 'intrusion_classifier': True}
    }
    
    report = threat_intel.generate_unified_threat_report(
        sample_network_stats, 
        sample_detection_stats
    )
    
    print("Unified Threat Report:")
    print(json.dumps(report, indent=2, ensure_ascii=False))
    
    print("✅ Integration API test completed!")
