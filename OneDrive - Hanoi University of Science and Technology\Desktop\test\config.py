"""
Configuration file cho AI Malware Detection System
"""

import os
from pathlib import Path

# Base directory
BASE_DIR = Path(__file__).parent.absolute()

# Database configuration
DATABASE_URL = f"sqlite:///{BASE_DIR}/data/malware_detection.db"

# Model configuration
MODEL_DIR = BASE_DIR / "data" / "models"
TRAINED_MODEL_PATH = MODEL_DIR / "malware_classifier.joblib"
SCALER_PATH = MODEL_DIR / "feature_scaler.joblib"

# Data directories
DATA_DIR = BASE_DIR / "data"
SAMPLES_DIR = DATA_DIR / "samples"
DATASETS_DIR = DATA_DIR / "datasets"

# Upload configuration
UPLOAD_FOLDER = BASE_DIR / "uploads"
MAX_FILE_SIZE = 50 * 1024 * 1024  # 50MB
ALLOWED_EXTENSIONS = {'.exe', '.dll', '.sys', '.scr', '.com', '.pif'}

# Feature extraction settings
FEATURE_CONFIG = {
    'extract_imports': True,
    'extract_sections': True,
    'extract_entropy': True,
    'extract_strings': True,
    'max_strings': 1000,
    'min_string_length': 4
}

# ML Model settings
MODEL_CONFIG = {
    'algorithm': 'random_forest',  # 'random_forest', 'svm', 'xgboost'
    'test_size': 0.2,
    'random_state': 42,
    'cv_folds': 5
}

# Random Forest specific settings
RF_CONFIG = {
    'n_estimators': 100,
    'max_depth': 20,
    'min_samples_split': 5,
    'min_samples_leaf': 2,
    'random_state': 42
}

# SVM specific settings
SVM_CONFIG = {
    'kernel': 'rbf',
    'C': 1.0,
    'gamma': 'scale',
    'random_state': 42
}

# Web app settings
FLASK_CONFIG = {
    'SECRET_KEY': 'your-secret-key-here',
    'DEBUG': True,
    'HOST': '0.0.0.0',
    'PORT': 5000
}

# Logging configuration
LOGGING_CONFIG = {
    'level': 'INFO',
    'format': '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    'log_file': BASE_DIR / 'logs' / 'malware_detection.log'
}

# Tạo các thư mục cần thiết
def create_directories():
    """Tạo các thư mục cần thiết nếu chưa tồn tại"""
    directories = [
        MODEL_DIR,
        UPLOAD_FOLDER,
        SAMPLES_DIR,
        DATASETS_DIR,
        BASE_DIR / 'logs'
    ]
    
    for directory in directories:
        directory.mkdir(parents=True, exist_ok=True)

if __name__ == "__main__":
    create_directories()
    print("Đã tạo các thư mục cần thiết!")
