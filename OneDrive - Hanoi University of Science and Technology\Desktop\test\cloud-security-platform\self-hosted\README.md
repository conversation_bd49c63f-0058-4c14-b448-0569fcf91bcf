# 🏠 Self-Hosted Cloud Security Platform

Hướng dẫn triển khai Cloud Security Platform trên infrastructure tự quản lý (on-premises/private cloud).

## 🏗️ Kiến trúc Self-Hosted

```
┌─────────────────────────────────────────────────────────────────┐
│                    Your Private Infrastructure                   │
├─────────────────────────────────────────────────────────────────┤
│  ┌─────────────────┐    ┌─────────────────┐                    │
│  │   HAProxy/Nginx │    │   SSL/TLS       │                    │
│  │   Load Balancer │    │   Termination   │                    │
│  └─────────────────┘    └─────────────────┘                    │
├─────────────────────────────────────────────────────────────────┤
│                    Kubernetes Cluster (K3s/K8s)                │
│  ┌─────────────────┐    ┌─────────────────┐                    │
│  │  AI Malware     │◄──►│   Network IDS   │                    │
│  │  Detection      │    │   Service       │                    │
│  └─────────────────┘    └─────────────────┘                    │
│  ┌─────────────────┐    ┌─────────────────┐                    │
│  │   Dashboard     │    │   API Gateway   │                    │
│  │   Service       │    │   (Nginx)       │                    │
│  └─────────────────┘    └─────────────────┘                    │
├─────────────────────────────────────────────────────────────────┤
│  ┌─────────────────┐    ┌─────────────────┐                    │
│  │   PostgreSQL    │    │   MinIO/NFS     │                    │
│  │   Database      │    │   Storage       │                    │
│  └─────────────────┘    └─────────────────┘                    │
├─────────────────────────────────────────────────────────────────┤
│  ┌─────────────────┐    ┌─────────────────┐                    │
│  │   Prometheus    │    │   ELK Stack     │                    │
│  │   + Grafana     │    │   Logging       │                    │
│  └─────────────────┘    └─────────────────┘                    │
└─────────────────────────────────────────────────────────────────┘
```

## 🖥️ Yêu cầu Hardware

### Minimum Requirements (Development)
- **CPU**: 8 cores
- **RAM**: 16GB
- **Storage**: 200GB SSD
- **Network**: 1Gbps

### Recommended (Production)
- **Master Nodes**: 3x (4 CPU, 8GB RAM, 100GB SSD)
- **Worker Nodes**: 3x (8 CPU, 16GB RAM, 500GB SSD)
- **Storage Node**: 1x (4 CPU, 8GB RAM, 2TB SSD)
- **Load Balancer**: 2x (2 CPU, 4GB RAM, 50GB SSD)

### High Availability Setup
- **Master Nodes**: 3x (8 CPU, 16GB RAM, 200GB SSD)
- **Worker Nodes**: 5x (16 CPU, 32GB RAM, 1TB SSD)
- **Storage Cluster**: 3x (8 CPU, 16GB RAM, 4TB SSD)
- **Load Balancers**: 2x (4 CPU, 8GB RAM, 100GB SSD)

## 🛠️ Công nghệ Stack

### Container Orchestration
- **K3s**: Lightweight Kubernetes (recommended)
- **Kubernetes**: Full Kubernetes cluster
- **Docker Swarm**: Alternative option

### Storage Solutions
- **MinIO**: S3-compatible object storage
- **NFS**: Network File System
- **Ceph**: Distributed storage (advanced)
- **Local Storage**: Direct attached storage

### Database
- **PostgreSQL**: Primary database
- **Redis**: Caching layer
- **InfluxDB**: Time-series metrics (optional)

### Load Balancing
- **HAProxy**: High availability load balancer
- **Nginx**: Web server and reverse proxy
- **Traefik**: Modern reverse proxy

### Monitoring
- **Prometheus**: Metrics collection
- **Grafana**: Dashboards and visualization
- **ELK Stack**: Centralized logging
- **Jaeger**: Distributed tracing (optional)

## 🚀 Quick Start (Single Node)

### 1. Chuẩn bị Server
```bash
# Update system
sudo apt update && sudo apt upgrade -y

# Install Docker
curl -fsSL https://get.docker.com -o get-docker.sh
sudo sh get-docker.sh
sudo usermod -aG docker $USER

# Install Docker Compose
sudo curl -L "https://github.com/docker/compose/releases/download/v2.20.0/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
sudo chmod +x /usr/local/bin/docker-compose

# Reboot to apply changes
sudo reboot
```

### 2. Clone và Setup
```bash
# Clone project
git clone <repository-url>
cd cloud-security-platform/self-hosted

# Copy environment file
cp .env.example .env

# Edit configuration
nano .env
```

### 3. Deploy với Docker Compose
```bash
# Start all services
docker-compose up -d

# Check status
docker-compose ps

# View logs
docker-compose logs -f
```

## 🏢 Production Deployment (Multi-Node)

### 1. Setup Kubernetes Cluster

#### Option A: K3s (Recommended)
```bash
# On master node
curl -sfL https://get.k3s.io | sh -

# Get node token
sudo cat /var/lib/rancher/k3s/server/node-token

# On worker nodes
curl -sfL https://get.k3s.io | K3S_URL=https://<master-ip>:6443 K3S_TOKEN=<token> sh -

# Install kubectl
sudo cp /usr/local/bin/k3s /usr/local/bin/kubectl
```

#### Option B: Full Kubernetes
```bash
# Install kubeadm, kubelet, kubectl on all nodes
sudo apt-get update
sudo apt-get install -y apt-transport-https ca-certificates curl
curl -fsSL https://packages.cloud.google.com/apt/doc/apt-key.gpg | sudo apt-key add -
echo "deb https://apt.kubernetes.io/ kubernetes-xenial main" | sudo tee /etc/apt/sources.list.d/kubernetes.list
sudo apt-get update
sudo apt-get install -y kubelet kubeadm kubectl

# Initialize cluster on master
sudo kubeadm init --pod-network-cidr=**********/16

# Join worker nodes
sudo kubeadm join <master-ip>:6443 --token <token> --discovery-token-ca-cert-hash <hash>
```

### 2. Setup Storage

#### MinIO Object Storage
```bash
# Create MinIO namespace
kubectl create namespace minio

# Deploy MinIO
kubectl apply -f self-hosted/storage/minio-deployment.yaml

# Create buckets
kubectl exec -it minio-0 -n minio -- mc mb local/models
kubectl exec -it minio-0 -n minio -- mc mb local/uploads
kubectl exec -it minio-0 -n minio -- mc mb local/logs
```

#### NFS Storage
```bash
# Install NFS server
sudo apt install nfs-kernel-server -y

# Create directories
sudo mkdir -p /nfs/models /nfs/uploads /nfs/logs
sudo chown nobody:nogroup /nfs/*

# Configure exports
echo "/nfs/models *(rw,sync,no_subtree_check)" | sudo tee -a /etc/exports
echo "/nfs/uploads *(rw,sync,no_subtree_check)" | sudo tee -a /etc/exports
echo "/nfs/logs *(rw,sync,no_subtree_check)" | sudo tee -a /etc/exports

# Start NFS
sudo systemctl restart nfs-kernel-server
```

### 3. Setup Database

#### PostgreSQL Cluster
```bash
# Deploy PostgreSQL
kubectl apply -f self-hosted/database/postgresql-deployment.yaml

# Initialize database
kubectl exec -it postgresql-0 -n security-platform -- psql -U postgres -c "CREATE DATABASE security_platform;"
kubectl exec -it postgresql-0 -n security-platform -- psql -U postgres -c "CREATE USER security_user WITH PASSWORD 'secure_password';"
kubectl exec -it postgresql-0 -n security-platform -- psql -U postgres -c "GRANT ALL PRIVILEGES ON DATABASE security_platform TO security_user;"
```

### 4. Deploy Applications
```bash
# Create namespace
kubectl create namespace security-platform

# Deploy secrets
kubectl apply -f self-hosted/secrets/

# Deploy applications
kubectl apply -f self-hosted/deployments/

# Deploy services
kubectl apply -f self-hosted/services/

# Deploy ingress
kubectl apply -f self-hosted/ingress/
```

### 5. Setup Load Balancer

#### HAProxy
```bash
# Install HAProxy
sudo apt install haproxy -y

# Configure HAProxy
sudo cp self-hosted/load-balancer/haproxy.cfg /etc/haproxy/
sudo systemctl restart haproxy
sudo systemctl enable haproxy
```

#### Nginx
```bash
# Install Nginx
sudo apt install nginx -y

# Configure Nginx
sudo cp self-hosted/load-balancer/nginx.conf /etc/nginx/
sudo systemctl restart nginx
sudo systemctl enable nginx
```

## 📊 Monitoring Setup

### 1. Prometheus + Grafana
```bash
# Deploy monitoring stack
kubectl apply -f self-hosted/monitoring/prometheus/
kubectl apply -f self-hosted/monitoring/grafana/

# Access Grafana
kubectl port-forward svc/grafana 3000:3000 -n monitoring
```

### 2. ELK Stack (Logging)
```bash
# Deploy Elasticsearch
kubectl apply -f self-hosted/logging/elasticsearch/

# Deploy Logstash
kubectl apply -f self-hosted/logging/logstash/

# Deploy Kibana
kubectl apply -f self-hosted/logging/kibana/
```

## 🔒 Security Hardening

### 1. Network Security
```bash
# Configure firewall
sudo ufw enable
sudo ufw allow 22/tcp    # SSH
sudo ufw allow 80/tcp    # HTTP
sudo ufw allow 443/tcp   # HTTPS
sudo ufw allow 6443/tcp  # Kubernetes API

# Setup VPN (optional)
sudo apt install openvpn -y
```

### 2. SSL/TLS Certificates
```bash
# Install cert-manager
kubectl apply -f https://github.com/cert-manager/cert-manager/releases/download/v1.12.0/cert-manager.yaml

# Create ClusterIssuer
kubectl apply -f self-hosted/certificates/cluster-issuer.yaml
```

### 3. Backup Strategy
```bash
# Database backup
kubectl create cronjob db-backup --image=postgres:14 --schedule="0 2 * * *" -- pg_dump -h postgresql -U security_user security_platform > /backup/db-$(date +%Y%m%d).sql

# Storage backup
kubectl create cronjob storage-backup --image=minio/mc --schedule="0 3 * * *" -- mc mirror local/models /backup/models-$(date +%Y%m%d)/
```

## 🔧 Configuration Files

### Environment Variables (.env)
```bash
# Database
DB_HOST=postgresql
DB_PORT=5432
DB_NAME=security_platform
DB_USER=security_user
DB_PASSWORD=secure_password

# Storage
STORAGE_TYPE=minio
MINIO_ENDPOINT=minio:9000
MINIO_ACCESS_KEY=minioadmin
MINIO_SECRET_KEY=minioadmin

# Redis
REDIS_HOST=redis
REDIS_PORT=6379

# Application
SECRET_KEY=your-secret-key
JWT_SECRET=your-jwt-secret
API_KEY=your-api-key
```

## 📈 Scaling

### Horizontal Scaling
```bash
# Scale applications
kubectl scale deployment malware-detection --replicas=5 -n security-platform
kubectl scale deployment network-ids --replicas=3 -n security-platform

# Add worker nodes
# Follow K3s/K8s join process
```

### Vertical Scaling
```bash
# Increase resources
kubectl patch deployment malware-detection -n security-platform -p '{"spec":{"template":{"spec":{"containers":[{"name":"malware-detection","resources":{"limits":{"cpu":"2000m","memory":"4Gi"}}}]}}}}'
```

## 💰 Cost Comparison

### Self-Hosted vs Cloud
- **Initial Setup**: Higher (hardware, setup time)
- **Monthly Costs**: Lower (no cloud fees)
- **Maintenance**: Higher (self-managed)
- **Scalability**: Limited by hardware
- **Security**: Full control

### Estimated Costs (Self-Hosted)
- **Hardware**: $5,000-15,000 (one-time)
- **Electricity**: $100-300/month
- **Internet**: $100-500/month
- **Maintenance**: $500-2000/month (staff time)

## 🆘 Troubleshooting

### Common Issues
1. **Pods not starting**: Check resource limits
2. **Storage issues**: Verify NFS/MinIO connectivity
3. **Network problems**: Check firewall rules
4. **Performance issues**: Monitor resource usage

### Useful Commands
```bash
# Check cluster status
kubectl get nodes
kubectl get pods --all-namespaces

# Check logs
kubectl logs -f deployment/malware-detection -n security-platform

# Debug networking
kubectl exec -it <pod> -- nslookup <service>
```

## 📚 Next Steps

1. **Setup CI/CD**: Jenkins, GitLab CI, or GitHub Actions
2. **Implement Backup**: Automated backup solutions
3. **Security Audit**: Regular security assessments
4. **Performance Tuning**: Optimize for your workload
5. **Documentation**: Create runbooks for your team

---

**🏠 Your Private Cloud Security Platform is Ready!** 🚀
