"""
Logging Configuration
Cấu hình logging system cho AI Malware Detection System
"""

import os
import sys
import logging
import logging.handlers
from pathlib import Path
from datetime import datetime
import json

# Add src to path
sys.path.append(str(Path(__file__).parent.parent))

import config
from api.database import db_manager


class DatabaseHandler(logging.Handler):
    """Custom logging handler đ<PERSON> lưu logs vào database"""
    
    def __init__(self):
        super().__init__()
        self.db_manager = db_manager
    
    def emit(self, record):
        """Emit log record to database"""
        try:
            # Get additional context from record
            client_ip = getattr(record, 'client_ip', None)
            user_agent = getattr(record, 'user_agent', None)
            request_id = getattr(record, 'request_id', None)
            
            # Save to database
            self.db_manager.log_system_event(
                level=record.levelname,
                message=self.format(record),
                module=record.module if hasattr(record, 'module') else record.name,
                function=record.funcName,
                line_number=record.lineno,
                client_ip=client_ip,
                user_agent=user_agent,
                request_id=request_id
            )
        except Exception:
            # Don't raise exceptions in logging handler
            pass


class JSONFormatter(logging.Formatter):
    """JSON formatter cho structured logging"""
    
    def format(self, record):
        """Format log record as JSON"""
        log_entry = {
            'timestamp': datetime.utcnow().isoformat(),
            'level': record.levelname,
            'logger': record.name,
            'module': record.module if hasattr(record, 'module') else record.name,
            'function': record.funcName,
            'line': record.lineno,
            'message': record.getMessage(),
            'thread': record.thread,
            'thread_name': record.threadName,
            'process': record.process,
            'process_name': record.processName
        }
        
        # Add exception info if present
        if record.exc_info:
            log_entry['exception'] = self.formatException(record.exc_info)
        
        # Add extra fields
        for key, value in record.__dict__.items():
            if key not in ['name', 'msg', 'args', 'levelname', 'levelno', 'pathname',
                          'filename', 'module', 'lineno', 'funcName', 'created',
                          'msecs', 'relativeCreated', 'thread', 'threadName',
                          'processName', 'process', 'getMessage', 'exc_info',
                          'exc_text', 'stack_info']:
                log_entry[key] = value
        
        return json.dumps(log_entry, ensure_ascii=False)


def setup_logging(log_level='INFO', enable_database_logging=True, enable_file_logging=True):
    """
    Setup logging configuration
    
    Args:
        log_level (str): Logging level
        enable_database_logging (bool): Enable database logging
        enable_file_logging (bool): Enable file logging
    """
    
    # Create logs directory
    log_dir = config.LOGGING_CONFIG['log_file'].parent
    log_dir.mkdir(parents=True, exist_ok=True)
    
    # Root logger configuration
    root_logger = logging.getLogger()
    root_logger.setLevel(getattr(logging, log_level.upper()))
    
    # Clear existing handlers
    root_logger.handlers.clear()
    
    # Console handler
    console_handler = logging.StreamHandler(sys.stdout)
    console_handler.setLevel(logging.INFO)
    console_formatter = logging.Formatter(
        '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        datefmt='%Y-%m-%d %H:%M:%S'
    )
    console_handler.setFormatter(console_formatter)
    root_logger.addHandler(console_handler)
    
    # File handler
    if enable_file_logging:
        file_handler = logging.handlers.RotatingFileHandler(
            config.LOGGING_CONFIG['log_file'],
            maxBytes=10*1024*1024,  # 10MB
            backupCount=5,
            encoding='utf-8'
        )
        file_handler.setLevel(logging.DEBUG)
        file_formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(funcName)s:%(lineno)d - %(message)s',
            datefmt='%Y-%m-%d %H:%M:%S'
        )
        file_handler.setFormatter(file_formatter)
        root_logger.addHandler(file_handler)
    
    # JSON file handler for structured logs
    json_log_file = log_dir / 'malware_detection.json.log'
    json_handler = logging.handlers.RotatingFileHandler(
        json_log_file,
        maxBytes=10*1024*1024,  # 10MB
        backupCount=5,
        encoding='utf-8'
    )
    json_handler.setLevel(logging.INFO)
    json_handler.setFormatter(JSONFormatter())
    root_logger.addHandler(json_handler)
    
    # Database handler
    if enable_database_logging:
        try:
            db_handler = DatabaseHandler()
            db_handler.setLevel(logging.WARNING)  # Only log warnings and errors to DB
            db_formatter = logging.Formatter('%(message)s')
            db_handler.setFormatter(db_formatter)
            root_logger.addHandler(db_handler)
        except Exception as e:
            logging.warning(f"Failed to setup database logging: {e}")
    
    # Configure specific loggers
    
    # Flask logger
    flask_logger = logging.getLogger('werkzeug')
    flask_logger.setLevel(logging.WARNING)
    
    # SQLAlchemy logger
    sqlalchemy_logger = logging.getLogger('sqlalchemy.engine')
    sqlalchemy_logger.setLevel(logging.WARNING)
    
    # Requests logger
    requests_logger = logging.getLogger('urllib3')
    requests_logger.setLevel(logging.WARNING)
    
    logging.info("Logging system initialized successfully")


class LoggerAdapter(logging.LoggerAdapter):
    """Custom logger adapter để thêm context information"""
    
    def __init__(self, logger, extra=None):
        super().__init__(logger, extra or {})
    
    def process(self, msg, kwargs):
        """Process log message and add extra context"""
        # Add extra context to log record
        if 'extra' not in kwargs:
            kwargs['extra'] = {}
        
        kwargs['extra'].update(self.extra)
        
        return msg, kwargs


def get_logger(name, **extra_context):
    """
    Get logger with optional extra context
    
    Args:
        name (str): Logger name
        **extra_context: Extra context to add to all log messages
        
    Returns:
        LoggerAdapter: Logger with extra context
    """
    logger = logging.getLogger(name)
    
    if extra_context:
        return LoggerAdapter(logger, extra_context)
    
    return logger


def log_request(request, response_status=None, response_time=None):
    """
    Log HTTP request
    
    Args:
        request: Flask request object
        response_status (int): HTTP response status code
        response_time (float): Response time in seconds
    """
    logger = get_logger('api.requests')
    
    log_data = {
        'method': request.method,
        'url': request.url,
        'remote_addr': request.remote_addr,
        'user_agent': request.headers.get('User-Agent', ''),
        'content_length': request.content_length,
        'response_status': response_status,
        'response_time': response_time
    }
    
    # Add extra context
    extra = {
        'client_ip': request.remote_addr,
        'user_agent': request.headers.get('User-Agent', ''),
        'request_id': getattr(request, 'request_id', None)
    }
    
    if response_status and response_status >= 400:
        logger.error(f"HTTP {response_status} - {request.method} {request.path}", extra=extra)
    else:
        logger.info(f"HTTP {response_status or 'N/A'} - {request.method} {request.path}", extra=extra)


def log_analysis_result(filename, prediction, confidence, analysis_time, client_ip=None):
    """
    Log analysis result
    
    Args:
        filename (str): Analyzed filename
        prediction (str): Prediction result
        confidence (float): Confidence score
        analysis_time (float): Analysis time in seconds
        client_ip (str): Client IP address
    """
    logger = get_logger('analysis.results')
    
    extra = {
        'client_ip': client_ip,
        'analysis_time': analysis_time,
        'confidence': confidence
    }
    
    logger.info(f"Analysis completed: {filename} -> {prediction} (confidence: {confidence:.3f})", 
                extra=extra)


def log_training_result(model_type, accuracy, training_time, dataset_size):
    """
    Log model training result
    
    Args:
        model_type (str): Type of model
        accuracy (float): Model accuracy
        training_time (float): Training time in seconds
        dataset_size (int): Size of training dataset
    """
    logger = get_logger('training.results')
    
    extra = {
        'model_type': model_type,
        'accuracy': accuracy,
        'training_time': training_time,
        'dataset_size': dataset_size
    }
    
    logger.info(f"Model training completed: {model_type} - Accuracy: {accuracy:.3f}", 
                extra=extra)


def log_error(error, context=None):
    """
    Log error with context
    
    Args:
        error (Exception): Error object
        context (dict): Additional context
    """
    logger = get_logger('system.errors')
    
    extra = context or {}
    
    logger.error(f"Error occurred: {str(error)}", exc_info=True, extra=extra)


# Performance monitoring decorator
def log_performance(func):
    """Decorator để log performance của functions"""
    import functools
    import time
    
    @functools.wraps(func)
    def wrapper(*args, **kwargs):
        logger = get_logger(f'performance.{func.__module__}.{func.__name__}')
        
        start_time = time.time()
        try:
            result = func(*args, **kwargs)
            execution_time = time.time() - start_time
            
            logger.info(f"Function executed successfully in {execution_time:.3f}s", 
                       extra={'execution_time': execution_time})
            
            return result
        except Exception as e:
            execution_time = time.time() - start_time
            logger.error(f"Function failed after {execution_time:.3f}s: {str(e)}", 
                        exc_info=True, extra={'execution_time': execution_time})
            raise
    
    return wrapper


if __name__ == "__main__":
    # Test logging setup
    setup_logging('DEBUG')
    
    # Test different log levels
    logger = get_logger(__name__)
    
    logger.debug("This is a debug message")
    logger.info("This is an info message")
    logger.warning("This is a warning message")
    logger.error("This is an error message")
    
    # Test logger with extra context
    context_logger = get_logger(__name__, client_ip='127.0.0.1', user_id='test_user')
    context_logger.info("This message has extra context")
    
    # Test performance decorator
    @log_performance
    def test_function():
        import time
        time.sleep(0.1)
        return "test result"
    
    result = test_function()
    print(f"Function result: {result}")
    
    print("Logging test completed!")
