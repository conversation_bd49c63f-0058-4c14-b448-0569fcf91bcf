apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: models-pvc
  namespace: security-platform
  labels:
    app: security-platform
    component: storage
    type: models
spec:
  accessModes:
    - ReadWriteMany
  resources:
    requests:
      storage: 50Gi
  storageClassName: ssd-retain
---
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: uploads-pvc
  namespace: security-platform
  labels:
    app: security-platform
    component: storage
    type: uploads
spec:
  accessModes:
    - ReadWriteMany
  resources:
    requests:
      storage: 100Gi
  storageClassName: ssd-retain
---
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: pcap-pvc
  namespace: security-platform
  labels:
    app: security-platform
    component: storage
    type: pcap
spec:
  accessModes:
    - ReadWriteMany
  resources:
    requests:
      storage: 200Gi
  storageClassName: ssd-retain
---
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: datasets-pvc
  namespace: security-platform
  labels:
    app: security-platform
    component: storage
    type: datasets
spec:
  accessModes:
    - ReadWriteMany
  resources:
    requests:
      storage: 100Gi
  storageClassName: ssd-retain
---
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: logs-pvc
  namespace: security-platform
  labels:
    app: security-platform
    component: storage
    type: logs
spec:
  accessModes:
    - ReadWriteMany
  resources:
    requests:
      storage: 50Gi
  storageClassName: ssd-retain
---
# Storage Classes
apiVersion: storage.k8s.io/v1
kind: StorageClass
metadata:
  name: ssd-retain
  labels:
    app: security-platform
provisioner: kubernetes.io/gce-pd
parameters:
  type: pd-ssd
  replication-type: regional-pd
  zones: asia-southeast1-a,asia-southeast1-b
reclaimPolicy: Retain
allowVolumeExpansion: true
volumeBindingMode: WaitForFirstConsumer
---
apiVersion: storage.k8s.io/v1
kind: StorageClass
metadata:
  name: standard-retain
  labels:
    app: security-platform
provisioner: kubernetes.io/gce-pd
parameters:
  type: pd-standard
  replication-type: regional-pd
  zones: asia-southeast1-a,asia-southeast1-b
reclaimPolicy: Retain
allowVolumeExpansion: true
volumeBindingMode: WaitForFirstConsumer
---
# Redis for caching
apiVersion: apps/v1
kind: Deployment
metadata:
  name: redis
  namespace: security-platform
  labels:
    app: redis
    component: cache
spec:
  replicas: 1
  selector:
    matchLabels:
      app: redis
  template:
    metadata:
      labels:
        app: redis
        component: cache
    spec:
      containers:
      - name: redis
        image: redis:7-alpine
        ports:
        - containerPort: 6379
          name: redis
        resources:
          requests:
            cpu: 100m
            memory: 256Mi
          limits:
            cpu: 500m
            memory: 1Gi
        volumeMounts:
        - name: redis-data
          mountPath: /data
        command:
        - redis-server
        - --appendonly
        - "yes"
        - --maxmemory
        - "512mb"
        - --maxmemory-policy
        - "allkeys-lru"
      volumes:
      - name: redis-data
        persistentVolumeClaim:
          claimName: redis-pvc
---
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: redis-pvc
  namespace: security-platform
  labels:
    app: redis
    component: cache
spec:
  accessModes:
    - ReadWriteOnce
  resources:
    requests:
      storage: 10Gi
  storageClassName: ssd-retain
---
apiVersion: v1
kind: Service
metadata:
  name: redis-service
  namespace: security-platform
  labels:
    app: redis
    component: cache
spec:
  type: ClusterIP
  ports:
  - port: 6379
    targetPort: 6379
    protocol: TCP
    name: redis
  selector:
    app: redis
