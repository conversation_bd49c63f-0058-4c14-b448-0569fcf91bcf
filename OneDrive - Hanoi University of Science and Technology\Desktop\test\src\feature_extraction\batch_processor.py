"""
Batch Processor
Module xử lý nhiều file PE cùng lúc để tạo dataset
"""

import os
import pandas as pd
from pathlib import Path
from tqdm import tqdm
import multiprocessing as mp
from concurrent.futures import ProcessPoolExecutor, as_completed
import logging

import sys
from pathlib import Path
sys.path.append(str(Path(__file__).parent.parent))

from feature_extraction.pe_analyzer import PEAnalyzer


class BatchProcessor:
    """Class xử lý batch files để tạo dataset"""
    
    def __init__(self, n_workers=None):
        """
        Khởi tạo BatchProcessor
        
        Args:
            n_workers (int): Số worker processes. Mặc định là số CPU cores
        """
        self.n_workers = n_workers or mp.cpu_count()
        self.logger = logging.getLogger(__name__)
        
    def process_directory(self, directory_path, label, output_file=None):
        """
        Xử lý tất cả file PE trong thư mục
        
        Args:
            directory_path (str): Đường dẫn thư mục chứa files
            label (str): Label cho files ('malware' hoặc 'benign')
            output_file (str): File CSV để lưu kết quả
            
        Returns:
            pd.DataFrame: DataFrame chứa features và labels
        """
        directory_path = Path(directory_path)
        
        if not directory_path.exists():
            raise FileNotFoundError(f"Thư mục không tồn tại: {directory_path}")
        
        # Tìm tất cả file PE
        pe_files = []
        for ext in ['.exe', '.dll', '.sys', '.scr', '.com', '.pif']:
            pe_files.extend(directory_path.glob(f'**/*{ext}'))
        
        self.logger.info(f"Tìm thấy {len(pe_files)} file PE trong {directory_path}")
        
        if not pe_files:
            self.logger.warning(f"Không tìm thấy file PE nào trong {directory_path}")
            return pd.DataFrame()
        
        # Xử lý files song song
        results = []
        with ProcessPoolExecutor(max_workers=self.n_workers) as executor:
            # Submit tasks
            future_to_file = {
                executor.submit(self._process_single_file, str(file_path), label): file_path
                for file_path in pe_files
            }
            
            # Collect results với progress bar
            for future in tqdm(as_completed(future_to_file), 
                             total=len(pe_files), 
                             desc=f"Processing {label} files"):
                file_path = future_to_file[future]
                try:
                    result = future.result()
                    if result is not None:
                        results.append(result)
                except Exception as e:
                    self.logger.error(f"Lỗi xử lý file {file_path}: {e}")
        
        # Tạo DataFrame
        if results:
            df = pd.DataFrame(results)
            self.logger.info(f"Đã xử lý thành công {len(results)}/{len(pe_files)} files")
            
            # Lưu vào file nếu được chỉ định
            if output_file:
                df.to_csv(output_file, index=False)
                self.logger.info(f"Đã lưu kết quả vào {output_file}")
            
            return df
        else:
            self.logger.warning("Không có file nào được xử lý thành công")
            return pd.DataFrame()
    
    def create_dataset(self, malware_dir, benign_dir, output_file):
        """
        Tạo dataset hoàn chỉnh từ thư mục malware và benign
        
        Args:
            malware_dir (str): Thư mục chứa malware samples
            benign_dir (str): Thư mục chứa benign samples
            output_file (str): File CSV để lưu dataset
            
        Returns:
            pd.DataFrame: Dataset hoàn chỉnh
        """
        self.logger.info("Bắt đầu tạo dataset...")
        
        # Xử lý malware files
        self.logger.info("Xử lý malware samples...")
        malware_df = self.process_directory(malware_dir, 'malware')
        
        # Xử lý benign files
        self.logger.info("Xử lý benign samples...")
        benign_df = self.process_directory(benign_dir, 'benign')
        
        # Kết hợp datasets
        if not malware_df.empty and not benign_df.empty:
            dataset = pd.concat([malware_df, benign_df], ignore_index=True)
            
            # Shuffle dataset
            dataset = dataset.sample(frac=1, random_state=42).reset_index(drop=True)
            
            # Lưu dataset
            dataset.to_csv(output_file, index=False)
            
            self.logger.info(f"Dataset đã được tạo với {len(dataset)} samples:")
            self.logger.info(f"- Malware: {len(malware_df)} samples")
            self.logger.info(f"- Benign: {len(benign_df)} samples")
            self.logger.info(f"Dataset đã lưu vào: {output_file}")
            
            return dataset
        else:
            self.logger.error("Không thể tạo dataset - thiếu dữ liệu malware hoặc benign")
            return pd.DataFrame()
    
    @staticmethod
    def _process_single_file(file_path, label):
        """
        Xử lý một file PE đơn lẻ
        
        Args:
            file_path (str): Đường dẫn file
            label (str): Label của file
            
        Returns:
            dict: Dictionary chứa features và label
        """
        try:
            analyzer = PEAnalyzer()
            features = analyzer.analyze_file(file_path)
            
            if features is None:
                return None
            
            # Lấy feature vector
            feature_vector = analyzer.get_feature_vector()
            if feature_vector is None:
                return None
            
            # Tạo result dictionary
            result = {}
            feature_names = PEAnalyzer.get_feature_names()
            
            # Thêm features vào result
            for i, feature_name in enumerate(feature_names):
                result[feature_name] = feature_vector[i]
            
            # Thêm metadata
            result['file_path'] = file_path
            result['file_name'] = os.path.basename(file_path)
            result['label'] = label
            result['md5'] = features.get('md5', '')
            result['sha256'] = features.get('sha256', '')
            
            return result
            
        except Exception as e:
            # Log error nhưng không raise để không làm crash toàn bộ batch
            logging.getLogger(__name__).error(f"Lỗi xử lý {file_path}: {e}")
            return None
    
    def validate_dataset(self, dataset_file):
        """
        Kiểm tra tính hợp lệ của dataset
        
        Args:
            dataset_file (str): Đường dẫn file dataset
            
        Returns:
            dict: Thống kê về dataset
        """
        try:
            df = pd.read_csv(dataset_file)
            
            stats = {
                'total_samples': len(df),
                'malware_samples': len(df[df['label'] == 'malware']),
                'benign_samples': len(df[df['label'] == 'benign']),
                'features_count': len([col for col in df.columns if col not in ['file_path', 'file_name', 'label', 'md5', 'sha256']]),
                'missing_values': df.isnull().sum().sum(),
                'duplicate_hashes': df['md5'].duplicated().sum()
            }
            
            # Kiểm tra balance
            stats['balance_ratio'] = stats['malware_samples'] / stats['benign_samples'] if stats['benign_samples'] > 0 else float('inf')
            
            self.logger.info("Dataset statistics:")
            for key, value in stats.items():
                self.logger.info(f"  {key}: {value}")
            
            return stats
            
        except Exception as e:
            self.logger.error(f"Lỗi khi validate dataset: {e}")
            return None


if __name__ == "__main__":
    # Setup logging
    logging.basicConfig(level=logging.INFO, 
                       format='%(asctime)s - %(levelname)s - %(message)s')
    
    # Test batch processor
    processor = BatchProcessor(n_workers=2)
    
    # Ví dụ sử dụng (cần thay đổi đường dẫn thực tế)
    # malware_dir = "data/samples/malware"
    # benign_dir = "data/samples/benign"
    # output_file = "data/datasets/malware_dataset.csv"
    
    # dataset = processor.create_dataset(malware_dir, benign_dir, output_file)
    # stats = processor.validate_dataset(output_file)
    
    print("BatchProcessor đã sẵn sàng sử dụng!")
    print("Hãy cung cấp đường dẫn đến thư mục malware và benign samples để tạo dataset.")
