"""
Flask API Backend
API server cho AI Malware Detection System
"""

import os
import sys
from pathlib import Path
from flask import Flask, request, jsonify, render_template, send_from_directory
from flask_cors import CORS
from werkzeug.utils import secure_filename
import logging
import hashlib
import time
from datetime import datetime

# Add src to path
sys.path.append(str(Path(__file__).parent.parent))

from models.classifier import MalwareClassifier
from feature_extraction.pe_analyzer import PEAnalyzer
sys.path.append(str(Path(__file__).parent.parent.parent))
import config


# Initialize Flask app
app = Flask(__name__, 
           template_folder='../web/templates',
           static_folder='../web/static')
app.config['SECRET_KEY'] = config.FLASK_CONFIG['SECRET_KEY']
app.config['MAX_CONTENT_LENGTH'] = config.MAX_FILE_SIZE

# Enable CORS
CORS(app)

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Global classifier instance
classifier = None


def init_classifier():
    """Initialize the malware classifier"""
    global classifier
    try:
        classifier = MalwareClassifier('random_forest')
        
        # Try to load pre-trained model
        model_path = config.TRAINED_MODEL_PATH
        scaler_path = config.SCALER_PATH
        
        if model_path.exists() and scaler_path.exists():
            classifier.load_model(str(model_path), str(scaler_path))
            logger.info("Đã load pre-trained model thành công")
        else:
            logger.warning("Không tìm thấy pre-trained model. Cần train model trước!")
            
    except Exception as e:
        logger.error(f"Lỗi khi khởi tạo classifier: {e}")
        classifier = None


def allowed_file(filename):
    """Check if file extension is allowed"""
    return '.' in filename and \
           Path(filename).suffix.lower() in config.ALLOWED_EXTENSIONS


def calculate_file_hash(file_path):
    """Calculate MD5 and SHA256 hash of file"""
    md5_hash = hashlib.md5()
    sha256_hash = hashlib.sha256()
    
    with open(file_path, 'rb') as f:
        for chunk in iter(lambda: f.read(4096), b""):
            md5_hash.update(chunk)
            sha256_hash.update(chunk)
    
    return md5_hash.hexdigest(), sha256_hash.hexdigest()


@app.route('/')
def index():
    """Main page"""
    return render_template('index.html')


@app.route('/api/status')
def api_status():
    """API status endpoint"""
    global classifier
    
    status = {
        'status': 'online',
        'timestamp': datetime.now().isoformat(),
        'model_loaded': classifier is not None and classifier.is_trained,
        'version': '1.0.0'
    }
    
    return jsonify(status)


@app.route('/api/upload', methods=['POST'])
def upload_file():
    """Upload and analyze file endpoint"""
    global classifier
    
    try:
        # Check if classifier is ready
        if classifier is None or not classifier.is_trained:
            return jsonify({
                'error': 'Model chưa được load. Vui lòng train model trước!',
                'success': False
            }), 500
        
        # Check if file is in request
        if 'file' not in request.files:
            return jsonify({
                'error': 'Không tìm thấy file trong request',
                'success': False
            }), 400
        
        file = request.files['file']
        
        # Check if file is selected
        if file.filename == '':
            return jsonify({
                'error': 'Chưa chọn file',
                'success': False
            }), 400
        
        # Check file extension
        if not allowed_file(file.filename):
            return jsonify({
                'error': f'File extension không được hỗ trợ. Chỉ chấp nhận: {", ".join(config.ALLOWED_EXTENSIONS)}',
                'success': False
            }), 400
        
        # Save uploaded file
        filename = secure_filename(file.filename)
        timestamp = str(int(time.time()))
        unique_filename = f"{timestamp}_{filename}"
        
        # Ensure upload directory exists
        config.UPLOAD_FOLDER.mkdir(parents=True, exist_ok=True)
        file_path = config.UPLOAD_FOLDER / unique_filename
        
        file.save(str(file_path))
        logger.info(f"File uploaded: {file_path}")
        
        # Calculate file hashes
        md5_hash, sha256_hash = calculate_file_hash(file_path)
        
        # Analyze file
        start_time = time.time()
        prediction_result = classifier.predict(str(file_path))
        analysis_time = time.time() - start_time
        
        if prediction_result is None:
            # Clean up uploaded file
            file_path.unlink(missing_ok=True)
            return jsonify({
                'error': 'Không thể phân tích file. File có thể bị lỗi hoặc không phải PE file.',
                'success': False
            }), 400
        
        # Get detailed features for display
        analyzer = PEAnalyzer()
        detailed_features = analyzer.analyze_file(str(file_path))
        
        # Prepare response
        response = {
            'success': True,
            'filename': filename,
            'file_size': file_path.stat().st_size,
            'md5': md5_hash,
            'sha256': sha256_hash,
            'prediction': prediction_result['prediction'],
            'analysis_time': round(analysis_time, 3),
            'timestamp': datetime.now().isoformat()
        }
        
        # Add confidence if available
        if 'confidence' in prediction_result and prediction_result['confidence']:
            response['confidence'] = round(prediction_result['confidence'], 4)
            response['malware_probability'] = round(prediction_result['malware_probability'], 4)
            response['benign_probability'] = round(prediction_result['benign_probability'], 4)
        
        # Add some key features for display
        if detailed_features:
            response['features'] = {
                'file_entropy': round(detailed_features.get('file_entropy', 0), 3),
                'section_count': detailed_features.get('section_count', 0),
                'import_dll_count': detailed_features.get('import_dll_count', 0),
                'import_function_count': detailed_features.get('import_function_count', 0),
                'suspicious_import_count': detailed_features.get('suspicious_import_count', 0),
                'suspicious_string_count': detailed_features.get('suspicious_string_count', 0)
            }
        
        # Clean up uploaded file
        file_path.unlink(missing_ok=True)
        
        logger.info(f"Analysis completed: {filename} -> {prediction_result['prediction']}")
        
        return jsonify(response)
        
    except Exception as e:
        logger.error(f"Lỗi khi xử lý upload: {e}")
        return jsonify({
            'error': f'Lỗi server: {str(e)}',
            'success': False
        }), 500


@app.route('/api/analyze', methods=['POST'])
def analyze_file():
    """Analyze file by path (for internal use)"""
    global classifier
    
    try:
        data = request.get_json()
        
        if not data or 'file_path' not in data:
            return jsonify({
                'error': 'Thiếu file_path trong request',
                'success': False
            }), 400
        
        file_path = data['file_path']
        
        if not os.path.exists(file_path):
            return jsonify({
                'error': 'File không tồn tại',
                'success': False
            }), 400
        
        # Check if classifier is ready
        if classifier is None or not classifier.is_trained:
            return jsonify({
                'error': 'Model chưa được load',
                'success': False
            }), 500
        
        # Analyze file
        start_time = time.time()
        prediction_result = classifier.predict(file_path)
        analysis_time = time.time() - start_time
        
        if prediction_result is None:
            return jsonify({
                'error': 'Không thể phân tích file',
                'success': False
            }), 400
        
        response = {
            'success': True,
            'file_path': file_path,
            'prediction': prediction_result['prediction'],
            'analysis_time': round(analysis_time, 3),
            'timestamp': datetime.now().isoformat()
        }
        
        if 'confidence' in prediction_result and prediction_result['confidence']:
            response['confidence'] = round(prediction_result['confidence'], 4)
        
        return jsonify(response)
        
    except Exception as e:
        logger.error(f"Lỗi khi analyze file: {e}")
        return jsonify({
            'error': f'Lỗi server: {str(e)}',
            'success': False
        }), 500


@app.route('/api/model/info')
def model_info():
    """Get model information"""
    global classifier
    
    if classifier is None:
        return jsonify({
            'error': 'Classifier chưa được khởi tạo',
            'success': False
        }), 500
    
    info = {
        'model_type': classifier.model_type,
        'is_trained': classifier.is_trained,
        'feature_count': len(classifier.feature_names),
        'feature_names': classifier.feature_names
    }
    
    # Add feature importance if available
    if classifier.is_trained and classifier.model_type == 'random_forest':
        try:
            importance = classifier.get_feature_importance()
            if importance:
                # Get top 10 features
                top_features = list(importance.items())[:10]
                info['top_features'] = [
                    {'name': name, 'importance': round(imp, 4)} 
                    for name, imp in top_features
                ]
        except Exception as e:
            logger.warning(f"Không thể lấy feature importance: {e}")
    
    return jsonify(info)


@app.errorhandler(413)
def too_large(e):
    """Handle file too large error"""
    return jsonify({
        'error': f'File quá lớn. Kích thước tối đa: {config.MAX_FILE_SIZE // (1024*1024)}MB',
        'success': False
    }), 413


@app.errorhandler(404)
def not_found(e):
    """Handle 404 errors"""
    return jsonify({
        'error': 'Endpoint không tồn tại',
        'success': False
    }), 404


@app.errorhandler(500)
def internal_error(e):
    """Handle 500 errors"""
    return jsonify({
        'error': 'Lỗi server nội bộ',
        'success': False
    }), 500


if __name__ == '__main__':
    # Create necessary directories
    config.create_directories()
    
    # Initialize classifier
    init_classifier()
    
    # Run app
    app.run(
        host=config.FLASK_CONFIG['HOST'],
        port=config.FLASK_CONFIG['PORT'],
        debug=config.FLASK_CONFIG['DEBUG']
    )
