#!/bin/bash

# Deployment Script for Cloud Security Platform
# Script để deploy applications lên Kubernetes cluster

set -e

# Configuration
ENVIRONMENT=${1:-"development"}
PROJECT_ID=${PROJECT_ID:-"security-platform-prod"}
CLUSTER_NAME=${CLUSTER_NAME:-"security-platform-cluster"}
CLUSTER_LOCATION=${CLUSTER_LOCATION:-"asia-southeast1"}
NAMESPACE="security-platform"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check prerequisites
check_prerequisites() {
    log_info "Checking prerequisites..."
    
    # Check if kubectl is installed
    if ! command -v kubectl &> /dev/null; then
        log_error "kubectl is not installed"
        exit 1
    fi
    
    # Check if gcloud is installed
    if ! command -v gcloud &> /dev/null; then
        log_error "gcloud CLI is not installed"
        exit 1
    fi
    
    # Check if authenticated
    if ! gcloud auth list --filter=status:ACTIVE --format="value(account)" | head -n1 &> /dev/null; then
        log_error "gcloud is not authenticated. Run 'gcloud auth login'"
        exit 1
    fi
    
    log_success "Prerequisites check passed"
}

# Configure kubectl
configure_kubectl() {
    log_info "Configuring kubectl for cluster: ${CLUSTER_NAME}"
    
    gcloud container clusters get-credentials "${CLUSTER_NAME}" \
        --region "${CLUSTER_LOCATION}" \
        --project "${PROJECT_ID}"
    
    # Verify connection
    if kubectl cluster-info &> /dev/null; then
        log_success "kubectl configured successfully"
    else
        log_error "Failed to connect to Kubernetes cluster"
        exit 1
    fi
}

# Create namespace if it doesn't exist
create_namespace() {
    log_info "Creating namespace: ${NAMESPACE}"
    
    if kubectl get namespace "${NAMESPACE}" &> /dev/null; then
        log_info "Namespace ${NAMESPACE} already exists"
    else
        kubectl apply -f kubernetes/namespaces/security-platform.yaml
        log_success "Namespace ${NAMESPACE} created"
    fi
}

# Deploy secrets
deploy_secrets() {
    log_info "Deploying secrets..."
    
    # Check if secrets already exist
    if kubectl get secret database-secrets -n "${NAMESPACE}" &> /dev/null; then
        log_warning "Secrets already exist, skipping creation"
        log_warning "To update secrets, delete them first: kubectl delete secret database-secrets app-secrets -n ${NAMESPACE}"
    else
        kubectl apply -f kubernetes/configmaps/secrets.yaml
        log_success "Secrets deployed"
    fi
}

# Deploy ConfigMaps
deploy_configmaps() {
    log_info "Deploying ConfigMaps..."
    
    kubectl apply -f kubernetes/configmaps/app-config.yaml
    log_success "ConfigMaps deployed"
}

# Deploy RBAC
deploy_rbac() {
    log_info "Deploying RBAC configuration..."
    
    kubectl apply -f kubernetes/rbac/service-accounts.yaml
    log_success "RBAC configuration deployed"
}

# Deploy storage
deploy_storage() {
    log_info "Deploying storage resources..."
    
    kubectl apply -f kubernetes/storage/persistent-volumes.yaml
    log_success "Storage resources deployed"
    
    # Wait for PVCs to be bound
    log_info "Waiting for PVCs to be bound..."
    kubectl wait --for=condition=Bound pvc/models-pvc -n "${NAMESPACE}" --timeout=300s
    kubectl wait --for=condition=Bound pvc/logs-pvc -n "${NAMESPACE}" --timeout=300s
    kubectl wait --for=condition=Bound pvc/uploads-pvc -n "${NAMESPACE}" --timeout=300s
    log_success "All PVCs are bound"
}

# Deploy applications
deploy_applications() {
    log_info "Deploying applications..."
    
    # Deploy in order: storage dependencies first, then applications
    log_info "Deploying Redis..."
    kubectl apply -f kubernetes/storage/persistent-volumes.yaml
    kubectl wait --for=condition=available --timeout=300s deployment/redis -n "${NAMESPACE}"
    
    log_info "Deploying Malware Detection service..."
    kubectl apply -f kubernetes/deployments/malware-detection.yaml
    
    log_info "Deploying Network IDS service..."
    kubectl apply -f kubernetes/deployments/network-ids.yaml
    
    log_info "Deploying Dashboard and API Gateway..."
    kubectl apply -f kubernetes/deployments/dashboard.yaml
    
    log_success "Applications deployed"
}

# Wait for deployments
wait_for_deployments() {
    log_info "Waiting for deployments to be ready..."
    
    # List of deployments to wait for
    deployments=(
        "malware-detection"
        "network-ids"
        "security-dashboard"
        "api-gateway"
        "redis"
    )
    
    for deployment in "${deployments[@]}"; do
        log_info "Waiting for ${deployment}..."
        kubectl wait --for=condition=available --timeout=600s deployment/"${deployment}" -n "${NAMESPACE}"
        log_success "${deployment} is ready"
    done
}

# Check deployment status
check_deployment_status() {
    log_info "Checking deployment status..."
    
    echo ""
    echo "=== Namespace Status ==="
    kubectl get namespace "${NAMESPACE}"
    
    echo ""
    echo "=== Deployments ==="
    kubectl get deployments -n "${NAMESPACE}"
    
    echo ""
    echo "=== Pods ==="
    kubectl get pods -n "${NAMESPACE}" -o wide
    
    echo ""
    echo "=== Services ==="
    kubectl get services -n "${NAMESPACE}"
    
    echo ""
    echo "=== PersistentVolumeClaims ==="
    kubectl get pvc -n "${NAMESPACE}"
    
    echo ""
    echo "=== HorizontalPodAutoscalers ==="
    kubectl get hpa -n "${NAMESPACE}" || echo "No HPAs found"
    
    # Check for any failed pods
    FAILED_PODS=$(kubectl get pods -n "${NAMESPACE}" --field-selector=status.phase!=Running --no-headers 2>/dev/null | wc -l)
    
    if [ "$FAILED_PODS" -eq "0" ]; then
        log_success "All pods are running successfully"
    else
        log_warning "Some pods are not running:"
        kubectl get pods -n "${NAMESPACE}" --field-selector=status.phase!=Running
    fi
}

# Get external access information
get_external_access() {
    log_info "Getting external access information..."
    
    # Get external IP
    EXTERNAL_IP=$(kubectl get service api-gateway-service -n "${NAMESPACE}" -o jsonpath='{.status.loadBalancer.ingress[0].ip}' 2>/dev/null || echo "")
    
    if [ -z "$EXTERNAL_IP" ]; then
        log_warning "External IP not yet assigned. Checking again..."
        sleep 30
        EXTERNAL_IP=$(kubectl get service api-gateway-service -n "${NAMESPACE}" -o jsonpath='{.status.loadBalancer.ingress[0].ip}' 2>/dev/null || echo "")
    fi
    
    echo ""
    echo "=== External Access ==="
    if [ ! -z "$EXTERNAL_IP" ]; then
        echo "🌐 External IP: $EXTERNAL_IP"
        echo "🔗 Application URLs:"
        echo "   • Dashboard: http://$EXTERNAL_IP/"
        echo "   • Malware Detection API: http://$EXTERNAL_IP/api/malware/"
        echo "   • Network IDS API: http://$EXTERNAL_IP/api/ids/"
        echo ""
        echo "📋 Quick Health Checks:"
        echo "   curl http://$EXTERNAL_IP/health"
        echo "   curl http://$EXTERNAL_IP/api/malware/status"
        echo "   curl http://$EXTERNAL_IP/api/ids/status"
    else
        echo "⏳ External IP is still being assigned..."
        echo "   Check status: kubectl get service api-gateway-service -n ${NAMESPACE}"
    fi
}

# Run health checks
run_health_checks() {
    log_info "Running health checks..."
    
    # Get external IP
    EXTERNAL_IP=$(kubectl get service api-gateway-service -n "${NAMESPACE}" -o jsonpath='{.status.loadBalancer.ingress[0].ip}' 2>/dev/null || echo "")
    
    if [ ! -z "$EXTERNAL_IP" ]; then
        echo "Testing endpoints..."
        
        # Test API Gateway health
        if curl -f -s "http://$EXTERNAL_IP/health" > /dev/null; then
            log_success "API Gateway health check passed"
        else
            log_warning "API Gateway health check failed"
        fi
        
        # Test Malware Detection API
        if curl -f -s "http://$EXTERNAL_IP/api/malware/status" > /dev/null; then
            log_success "Malware Detection API health check passed"
        else
            log_warning "Malware Detection API health check failed"
        fi
        
        # Test Network IDS API
        if curl -f -s "http://$EXTERNAL_IP/api/ids/status" > /dev/null; then
            log_success "Network IDS API health check passed"
        else
            log_warning "Network IDS API health check failed"
        fi
    else
        log_warning "External IP not available, skipping external health checks"
    fi
    
    # Internal health checks
    log_info "Running internal health checks..."
    
    # Check if all pods are ready
    NOT_READY=$(kubectl get pods -n "${NAMESPACE}" --no-headers | grep -v "Running\|Completed" | wc -l)
    
    if [ "$NOT_READY" -eq "0" ]; then
        log_success "All pods are ready"
    else
        log_warning "$NOT_READY pods are not ready"
    fi
}

# Main deployment function
main() {
    log_info "Starting deployment to ${ENVIRONMENT} environment..."
    log_info "Project: ${PROJECT_ID}"
    log_info "Cluster: ${CLUSTER_NAME}"
    log_info "Location: ${CLUSTER_LOCATION}"
    log_info "Namespace: ${NAMESPACE}"
    
    # Run deployment steps
    check_prerequisites
    configure_kubectl
    create_namespace
    deploy_secrets
    deploy_configmaps
    deploy_rbac
    deploy_storage
    deploy_applications
    wait_for_deployments
    check_deployment_status
    get_external_access
    run_health_checks
    
    log_success "Deployment completed successfully!"
    
    echo ""
    echo "=== Next Steps ==="
    echo "1. Monitor the deployment: kubectl get pods -n ${NAMESPACE} -w"
    echo "2. Check logs: kubectl logs -f deployment/malware-detection -n ${NAMESPACE}"
    echo "3. Access the dashboard at the external IP shown above"
    echo "4. Set up monitoring and alerting"
    echo "5. Configure DNS if using a custom domain"
}

# Handle script arguments
case "${1:-}" in
    "development"|"staging"|"production")
        main
        ;;
    "--help"|"-h")
        echo "Usage: $0 [environment]"
        echo ""
        echo "Environments:"
        echo "  development  - Deploy to development environment"
        echo "  staging      - Deploy to staging environment"
        echo "  production   - Deploy to production environment"
        echo ""
        echo "Environment variables:"
        echo "  PROJECT_ID       - GCP Project ID (default: security-platform-prod)"
        echo "  CLUSTER_NAME     - GKE Cluster name (default: security-platform-cluster)"
        echo "  CLUSTER_LOCATION - GKE Cluster location (default: asia-southeast1)"
        exit 0
        ;;
    *)
        log_error "Invalid environment: ${1:-}"
        echo "Use: $0 [development|staging|production]"
        echo "Or: $0 --help for more information"
        exit 1
        ;;
esac
