version: '3.8'

services:
  # PostgreSQL Database
  postgresql:
    image: postgres:14
    container_name: security-postgresql
    environment:
      POSTGRES_DB: security_platform
      POSTGRES_USER: security_user
      POSTGRES_PASSWORD: ${DB_PASSWORD:-secure_password}
      POSTGRES_INITDB_ARGS: "--encoding=UTF-8"
    volumes:
      - postgresql_data:/var/lib/postgresql/data
      - ./database/init.sql:/docker-entrypoint-initdb.d/init.sql
    ports:
      - "5432:5432"
    networks:
      - security-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U security_user -d security_platform"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Redis Cache
  redis:
    image: redis:7-alpine
    container_name: security-redis
    command: redis-server --appendonly yes --maxmemory 512mb --maxmemory-policy allkeys-lru
    volumes:
      - redis_data:/data
    ports:
      - "6379:6379"
    networks:
      - security-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  # MinIO Object Storage
  minio:
    image: minio/minio:latest
    container_name: security-minio
    command: server /data --console-address ":9001"
    environment:
      MINIO_ROOT_USER: ${MINIO_ACCESS_KEY:-minioadmin}
      MINIO_ROOT_PASSWORD: ${MINIO_SECRET_KEY:-minioadmin123}
    volumes:
      - minio_data:/data
    ports:
      - "9000:9000"
      - "9001:9001"
    networks:
      - security-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:9000/minio/health/live"]
      interval: 30s
      timeout: 10s
      retries: 3

  # MinIO Client (for bucket creation)
  minio-client:
    image: minio/mc:latest
    container_name: security-minio-client
    depends_on:
      - minio
    networks:
      - security-network
    entrypoint: >
      /bin/sh -c "
      sleep 10;
      /usr/bin/mc alias set local http://minio:9000 ${MINIO_ACCESS_KEY:-minioadmin} ${MINIO_SECRET_KEY:-minioadmin123};
      /usr/bin/mc mb local/models;
      /usr/bin/mc mb local/uploads;
      /usr/bin/mc mb local/logs;
      /usr/bin/mc mb local/backups;
      /usr/bin/mc policy set public local/models;
      exit 0;
      "

  # AI Malware Detection Service
  malware-detection:
    build:
      context: ../docker/malware-detection
      dockerfile: Dockerfile
    container_name: security-malware-detection
    environment:
      - DB_HOST=postgresql
      - DB_PORT=5432
      - DB_NAME=security_platform
      - DB_USER=security_user
      - DB_PASSWORD=${DB_PASSWORD:-secure_password}
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - MINIO_ENDPOINT=minio:9000
      - MINIO_ACCESS_KEY=${MINIO_ACCESS_KEY:-minioadmin}
      - MINIO_SECRET_KEY=${MINIO_SECRET_KEY:-minioadmin123}
      - SECRET_KEY=${SECRET_KEY:-your-secret-key}
      - FLASK_ENV=production
    volumes:
      - ./uploads:/app/uploads
      - ./models:/app/models
    ports:
      - "5000:5000"
    networks:
      - security-network
    depends_on:
      - postgresql
      - redis
      - minio
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:5000/api/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Network IDS Service
  network-ids:
    build:
      context: ../docker/network-ids
      dockerfile: Dockerfile
    container_name: security-network-ids
    environment:
      - DB_HOST=postgresql
      - DB_PORT=5432
      - DB_NAME=security_platform
      - DB_USER=security_user
      - DB_PASSWORD=${DB_PASSWORD:-secure_password}
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - SECRET_KEY=${SECRET_KEY:-your-secret-key}
      - FLASK_ENV=production
    volumes:
      - ./pcap:/app/pcap
      - ./models:/app/models
    ports:
      - "5001:5001"
    networks:
      - security-network
    depends_on:
      - postgresql
      - redis
    restart: unless-stopped
    cap_add:
      - NET_ADMIN
      - NET_RAW
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:5001/api/status"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Security Dashboard
  security-dashboard:
    build:
      context: ../docker/dashboard
      dockerfile: Dockerfile
    container_name: security-dashboard
    environment:
      - DB_HOST=postgresql
      - DB_PORT=5432
      - DB_NAME=security_platform
      - DB_USER=security_user
      - DB_PASSWORD=${DB_PASSWORD:-secure_password}
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - MALWARE_API_URL=http://malware-detection:5000
      - IDS_API_URL=http://network-ids:5001
      - SECRET_KEY=${SECRET_KEY:-your-secret-key}
      - FLASK_ENV=production
    ports:
      - "8080:8080"
    networks:
      - security-network
    depends_on:
      - postgresql
      - redis
      - malware-detection
      - network-ids
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # API Gateway (Nginx)
  api-gateway:
    build:
      context: ../docker/api-gateway
      dockerfile: Dockerfile
    container_name: security-api-gateway
    ports:
      - "80:80"
      - "443:443"
    networks:
      - security-network
    depends_on:
      - malware-detection
      - network-ids
      - security-dashboard
    restart: unless-stopped
    volumes:
      - ./ssl:/etc/nginx/ssl
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Prometheus Monitoring
  prometheus:
    image: prom/prometheus:v2.45.0
    container_name: security-prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=30d'
      - '--web.enable-lifecycle'
    volumes:
      - ./monitoring/prometheus/prometheus.yml:/etc/prometheus/prometheus.yml
      - ./monitoring/prometheus/rules:/etc/prometheus/rules
      - prometheus_data:/prometheus
    ports:
      - "9090:9090"
    networks:
      - security-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "wget", "--quiet", "--tries=1", "--spider", "http://localhost:9090/-/healthy"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Grafana Dashboard
  grafana:
    image: grafana/grafana:10.0.0
    container_name: security-grafana
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=${GRAFANA_PASSWORD:-admin123}
      - GF_USERS_ALLOW_SIGN_UP=false
      - GF_INSTALL_PLUGINS=grafana-piechart-panel,grafana-worldmap-panel
    volumes:
      - grafana_data:/var/lib/grafana
      - ./monitoring/grafana/provisioning:/etc/grafana/provisioning
      - ./monitoring/grafana/dashboards:/var/lib/grafana/dashboards
    ports:
      - "3000:3000"
    networks:
      - security-network
    depends_on:
      - prometheus
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "curl -f http://localhost:3000/api/health || exit 1"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Elasticsearch (for logging)
  elasticsearch:
    image: docker.elastic.co/elasticsearch/elasticsearch:8.8.0
    container_name: security-elasticsearch
    environment:
      - discovery.type=single-node
      - xpack.security.enabled=false
      - "ES_JAVA_OPTS=-Xms512m -Xmx512m"
    volumes:
      - elasticsearch_data:/usr/share/elasticsearch/data
    ports:
      - "9200:9200"
    networks:
      - security-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "curl -f http://localhost:9200/_cluster/health || exit 1"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Kibana (for log visualization)
  kibana:
    image: docker.elastic.co/kibana/kibana:8.8.0
    container_name: security-kibana
    environment:
      - ELASTICSEARCH_HOSTS=http://elasticsearch:9200
    ports:
      - "5601:5601"
    networks:
      - security-network
    depends_on:
      - elasticsearch
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "curl -f http://localhost:5601/api/status || exit 1"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Logstash (for log processing)
  logstash:
    image: docker.elastic.co/logstash/logstash:8.8.0
    container_name: security-logstash
    volumes:
      - ./logging/logstash/pipeline:/usr/share/logstash/pipeline
      - ./logging/logstash/config:/usr/share/logstash/config
    ports:
      - "5044:5044"
    networks:
      - security-network
    depends_on:
      - elasticsearch
    restart: unless-stopped

volumes:
  postgresql_data:
    driver: local
  redis_data:
    driver: local
  minio_data:
    driver: local
  prometheus_data:
    driver: local
  grafana_data:
    driver: local
  elasticsearch_data:
    driver: local

networks:
  security-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
