"""
Unit Tests for API
Tests cho Flask API endpoints
"""

import unittest
import json
import os
import sys
import tempfile
from pathlib import Path
import io

# Add src to path
sys.path.append(str(Path(__file__).parent.parent / 'src'))

from api.app import app
from api.database import init_database


class TestAPIEndpoints(unittest.TestCase):
    """Test cases cho API endpoints"""
    
    def setUp(self):
        """Setup test environment"""
        # Configure app for testing
        app.config['TESTING'] = True
        app.config['WTF_CSRF_ENABLED'] = False
        
        # Initialize test database
        init_database()
        
        # Create test client
        self.client = app.test_client()
        self.app_context = app.app_context()
        self.app_context.push()
    
    def tearDown(self):
        """Cleanup after tests"""
        self.app_context.pop()
    
    def test_index_page(self):
        """Test main index page"""
        response = self.client.get('/')
        
        self.assertEqual(response.status_code, 200)
        self.assertIn(b'AI Malware Detection', response.data)
    
    def test_api_status(self):
        """Test API status endpoint"""
        response = self.client.get('/api/status')
        
        self.assertEqual(response.status_code, 200)
        
        data = json.loads(response.data)
        self.assertIn('status', data)
        self.assertIn('timestamp', data)
        self.assertIn('model_loaded', data)
        self.assertIn('version', data)
        
        self.assertEqual(data['status'], 'online')
    
    def test_model_info(self):
        """Test model info endpoint"""
        response = self.client.get('/api/model/info')
        
        # Should return 200 even if model is not trained
        self.assertEqual(response.status_code, 200)
        
        data = json.loads(response.data)
        self.assertIn('model_type', data)
        self.assertIn('is_trained', data)
        self.assertIn('feature_count', data)
        self.assertIn('feature_names', data)
    
    def test_upload_no_file(self):
        """Test upload endpoint without file"""
        response = self.client.post('/api/upload')
        
        self.assertEqual(response.status_code, 400)
        
        data = json.loads(response.data)
        self.assertFalse(data['success'])
        self.assertIn('error', data)
    
    def test_upload_empty_filename(self):
        """Test upload endpoint with empty filename"""
        response = self.client.post('/api/upload', data={
            'file': (io.BytesIO(b''), '')
        })
        
        self.assertEqual(response.status_code, 400)
        
        data = json.loads(response.data)
        self.assertFalse(data['success'])
        self.assertIn('Chưa chọn file', data['error'])
    
    def test_upload_invalid_extension(self):
        """Test upload endpoint with invalid file extension"""
        response = self.client.post('/api/upload', data={
            'file': (io.BytesIO(b'test content'), 'test.txt')
        })
        
        self.assertEqual(response.status_code, 400)
        
        data = json.loads(response.data)
        self.assertFalse(data['success'])
        self.assertIn('File extension không được hỗ trợ', data['error'])
    
    def test_upload_valid_extension_no_model(self):
        """Test upload with valid extension but no trained model"""
        # Create a fake PE file
        fake_pe_content = b'MZ' + b'\x00' * 100  # Minimal PE header
        
        response = self.client.post('/api/upload', data={
            'file': (io.BytesIO(fake_pe_content), 'test.exe')
        })
        
        # Should return 500 if model is not loaded
        self.assertEqual(response.status_code, 500)
        
        data = json.loads(response.data)
        self.assertFalse(data['success'])
        self.assertIn('Model chưa được load', data['error'])
    
    def test_analyze_endpoint_no_data(self):
        """Test analyze endpoint without data"""
        response = self.client.post('/api/analyze',
                                  content_type='application/json')
        
        self.assertEqual(response.status_code, 400)
        
        data = json.loads(response.data)
        self.assertFalse(data['success'])
        self.assertIn('Thiếu file_path', data['error'])
    
    def test_analyze_endpoint_invalid_path(self):
        """Test analyze endpoint with invalid file path"""
        response = self.client.post('/api/analyze',
                                  data=json.dumps({'file_path': '/invalid/path.exe'}),
                                  content_type='application/json')
        
        self.assertEqual(response.status_code, 400)
        
        data = json.loads(response.data)
        self.assertFalse(data['success'])
        self.assertIn('File không tồn tại', data['error'])
    
    def test_404_error_handler(self):
        """Test 404 error handler"""
        response = self.client.get('/nonexistent-endpoint')
        
        self.assertEqual(response.status_code, 404)
        
        data = json.loads(response.data)
        self.assertFalse(data['success'])
        self.assertIn('Endpoint không tồn tại', data['error'])
    
    def test_file_too_large_error(self):
        """Test file too large error handling"""
        # This test would require creating a very large file
        # For now, we'll just test that the error handler exists
        
        # Create a large content (simulate)
        large_content = b'A' * (51 * 1024 * 1024)  # 51MB
        
        response = self.client.post('/api/upload', data={
            'file': (io.BytesIO(large_content), 'large_file.exe')
        })
        
        # Should return 413 (Request Entity Too Large)
        self.assertEqual(response.status_code, 413)
        
        data = json.loads(response.data)
        self.assertFalse(data['success'])
        self.assertIn('File quá lớn', data['error'])


class TestAPIIntegration(unittest.TestCase):
    """Integration tests for API"""
    
    def setUp(self):
        """Setup test environment"""
        app.config['TESTING'] = True
        self.client = app.test_client()
        self.app_context = app.app_context()
        self.app_context.push()
        
        # Sample PE file for testing
        self.sample_pe_file = "C:\\Windows\\System32\\notepad.exe"
    
    def tearDown(self):
        """Cleanup after tests"""
        self.app_context.pop()
    
    def test_full_upload_workflow(self):
        """Test complete upload workflow (if model is available)"""
        # First check if model is loaded
        status_response = self.client.get('/api/status')
        status_data = json.loads(status_response.data)
        
        if not status_data.get('model_loaded', False):
            self.skipTest("Model not loaded, skipping integration test")
        
        # Test with a real PE file if available
        if not os.path.exists(self.sample_pe_file):
            self.skipTest(f"Sample PE file not found: {self.sample_pe_file}")
        
        with open(self.sample_pe_file, 'rb') as f:
            file_content = f.read()
        
        response = self.client.post('/api/upload', data={
            'file': (io.BytesIO(file_content), 'notepad.exe')
        })
        
        # Should succeed if model is loaded
        if response.status_code == 200:
            data = json.loads(response.data)
            self.assertTrue(data['success'])
            self.assertIn('prediction', data)
            self.assertIn('filename', data)
            self.assertIn('file_size', data)
            self.assertIn('md5', data)
            self.assertIn('sha256', data)
            self.assertIn('analysis_time', data)
            
            # Prediction should be either 'malware' or 'benign'
            self.assertIn(data['prediction'], ['malware', 'benign'])


class TestAPIPerformance(unittest.TestCase):
    """Performance tests for API"""
    
    def setUp(self):
        """Setup test environment"""
        app.config['TESTING'] = True
        self.client = app.test_client()
        self.app_context = app.app_context()
        self.app_context.push()
    
    def tearDown(self):
        """Cleanup after tests"""
        self.app_context.pop()
    
    def test_status_endpoint_performance(self):
        """Test status endpoint response time"""
        import time
        
        start_time = time.time()
        response = self.client.get('/api/status')
        end_time = time.time()
        
        response_time = end_time - start_time
        
        self.assertEqual(response.status_code, 200)
        self.assertLess(response_time, 1.0)  # Should respond within 1 second
        
        print(f"Status endpoint responded in {response_time:.3f} seconds")
    
    def test_concurrent_requests(self):
        """Test handling of concurrent requests"""
        import threading
        import time
        
        results = []
        
        def make_request():
            response = self.client.get('/api/status')
            results.append(response.status_code)
        
        # Create multiple threads
        threads = []
        for _ in range(10):
            thread = threading.Thread(target=make_request)
            threads.append(thread)
        
        # Start all threads
        start_time = time.time()
        for thread in threads:
            thread.start()
        
        # Wait for all threads to complete
        for thread in threads:
            thread.join()
        
        end_time = time.time()
        total_time = end_time - start_time
        
        # All requests should succeed
        self.assertEqual(len(results), 10)
        for status_code in results:
            self.assertEqual(status_code, 200)
        
        # Should handle concurrent requests efficiently
        self.assertLess(total_time, 5.0)
        
        print(f"10 concurrent requests completed in {total_time:.3f} seconds")


if __name__ == '__main__':
    # Create test suite
    suite = unittest.TestSuite()
    
    # Add test cases
    suite.addTest(unittest.makeSuite(TestAPIEndpoints))
    suite.addTest(unittest.makeSuite(TestAPIIntegration))
    suite.addTest(unittest.makeSuite(TestAPIPerformance))
    
    # Run tests
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(suite)
    
    # Print summary
    print(f"\nAPI Tests Summary:")
    print(f"Tests run: {result.testsRun}")
    print(f"Failures: {len(result.failures)}")
    print(f"Errors: {len(result.errors)}")
    
    if result.failures:
        print("\nFailures:")
        for test, traceback in result.failures:
            print(f"- {test}")
    
    if result.errors:
        print("\nErrors:")
        for test, traceback in result.errors:
            print(f"- {test}")
    
    # Exit with appropriate code
    sys.exit(0 if result.wasSuccessful() else 1)
