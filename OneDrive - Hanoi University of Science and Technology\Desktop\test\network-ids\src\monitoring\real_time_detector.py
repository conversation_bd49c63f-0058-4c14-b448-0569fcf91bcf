"""
Real-time Detector Module
Module ph<PERSON>t hiện intrusion và anomaly real-time
"""

import sys
import time
import threading
import pandas as pd
import numpy as np
from pathlib import Path
from datetime import datetime, timedelta
from collections import deque, defaultdict
import logging

# Add parent directory to path
sys.path.append(str(Path(__file__).parent.parent.parent))

import config
from ml_models.anomaly_detector import AnomalyDetector
from ml_models.intrusion_classifier import IntrusionClassifier
from traffic_analyzer.feature_extractor import FeatureExtractor
from monitoring.alert_system import AlertManager


class RealTimeDetector:
    """Class phát hiện real-time intrusions và anomalies"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        
        # ML Models
        self.anomaly_detector = None
        self.intrusion_classifier = None
        self.feature_extractor = FeatureExtractor()
        
        # Alert system
        self.alert_manager = AlertManager()
        
        # Detection state
        self.is_detecting = False
        self.detection_thread = None
        self.stop_event = threading.Event()
        
        # Packet buffer for analysis
        self.packet_buffer = deque(maxlen=config.ANOMALY_CONFIG['window_size'])
        self.feature_buffer = deque(maxlen=100)  # Store extracted features
        
        # Statistics
        self.detection_stats = {
            'total_analyzed': 0,
            'anomalies_detected': 0,
            'intrusions_detected': 0,
            'false_positives': 0,
            'last_detection_time': None,
            'detection_rate': 0.0
        }
        
        # Load models
        self._load_models()
    
    def _load_models(self):
        """Load trained ML models"""
        try:
            # Load anomaly detector
            anomaly_model_path = config.MODEL_DIR / "isolation_forest_anomaly_model.joblib"
            anomaly_scaler_path = config.MODEL_DIR / "isolation_forest_anomaly_scaler.joblib"
            
            if anomaly_model_path.exists() and anomaly_scaler_path.exists():
                self.anomaly_detector = AnomalyDetector('isolation_forest')
                self.anomaly_detector.load_model(str(anomaly_model_path), str(anomaly_scaler_path))
                self.logger.info("Anomaly detector loaded successfully")
            else:
                self.logger.warning("Anomaly detector model không tồn tại")
            
            # Load intrusion classifier
            intrusion_model_path = config.MODEL_DIR / "random_forest_intrusion_model.joblib"
            intrusion_scaler_path = config.MODEL_DIR / "random_forest_intrusion_scaler.joblib"
            
            if intrusion_model_path.exists() and intrusion_scaler_path.exists():
                self.intrusion_classifier = IntrusionClassifier('random_forest')
                self.intrusion_classifier.load_model(str(intrusion_model_path), str(intrusion_scaler_path))
                self.logger.info("Intrusion classifier loaded successfully")
            else:
                self.logger.warning("Intrusion classifier model không tồn tại")
                
        except Exception as e:
            self.logger.error(f"Lỗi khi load models: {e}")
    
    def start_detection(self):
        """Bắt đầu real-time detection"""
        if self.is_detecting:
            self.logger.warning("Detection đã đang chạy")
            return
        
        if not self.anomaly_detector and not self.intrusion_classifier:
            self.logger.error("Không có model nào được load. Không thể bắt đầu detection.")
            return
        
        self.logger.info("Bắt đầu real-time detection...")
        
        self.is_detecting = True
        self.stop_event.clear()
        
        # Start detection thread
        self.detection_thread = threading.Thread(target=self._detection_worker)
        self.detection_thread.daemon = True
        self.detection_thread.start()
    
    def stop_detection(self):
        """Dừng real-time detection"""
        if not self.is_detecting:
            self.logger.warning("Detection không đang chạy")
            return
        
        self.logger.info("Đang dừng real-time detection...")
        
        self.stop_event.set()
        
        if self.detection_thread and self.detection_thread.is_alive():
            self.detection_thread.join(timeout=5)
        
        self.is_detecting = False
        self.logger.info("Real-time detection đã dừng")
    
    def add_packet(self, packet):
        """
        Thêm packet vào buffer để phân tích
        
        Args:
            packet: Scapy packet object
        """
        if self.is_detecting:
            self.packet_buffer.append(packet)
    
    def _detection_worker(self):
        """Worker thread cho real-time detection"""
        while not self.stop_event.is_set():
            try:
                # Check if we have enough packets to analyze
                if len(self.packet_buffer) >= config.ANOMALY_CONFIG['window_size']:
                    packets = list(self.packet_buffer)
                    self._analyze_packets(packets)
                    
                    # Clear some packets to prevent memory issues
                    for _ in range(len(packets) // 2):
                        if self.packet_buffer:
                            self.packet_buffer.popleft()
                
                time.sleep(1)  # Check every second
                
            except Exception as e:
                self.logger.error(f"Lỗi trong detection worker: {e}")
    
    def _analyze_packets(self, packets):
        """
        Phân tích packets để detect anomalies và intrusions
        
        Args:
            packets: List of packets to analyze
        """
        try:
            # Extract features from packets
            flow_features = self.feature_extractor.extract_flow_features(packets)
            
            if not flow_features:
                return
            
            # Convert to DataFrame
            df = pd.DataFrame(flow_features)
            
            # Store features for later analysis
            self.feature_buffer.extend(flow_features)
            
            # Update statistics
            self.detection_stats['total_analyzed'] += len(flow_features)
            self.detection_stats['last_detection_time'] = datetime.now()
            
            # Anomaly detection
            if self.anomaly_detector and self.anomaly_detector.is_trained:
                self._detect_anomalies(df)
            
            # Intrusion classification
            if self.intrusion_classifier and self.intrusion_classifier.is_trained:
                self._classify_intrusions(df)
            
            # Update detection rate
            self._update_detection_rate()
            
        except Exception as e:
            self.logger.error(f"Lỗi khi phân tích packets: {e}")
    
    def _detect_anomalies(self, df):
        """
        Detect anomalies trong traffic
        
        Args:
            df (pd.DataFrame): Features DataFrame
        """
        try:
            # Predict anomalies
            predictions = self.anomaly_detector.predict(df)
            probabilities = self.anomaly_detector.predict_proba(df)
            
            # Process results
            for i, (prediction, prob) in enumerate(zip(predictions, probabilities)):
                if prediction == 'anomaly' and prob > self.anomaly_detector.threshold:
                    # Create alert
                    flow_data = df.iloc[i]
                    
                    self.alert_manager.create_alert(
                        alert_type='ANOMALY_DETECTED',
                        severity=self._get_severity_from_probability(prob),
                        source_ip=flow_data.get('src_ip', 'unknown'),
                        message=f'Network anomaly detected (confidence: {prob:.3f})',
                        details={
                            'anomaly_score': float(prob),
                            'flow_features': flow_data.to_dict(),
                            'detection_method': 'anomaly_detector'
                        }
                    )
                    
                    self.detection_stats['anomalies_detected'] += 1
                    
        except Exception as e:
            self.logger.error(f"Lỗi trong anomaly detection: {e}")
    
    def _classify_intrusions(self, df):
        """
        Classify intrusion types
        
        Args:
            df (pd.DataFrame): Features DataFrame
        """
        try:
            # Predict intrusion types
            predictions = self.intrusion_classifier.predict(df)
            probabilities = self.intrusion_classifier.predict_proba(df)
            
            # Process results
            for i, (prediction, prob_array) in enumerate(zip(predictions, probabilities)):
                if prediction != 'normal':
                    # Get confidence for predicted class
                    predicted_class_idx = list(self.intrusion_classifier.classes_).index(prediction)
                    confidence = prob_array[predicted_class_idx]
                    
                    if confidence > config.INTRUSION_CONFIG['confidence_threshold']:
                        # Create alert
                        flow_data = df.iloc[i]
                        
                        severity = self._get_severity_from_attack_type(prediction)
                        
                        self.alert_manager.create_alert(
                            alert_type=f'INTRUSION_{prediction.upper()}',
                            severity=severity,
                            source_ip=flow_data.get('src_ip', 'unknown'),
                            message=f'{prediction.upper()} attack detected (confidence: {confidence:.3f})',
                            details={
                                'attack_type': prediction,
                                'confidence': float(confidence),
                                'probabilities': {
                                    cls: float(prob) 
                                    for cls, prob in zip(self.intrusion_classifier.classes_, prob_array)
                                },
                                'flow_features': flow_data.to_dict(),
                                'detection_method': 'intrusion_classifier'
                            }
                        )
                        
                        self.detection_stats['intrusions_detected'] += 1
                        
        except Exception as e:
            self.logger.error(f"Lỗi trong intrusion classification: {e}")
    
    def _get_severity_from_probability(self, probability):
        """
        Xác định severity từ anomaly probability
        
        Args:
            probability (float): Anomaly probability
            
        Returns:
            str: Severity level
        """
        if probability >= 0.9:
            return 'critical'
        elif probability >= 0.8:
            return 'high'
        elif probability >= 0.6:
            return 'medium'
        else:
            return 'low'
    
    def _get_severity_from_attack_type(self, attack_type):
        """
        Xác định severity từ attack type
        
        Args:
            attack_type (str): Type of attack
            
        Returns:
            str: Severity level
        """
        severity_mapping = {
            'dos': 'critical',
            'u2r': 'critical',
            'r2l': 'high',
            'probe': 'medium',
            'port_scan': 'high',
            'brute_force': 'high',
            'sql_injection': 'critical'
        }
        
        return severity_mapping.get(attack_type.lower(), 'medium')
    
    def _update_detection_rate(self):
        """Update detection rate statistics"""
        total_detections = (
            self.detection_stats['anomalies_detected'] + 
            self.detection_stats['intrusions_detected']
        )
        
        if self.detection_stats['total_analyzed'] > 0:
            self.detection_stats['detection_rate'] = (
                total_detections / self.detection_stats['total_analyzed']
            )
    
    def get_detection_stats(self):
        """
        Lấy detection statistics
        
        Returns:
            dict: Detection statistics
        """
        stats = dict(self.detection_stats)
        
        # Add model status
        stats['models_loaded'] = {
            'anomaly_detector': self.anomaly_detector is not None and self.anomaly_detector.is_trained,
            'intrusion_classifier': self.intrusion_classifier is not None and self.intrusion_classifier.is_trained
        }
        
        # Add alert statistics
        alert_stats = self.alert_manager.get_alert_statistics(time_range_hours=1)
        stats['recent_alerts'] = alert_stats
        
        return stats
    
    def get_recent_detections(self, limit=50):
        """
        Lấy recent detections
        
        Args:
            limit (int): Number of detections to return
            
        Returns:
            list: Recent detections
        """
        return self.alert_manager.get_active_alerts(limit=limit)
    
    def manual_analysis(self, packets):
        """
        Phân tích manual một batch packets
        
        Args:
            packets: List of packets
            
        Returns:
            dict: Analysis results
        """
        try:
            # Extract features
            flow_features = self.feature_extractor.extract_flow_features(packets)
            
            if not flow_features:
                return {'error': 'Không thể extract features'}
            
            df = pd.DataFrame(flow_features)
            results = {
                'total_flows': len(flow_features),
                'anomalies': [],
                'intrusions': []
            }
            
            # Anomaly detection
            if self.anomaly_detector and self.anomaly_detector.is_trained:
                predictions = self.anomaly_detector.predict(df)
                probabilities = self.anomaly_detector.predict_proba(df)
                
                for i, (pred, prob) in enumerate(zip(predictions, probabilities)):
                    if pred == 'anomaly':
                        results['anomalies'].append({
                            'flow_index': i,
                            'probability': float(prob),
                            'features': df.iloc[i].to_dict()
                        })
            
            # Intrusion classification
            if self.intrusion_classifier and self.intrusion_classifier.is_trained:
                predictions = self.intrusion_classifier.predict(df)
                probabilities = self.intrusion_classifier.predict_proba(df)
                
                for i, (pred, prob_array) in enumerate(zip(predictions, probabilities)):
                    if pred != 'normal':
                        predicted_class_idx = list(self.intrusion_classifier.classes_).index(pred)
                        confidence = prob_array[predicted_class_idx]
                        
                        results['intrusions'].append({
                            'flow_index': i,
                            'attack_type': pred,
                            'confidence': float(confidence),
                            'features': df.iloc[i].to_dict()
                        })
            
            return results
            
        except Exception as e:
            self.logger.error(f"Lỗi trong manual analysis: {e}")
            return {'error': str(e)}


def main():
    """Main function để test real-time detector"""
    import argparse
    
    parser = argparse.ArgumentParser(description='Real-time Network Intrusion Detector')
    parser.add_argument('--duration', '-d', type=int, default=60, help='Detection duration (seconds)')
    
    args = parser.parse_args()
    
    # Setup logging
    logging.basicConfig(level=logging.INFO,
                       format='%(asctime)s - %(levelname)s - %(message)s')
    
    # Create directories
    config.create_directories()
    
    # Initialize detector
    detector = RealTimeDetector()
    
    try:
        # Start detection
        print("🚀 Bắt đầu real-time detection...")
        detector.start_detection()
        
        # Simulate packet processing (in real scenario, packets come from network monitor)
        import numpy as np
        
        for i in range(args.duration):
            # Simulate adding packets (this would come from network monitor)
            # detector.add_packet(packet)
            
            # Show stats every 10 seconds
            if i % 10 == 0:
                stats = detector.get_detection_stats()
                print(f"📊 Stats - Analyzed: {stats['total_analyzed']}, "
                      f"Anomalies: {stats['anomalies_detected']}, "
                      f"Intrusions: {stats['intrusions_detected']}")
            
            time.sleep(1)
        
        # Stop detection
        detector.stop_detection()
        
        # Show final stats
        final_stats = detector.get_detection_stats()
        print(f"\n📈 Final Statistics:")
        print(f"  Total analyzed: {final_stats['total_analyzed']}")
        print(f"  Anomalies detected: {final_stats['anomalies_detected']}")
        print(f"  Intrusions detected: {final_stats['intrusions_detected']}")
        print(f"  Detection rate: {final_stats['detection_rate']:.3f}")
        
    except KeyboardInterrupt:
        print(f"\n⏹️  Detection interrupted by user")
        detector.stop_detection()
    except Exception as e:
        print(f"💥 Error: {e}")


if __name__ == "__main__":
    main()
