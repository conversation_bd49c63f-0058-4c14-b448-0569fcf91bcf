apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: security-platform-ingress
  namespace: security-platform
  labels:
    app: security-platform
    component: ingress
  annotations:
    kubernetes.io/ingress.class: "gce"
    kubernetes.io/ingress.global-static-ip-name: "security-platform-ip"
    networking.gke.io/managed-certificates: "security-platform-ssl-cert"
    kubernetes.io/ingress.allow-http: "false"
    cloud.google.com/armor-config: '{"security-platform-policy": "security-platform-policy"}'
    cloud.google.com/backend-config: '{"default": "security-platform-backend-config"}'
    cloud.google.com/neg: '{"ingress": true}'
spec:
  rules:
  - host: security-platform.example.com
    http:
      paths:
      - path: /api/malware/*
        pathType: Prefix
        backend:
          service:
            name: malware-detection-service
            port:
              number: 5000
      - path: /api/ids/*
        pathType: Prefix
        backend:
          service:
            name: network-ids-service
            port:
              number: 5001
      - path: /api/dashboard/*
        pathType: Prefix
        backend:
          service:
            name: dashboard-service
            port:
              number: 8080
      - path: /
        pathType: Prefix
        backend:
          service:
            name: dashboard-service
            port:
              number: 8080
  - host: api.security-platform.example.com
    http:
      paths:
      - path: /malware/*
        pathType: Prefix
        backend:
          service:
            name: malware-detection-service
            port:
              number: 5000
      - path: /ids/*
        pathType: Prefix
        backend:
          service:
            name: network-ids-service
            port:
              number: 5001
  - host: dashboard.security-platform.example.com
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: dashboard-service
            port:
              number: 8080
---
apiVersion: networking.gke.io/v1
kind: ManagedCertificate
metadata:
  name: security-platform-ssl-cert
  namespace: security-platform
  labels:
    app: security-platform
    component: ssl
spec:
  domains:
    - security-platform.example.com
    - api.security-platform.example.com
    - dashboard.security-platform.example.com
---
apiVersion: cloud.google.com/v1
kind: BackendConfig
metadata:
  name: security-platform-backend-config
  namespace: security-platform
  labels:
    app: security-platform
    component: backend-config
spec:
  # Health check configuration
  healthCheck:
    checkIntervalSec: 10
    timeoutSec: 5
    healthyThreshold: 1
    unhealthyThreshold: 3
    type: HTTP
    requestPath: /health
    port: 8080
  
  # Session affinity
  sessionAffinity:
    affinityType: "CLIENT_IP"
    affinityCookieTtlSec: 3600
  
  # Connection draining
  connectionDraining:
    drainingTimeoutSec: 60
  
  # Timeout settings
  timeoutSec: 30
  
  # Custom request headers
  customRequestHeaders:
    headers:
    - "X-Client-Region:{client_region}"
    - "X-Client-City:{client_city}"
  
  # Security policy
  securityPolicy:
    name: "security-platform-policy"
  
  # CDN configuration
  cdn:
    enabled: true
    cachePolicy:
      includeHost: true
      includeProtocol: true
      includeQueryString: false
    negativeCaching: true
    negativeCachingPolicy:
    - code: 404
      ttl: 120
    - code: 500
      ttl: 60
---
apiVersion: v1
kind: Service
metadata:
  name: malware-detection-service-lb
  namespace: security-platform
  labels:
    app: malware-detection
    component: load-balancer
  annotations:
    cloud.google.com/backend-config: '{"default": "malware-detection-backend-config"}'
    cloud.google.com/neg: '{"ingress": true}'
spec:
  type: NodePort
  ports:
  - port: 5000
    targetPort: 5000
    protocol: TCP
    name: http
  selector:
    app: malware-detection
---
apiVersion: cloud.google.com/v1
kind: BackendConfig
metadata:
  name: malware-detection-backend-config
  namespace: security-platform
  labels:
    app: malware-detection
    component: backend-config
spec:
  healthCheck:
    checkIntervalSec: 15
    timeoutSec: 10
    healthyThreshold: 1
    unhealthyThreshold: 3
    type: HTTP
    requestPath: /api/health
    port: 5000
  
  timeoutSec: 60  # Longer timeout for file uploads
  
  # Rate limiting for malware detection
  securityPolicy:
    name: "malware-detection-policy"
---
apiVersion: v1
kind: Service
metadata:
  name: network-ids-service-lb
  namespace: security-platform
  labels:
    app: network-ids
    component: load-balancer
  annotations:
    cloud.google.com/backend-config: '{"default": "network-ids-backend-config"}'
    cloud.google.com/neg: '{"ingress": true}'
spec:
  type: NodePort
  ports:
  - port: 5001
    targetPort: 5001
    protocol: TCP
    name: http
  selector:
    app: network-ids
---
apiVersion: cloud.google.com/v1
kind: BackendConfig
metadata:
  name: network-ids-backend-config
  namespace: security-platform
  labels:
    app: network-ids
    component: backend-config
spec:
  healthCheck:
    checkIntervalSec: 10
    timeoutSec: 5
    healthyThreshold: 1
    unhealthyThreshold: 3
    type: HTTP
    requestPath: /api/status
    port: 5001
  
  timeoutSec: 30
  
  # Session affinity for network monitoring
  sessionAffinity:
    affinityType: "CLIENT_IP"
    affinityCookieTtlSec: 1800
---
apiVersion: v1
kind: Service
metadata:
  name: dashboard-service-lb
  namespace: security-platform
  labels:
    app: security-dashboard
    component: load-balancer
  annotations:
    cloud.google.com/backend-config: '{"default": "dashboard-backend-config"}'
    cloud.google.com/neg: '{"ingress": true}'
spec:
  type: NodePort
  ports:
  - port: 8080
    targetPort: 8080
    protocol: TCP
    name: http
  selector:
    app: security-dashboard
---
apiVersion: cloud.google.com/v1
kind: BackendConfig
metadata:
  name: dashboard-backend-config
  namespace: security-platform
  labels:
    app: security-dashboard
    component: backend-config
spec:
  healthCheck:
    checkIntervalSec: 10
    timeoutSec: 5
    healthyThreshold: 1
    unhealthyThreshold: 3
    type: HTTP
    requestPath: /health
    port: 8080
  
  timeoutSec: 30
  
  # CDN for static assets
  cdn:
    enabled: true
    cachePolicy:
      includeHost: true
      includeProtocol: true
      includeQueryString: true
    negativeCaching: true
    negativeCachingPolicy:
    - code: 404
      ttl: 300
  
  # Session affinity for dashboard
  sessionAffinity:
    affinityType: "GENERATED_COOKIE"
    affinityCookieTtlSec: 3600
---
# Global static IP address
apiVersion: v1
kind: ConfigMap
metadata:
  name: load-balancer-config
  namespace: security-platform
  labels:
    app: security-platform
    component: load-balancer
data:
  # Terraform will create the static IP and update this
  static-ip-name: "security-platform-ip"
  
  # Load balancer configuration
  lb-config.yaml: |
    global:
      static_ip: "security-platform-ip"
      ssl_certificates:
        - "security-platform-ssl-cert"
      security_policy: "security-platform-policy"
    
    backends:
      malware-detection:
        service: "malware-detection-service"
        port: 5000
        health_check_path: "/api/health"
        timeout: 60
        
      network-ids:
        service: "network-ids-service"
        port: 5001
        health_check_path: "/api/status"
        timeout: 30
        
      dashboard:
        service: "dashboard-service"
        port: 8080
        health_check_path: "/health"
        timeout: 30
        cdn_enabled: true
    
    routing_rules:
      - host: "security-platform.example.com"
        paths:
          - path: "/api/malware/*"
            backend: "malware-detection"
          - path: "/api/ids/*"
            backend: "network-ids"
          - path: "/*"
            backend: "dashboard"
      
      - host: "api.security-platform.example.com"
        paths:
          - path: "/malware/*"
            backend: "malware-detection"
          - path: "/ids/*"
            backend: "network-ids"
      
      - host: "dashboard.security-platform.example.com"
        paths:
          - path: "/*"
            backend: "dashboard"
