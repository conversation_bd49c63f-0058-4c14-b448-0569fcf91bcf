# IAM Policies and Security Configuration for Cloud Security Platform

# Custom IAM roles for fine-grained access control
resource "google_project_iam_custom_role" "security_platform_operator" {
  role_id     = "securityPlatformOperator"
  title       = "Security Platform Operator"
  description = "Custom role for Security Platform operations"
  
  permissions = [
    # GKE permissions
    "container.clusters.get",
    "container.clusters.list",
    "container.pods.get",
    "container.pods.list",
    "container.services.get",
    "container.services.list",
    
    # Cloud SQL permissions
    "cloudsql.instances.get",
    "cloudsql.instances.list",
    "cloudsql.databases.get",
    "cloudsql.databases.list",
    
    # Storage permissions
    "storage.buckets.get",
    "storage.buckets.list",
    "storage.objects.get",
    "storage.objects.list",
    "storage.objects.create",
    "storage.objects.update",
    
    # Monitoring permissions
    "monitoring.metricDescriptors.get",
    "monitoring.metricDescriptors.list",
    "monitoring.timeSeries.list",
    
    # Logging permissions
    "logging.entries.list",
    "logging.logEntries.list",
    
    # Secret Manager permissions
    "secretmanager.secrets.get",
    "secretmanager.versions.access"
  ]
}

resource "google_project_iam_custom_role" "security_platform_viewer" {
  role_id     = "securityPlatformViewer"
  title       = "Security Platform Viewer"
  description = "Read-only access to Security Platform resources"
  
  permissions = [
    # GKE read permissions
    "container.clusters.get",
    "container.clusters.list",
    "container.pods.get",
    "container.pods.list",
    "container.services.get",
    "container.services.list",
    
    # Storage read permissions
    "storage.buckets.get",
    "storage.buckets.list",
    "storage.objects.get",
    "storage.objects.list",
    
    # Monitoring read permissions
    "monitoring.metricDescriptors.get",
    "monitoring.metricDescriptors.list",
    "monitoring.timeSeries.list",
    
    # Logging read permissions
    "logging.entries.list",
    "logging.logEntries.list"
  ]
}

# Service Account for Security Platform applications
resource "google_service_account" "security_platform_app" {
  account_id   = "security-platform-app"
  display_name = "Security Platform Application Service Account"
  description  = "Service account for Security Platform applications"
}

# Workload Identity binding
resource "google_service_account_iam_binding" "workload_identity_binding" {
  service_account_id = google_service_account.security_platform_app.name
  role               = "roles/iam.workloadIdentityUser"
  
  members = [
    "serviceAccount:${var.project_id}.svc.id.goog[security-platform/security-platform-sa]"
  ]
}

# IAM bindings for application service account
resource "google_project_iam_member" "app_service_account_roles" {
  for_each = toset([
    "roles/cloudsql.client",
    "roles/storage.objectAdmin",
    "roles/secretmanager.secretAccessor",
    "roles/monitoring.metricWriter",
    "roles/logging.logWriter"
  ])
  
  project = var.project_id
  role    = each.value
  member  = "serviceAccount:${google_service_account.security_platform_app.email}"
}

# Security Analyst role for human users
resource "google_project_iam_custom_role" "security_analyst" {
  role_id     = "securityAnalyst"
  title       = "Security Analyst"
  description = "Role for security analysts to access platform data"
  
  permissions = [
    # Full access to security platform resources
    "container.clusters.get",
    "container.pods.get",
    "container.pods.list",
    "container.pods.getLogs",
    
    # Storage access for analysis
    "storage.objects.get",
    "storage.objects.list",
    
    # Monitoring and alerting
    "monitoring.alertPolicies.get",
    "monitoring.alertPolicies.list",
    "monitoring.incidents.get",
    "monitoring.incidents.list",
    
    # Logging for investigation
    "logging.entries.list",
    "logging.logEntries.list",
    "logging.views.get",
    "logging.views.list"
  ]
}

# Security Administrator role
resource "google_project_iam_custom_role" "security_admin" {
  role_id     = "securityAdmin"
  title       = "Security Administrator"
  description = "Administrative role for security platform management"
  
  permissions = [
    # Full GKE access
    "container.clusters.get",
    "container.clusters.update",
    "container.pods.get",
    "container.pods.list",
    "container.pods.delete",
    "container.services.get",
    "container.services.list",
    "container.services.update",
    
    # Storage management
    "storage.buckets.get",
    "storage.buckets.list",
    "storage.buckets.update",
    "storage.objects.get",
    "storage.objects.list",
    "storage.objects.create",
    "storage.objects.delete",
    
    # Secret management
    "secretmanager.secrets.get",
    "secretmanager.secrets.list",
    "secretmanager.secrets.update",
    "secretmanager.versions.access",
    
    # Monitoring management
    "monitoring.alertPolicies.get",
    "monitoring.alertPolicies.list",
    "monitoring.alertPolicies.create",
    "monitoring.alertPolicies.update",
    "monitoring.alertPolicies.delete",
    
    # IAM management (limited)
    "iam.serviceAccounts.get",
    "iam.serviceAccounts.list"
  ]
}

# Audit logging configuration
resource "google_project_iam_audit_config" "audit_config" {
  project = var.project_id
  service = "allServices"
  
  audit_log_config {
    log_type = "ADMIN_READ"
  }
  
  audit_log_config {
    log_type = "DATA_READ"
    exempted_members = [
      "serviceAccount:${google_service_account.security_platform_app.email}"
    ]
  }
  
  audit_log_config {
    log_type = "DATA_WRITE"
  }
}

# Organization policy constraints
resource "google_project_organization_policy" "disable_service_account_key_creation" {
  project    = var.project_id
  constraint = "iam.disableServiceAccountKeyCreation"
  
  boolean_policy {
    enforced = true
  }
}

resource "google_project_organization_policy" "require_os_login" {
  project    = var.project_id
  constraint = "compute.requireOsLogin"
  
  boolean_policy {
    enforced = true
  }
}

resource "google_project_organization_policy" "restrict_vm_external_ips" {
  project    = var.project_id
  constraint = "compute.vmExternalIpAccess"
  
  list_policy {
    deny {
      all = true
    }
  }
}

# Security Command Center notification
resource "google_scc_notification_config" "security_notification" {
  config_id    = "security-platform-notifications"
  organization = var.organization_id
  description  = "Security notifications for Security Platform"
  
  pubsub_topic = google_pubsub_topic.security_notifications.id
  
  streaming_config {
    filter = "state=\"ACTIVE\" AND category=\"MALWARE\""
  }
}

resource "google_pubsub_topic" "security_notifications" {
  name = "security-platform-notifications"
}

# Binary Authorization policy
resource "google_binary_authorization_policy" "policy" {
  admission_whitelist_patterns {
    name_pattern = "gcr.io/${var.project_id}/*"
  }
  
  default_admission_rule {
    evaluation_mode  = "REQUIRE_ATTESTATION"
    enforcement_mode = "ENFORCED_BLOCK_AND_AUDIT_LOG"
    
    require_attestations_by = [
      google_binary_authorization_attestor.attestor.name
    ]
  }
  
  cluster_admission_rules {
    cluster                = "${var.region}.${google_container_cluster.primary.name}"
    evaluation_mode        = "REQUIRE_ATTESTATION"
    enforcement_mode       = "ENFORCED_BLOCK_AND_AUDIT_LOG"
    
    require_attestations_by = [
      google_binary_authorization_attestor.attestor.name
    ]
  }
}

resource "google_binary_authorization_attestor" "attestor" {
  name = "security-platform-attestor"
  description = "Attestor for Security Platform images"
  
  attestation_authority_note {
    note_reference = google_container_analysis_note.note.name
    
    public_keys {
      ascii_armored_pgp_public_key = file("${path.module}/attestor-public-key.pgp")
    }
  }
}

resource "google_container_analysis_note" "note" {
  name = "security-platform-attestor-note"
  
  attestation_authority {
    hint {
      human_readable_name = "Security Platform Attestor"
    }
  }
}

# VPC Security Configuration
resource "google_compute_security_policy" "security_policy" {
  name        = "security-platform-policy"
  description = "Security policy for Security Platform"
  
  # Block known malicious IPs
  rule {
    action   = "deny(403)"
    priority = "1000"
    match {
      versioned_expr = "SRC_IPS_V1"
      config {
        src_ip_ranges = [
          "*********/24",  # Example malicious range
          "************/24"
        ]
      }
    }
    description = "Block known malicious IP ranges"
  }
  
  # Rate limiting
  rule {
    action   = "rate_based_ban"
    priority = "2000"
    match {
      versioned_expr = "SRC_IPS_V1"
      config {
        src_ip_ranges = ["*"]
      }
    }
    rate_limit_options {
      conform_action = "allow"
      exceed_action  = "deny(429)"
      enforce_on_key = "IP"
      
      rate_limit_threshold {
        count        = 100
        interval_sec = 60
      }
      
      ban_duration_sec = 600
    }
    description = "Rate limiting rule"
  }
  
  # Default allow rule
  rule {
    action   = "allow"
    priority = "**********"
    match {
      versioned_expr = "SRC_IPS_V1"
      config {
        src_ip_ranges = ["*"]
      }
    }
    description = "Default allow rule"
  }
}

# SSL Policy
resource "google_compute_ssl_policy" "ssl_policy" {
  name            = "security-platform-ssl-policy"
  profile         = "MODERN"
  min_tls_version = "TLS_1_2"
}

# Outputs
output "security_service_accounts" {
  description = "Security-related service accounts"
  value = {
    app_service_account = google_service_account.security_platform_app.email
  }
}

output "custom_roles" {
  description = "Custom IAM roles created"
  value = {
    operator = google_project_iam_custom_role.security_platform_operator.role_id
    viewer   = google_project_iam_custom_role.security_platform_viewer.role_id
    analyst  = google_project_iam_custom_role.security_analyst.role_id
    admin    = google_project_iam_custom_role.security_admin.role_id
  }
}

output "security_policies" {
  description = "Security policies created"
  value = {
    vpc_security_policy = google_compute_security_policy.security_policy.name
    ssl_policy         = google_compute_ssl_policy.ssl_policy.name
  }
}
