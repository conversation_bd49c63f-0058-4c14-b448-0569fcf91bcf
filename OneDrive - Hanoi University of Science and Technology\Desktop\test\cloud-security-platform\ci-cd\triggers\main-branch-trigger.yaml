# Cloud Build Trigger for Main Branch (Production Deployment)
# This trigger runs on pushes to the main branch and deploys to production

name: security-platform-main-trigger
description: "Production deployment trigger for Security Platform"

# GitHub configuration
github:
  owner: "your-github-username"  # Replace with actual GitHub username/org
  name: "cloud-security-platform"  # Replace with actual repository name
  push:
    branch: "^main$"

# Build configuration
filename: "ci-cd/cloudbuild.yaml"

# Substitutions for production environment
substitutions:
  _ENVIRONMENT: "production"
  _CLUSTER_NAME: "security-platform-cluster"
  _CLUSTER_LOCATION: "asia-southeast1"
  _NAMESPACE: "security-platform"

# Service account for the build
serviceAccount: "projects/security-platform-prod/serviceAccounts/<EMAIL>"

# Build approval required for production
approvalConfig:
  approvalRequired: true

# Include build logs in Cloud Logging
includeBuildLogs: INCLUDE_BUILD_LOGS_WITH_STATUS

# Tags for organization
tags:
  - "security-platform"
  - "production"
  - "main-branch"

# Filter to only trigger on specific file changes
ignoredFiles:
  - "docs/**"
  - "README.md"
  - "*.md"
  - ".gitignore"

includedFiles:
  - "src/**"
  - "docker/**"
  - "kubernetes/**"
  - "ci-cd/**"
  - "terraform/**"
