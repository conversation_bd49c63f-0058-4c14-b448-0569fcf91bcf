# Terraform Outputs for Cloud Security Platform

# Project Information
output "project_id" {
  description = "GCP Project ID"
  value       = var.project_id
}

output "region" {
  description = "GCP Region"
  value       = var.region
}

output "zone" {
  description = "GCP Zone"
  value       = var.zone
}

# Network Information
output "vpc_name" {
  description = "VPC network name"
  value       = google_compute_network.vpc.name
}

output "vpc_id" {
  description = "VPC network ID"
  value       = google_compute_network.vpc.id
}

output "subnet_name" {
  description = "Subnet name"
  value       = google_compute_subnetwork.subnet.name
}

output "subnet_cidr" {
  description = "Subnet CIDR"
  value       = google_compute_subnetwork.subnet.ip_cidr_range
}

# GKE Cluster Information
output "cluster_name" {
  description = "GKE cluster name"
  value       = google_container_cluster.primary.name
}

output "cluster_endpoint" {
  description = "GKE cluster endpoint"
  value       = google_container_cluster.primary.endpoint
  sensitive   = true
}

output "cluster_ca_certificate" {
  description = "GKE cluster CA certificate"
  value       = google_container_cluster.primary.master_auth.0.cluster_ca_certificate
  sensitive   = true
}

output "cluster_location" {
  description = "GKE cluster location"
  value       = google_container_cluster.primary.location
}

output "node_pool_name" {
  description = "GKE node pool name"
  value       = google_container_node_pool.primary_nodes.name
}

# Database Information
output "database_instance_name" {
  description = "Cloud SQL instance name"
  value       = google_sql_database_instance.main.name
}

output "database_connection_name" {
  description = "Cloud SQL instance connection name"
  value       = google_sql_database_instance.main.connection_name
}

output "database_private_ip" {
  description = "Cloud SQL instance private IP"
  value       = google_sql_database_instance.main.private_ip_address
}

output "database_name" {
  description = "Database name"
  value       = google_sql_database.database.name
}

output "database_user" {
  description = "Database user"
  value       = google_sql_user.users.name
}

# Storage Information
output "models_bucket_name" {
  description = "Models storage bucket name"
  value       = google_storage_bucket.models_bucket.name
}

output "models_bucket_url" {
  description = "Models storage bucket URL"
  value       = google_storage_bucket.models_bucket.url
}

output "logs_bucket_name" {
  description = "Logs storage bucket name"
  value       = google_storage_bucket.logs_bucket.name
}

output "logs_bucket_url" {
  description = "Logs storage bucket URL"
  value       = google_storage_bucket.logs_bucket.url
}

output "uploads_bucket_name" {
  description = "Uploads storage bucket name"
  value       = google_storage_bucket.uploads_bucket.name
}

output "uploads_bucket_url" {
  description = "Uploads storage bucket URL"
  value       = google_storage_bucket.uploads_bucket.url
}

output "backups_bucket_name" {
  description = "Backups storage bucket name"
  value       = google_storage_bucket.backups_bucket.name
}

output "backups_bucket_url" {
  description = "Backups storage bucket URL"
  value       = google_storage_bucket.backups_bucket.url
}

# Service Account Information
output "gke_service_account_email" {
  description = "GKE service account email"
  value       = google_service_account.gke_service_account.email
}

output "storage_service_account_email" {
  description = "Storage service account email"
  value       = google_service_account.storage_service_account.email
}

output "cloudsql_service_account_email" {
  description = "Cloud SQL service account email"
  value       = google_service_account.cloudsql_service_account.email
}

# Secret Manager Information
output "db_password_secret_name" {
  description = "Database password secret name"
  value       = google_secret_manager_secret.db_password.secret_id
}

output "app_secrets_secret_name" {
  description = "Application secrets secret name"
  value       = google_secret_manager_secret.app_secrets.secret_id
}

# Kubernetes Configuration
output "kubernetes_config_command" {
  description = "Command to configure kubectl"
  value       = "gcloud container clusters get-credentials ${google_container_cluster.primary.name} --region ${google_container_cluster.primary.location} --project ${var.project_id}"
}

# Application URLs (will be available after deployment)
output "application_urls" {
  description = "Application URLs (available after deployment)"
  value = {
    api_gateway    = "http://[EXTERNAL_IP]"
    malware_api    = "http://[EXTERNAL_IP]/api/malware/"
    network_ids_api = "http://[EXTERNAL_IP]/api/ids/"
    dashboard      = "http://[EXTERNAL_IP]/"
  }
}

# Container Registry Information
output "container_registry_url" {
  description = "Container Registry URL"
  value       = "gcr.io/${var.project_id}"
}

# Monitoring Information
output "monitoring_namespace" {
  description = "Kubernetes namespace for monitoring"
  value       = "security-platform"
}

# Environment Configuration
output "environment_config" {
  description = "Environment configuration summary"
  value = {
    environment           = var.environment
    gke_nodes_per_zone   = var.gke_num_nodes
    gke_machine_type     = var.gke_machine_type
    database_tier        = var.db_tier
    monitoring_enabled   = var.enable_monitoring
    network_policy_enabled = var.enable_network_policy
  }
}

# Resource Names for Reference
output "resource_names" {
  description = "Important resource names for reference"
  value = {
    vpc_network           = google_compute_network.vpc.name
    subnet               = google_compute_subnetwork.subnet.name
    gke_cluster          = google_container_cluster.primary.name
    database_instance    = google_sql_database_instance.main.name
    models_bucket        = google_storage_bucket.models_bucket.name
    logs_bucket          = google_storage_bucket.logs_bucket.name
    uploads_bucket       = google_storage_bucket.uploads_bucket.name
    backups_bucket       = google_storage_bucket.backups_bucket.name
  }
}

# Connection Information
output "connection_info" {
  description = "Connection information for services"
  value = {
    database_connection_string = "postgresql://${google_sql_user.users.name}:[PASSWORD]@${google_sql_database_instance.main.private_ip_address}:5432/${google_sql_database.database.name}"
    cloud_sql_proxy_command   = "cloud_sql_proxy -instances=${google_sql_database_instance.main.connection_name}=tcp:5432"
    kubectl_config_command    = "gcloud container clusters get-credentials ${google_container_cluster.primary.name} --region ${google_container_cluster.primary.location} --project ${var.project_id}"
  }
  sensitive = true
}

# Deployment Commands
output "deployment_commands" {
  description = "Commands for deployment"
  value = {
    configure_kubectl = "gcloud container clusters get-credentials ${google_container_cluster.primary.name} --region ${google_container_cluster.primary.location} --project ${var.project_id}"
    build_images     = "./scripts/build-images.sh --project-id ${var.project_id}"
    deploy_apps      = "./scripts/deploy.sh ${var.environment}"
    check_status     = "kubectl get pods -n security-platform"
  }
}

# Cost Information
output "estimated_monthly_cost" {
  description = "Estimated monthly cost breakdown (USD)"
  value = {
    gke_cluster      = "~$150-300 (depending on node usage)"
    cloud_sql        = "~$50-100 (depending on tier and usage)"
    cloud_storage    = "~$10-50 (depending on data volume)"
    load_balancer    = "~$20"
    network_egress   = "~$10-50 (depending on traffic)"
    monitoring       = "~$10-30"
    total_estimated  = "~$250-550 per month"
    note            = "Costs vary based on actual usage, region, and configuration"
  }
}

# Security Information
output "security_notes" {
  description = "Important security information"
  value = {
    private_cluster     = "GKE cluster uses private nodes"
    database_private    = "Cloud SQL instance is private (no public IP)"
    workload_identity   = "Workload Identity is enabled for secure pod-to-GCP authentication"
    network_policies    = var.enable_network_policy ? "Network policies are enabled" : "Network policies are disabled"
    secrets_management  = "Secrets are managed via Google Secret Manager"
    ssl_tls            = "SSL/TLS encryption is enabled for all services"
  }
}

# Next Steps
output "next_steps" {
  description = "Next steps after Terraform deployment"
  value = [
    "1. Configure kubectl: ${local.kubectl_command}",
    "2. Build and push Docker images: ./scripts/build-images.sh",
    "3. Deploy applications: ./scripts/deploy.sh ${var.environment}",
    "4. Check deployment status: kubectl get pods -n security-platform",
    "5. Get external IP: kubectl get service api-gateway-service -n security-platform",
    "6. Configure DNS (if using custom domain)",
    "7. Set up monitoring dashboards",
    "8. Configure alerting rules",
    "9. Test all services and endpoints",
    "10. Set up backup and disaster recovery procedures"
  ]
}

# Local values for outputs
locals {
  kubectl_command = "gcloud container clusters get-credentials ${google_container_cluster.primary.name} --region ${google_container_cluster.primary.location} --project ${var.project_id}"
}
