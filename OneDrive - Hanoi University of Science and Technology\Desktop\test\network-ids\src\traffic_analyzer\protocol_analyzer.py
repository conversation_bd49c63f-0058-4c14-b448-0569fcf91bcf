"""
Protocol Analyzer Module
Module phân tích các protocols mạng
"""

import sys
import re
import json
from pathlib import Path
from collections import defaultdict, Counter
from datetime import datetime
import logging

# Add parent directory to path
sys.path.append(str(Path(__file__).parent.parent.parent))

try:
    from scapy.all import *
    from scapy.layers.inet import IP, TCP, UDP, ICMP
    from scapy.layers.http import HTTP, HTTPRequest, HTTPResponse
    from scapy.layers.dns import DNS, DNSQR, DNSRR
    from scapy.layers.tls import TLS
except ImportError as e:
    print(f"❌ Import error: {e}")
    print("💡 Cài đặt: pip install scapy")
    sys.exit(1)

import config


class ProtocolAnalyzer:
    """Class phân tích các network protocols"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.suspicious_patterns = self._load_suspicious_patterns()
        
    def _load_suspicious_patterns(self):
        """Load suspicious patterns cho detection"""
        return {
            'sql_injection': [
                r"union\s+select", r"drop\s+table", r"insert\s+into",
                r"delete\s+from", r"update\s+set", r"exec\s*\(",
                r"script\s*>", r"javascript:", r"vbscript:"
            ],
            'xss': [
                r"<script", r"javascript:", r"onload\s*=",
                r"onerror\s*=", r"onclick\s*=", r"alert\s*\("
            ],
            'command_injection': [
                r";\s*cat\s+", r";\s*ls\s+", r";\s*pwd",
                r";\s*whoami", r";\s*id\s*;", r"\|\s*nc\s+"
            ],
            'directory_traversal': [
                r"\.\.\/", r"\.\.\\", r"%2e%2e%2f", r"%2e%2e%5c"
            ]
        }
    
    def analyze_http_traffic(self, packets):
        """
        Phân tích HTTP traffic
        
        Args:
            packets: List of packets
            
        Returns:
            dict: HTTP analysis results
        """
        http_stats = {
            'total_requests': 0,
            'total_responses': 0,
            'methods': defaultdict(int),
            'status_codes': defaultdict(int),
            'user_agents': defaultdict(int),
            'hosts': defaultdict(int),
            'suspicious_requests': [],
            'large_requests': [],
            'error_responses': []
        }
        
        for packet in packets:
            if packet.haslayer(HTTP):
                self._analyze_http_packet(packet, http_stats)
        
        return http_stats
    
    def _analyze_http_packet(self, packet, stats):
        """Phân tích một HTTP packet"""
        try:
            if packet.haslayer(HTTPRequest):
                stats['total_requests'] += 1
                request = packet[HTTPRequest]
                
                # Method analysis
                method = request.Method.decode() if request.Method else 'UNKNOWN'
                stats['methods'][method] += 1
                
                # Host analysis
                host = request.Host.decode() if request.Host else 'UNKNOWN'
                stats['hosts'][host] += 1
                
                # User-Agent analysis
                if hasattr(request, 'User_Agent') and request.User_Agent:
                    ua = request.User_Agent.decode()
                    stats['user_agents'][ua] += 1
                
                # URL analysis
                path = request.Path.decode() if request.Path else ''
                
                # Check for suspicious patterns
                suspicious = self._check_suspicious_http(request, path)
                if suspicious:
                    stats['suspicious_requests'].append({
                        'timestamp': packet.time if hasattr(packet, 'time') else datetime.now(),
                        'src_ip': packet[IP].src if packet.haslayer(IP) else 'unknown',
                        'method': method,
                        'host': host,
                        'path': path,
                        'reason': suspicious
                    })
                
                # Check for large requests
                if len(packet) > 8192:  # 8KB
                    stats['large_requests'].append({
                        'timestamp': packet.time if hasattr(packet, 'time') else datetime.now(),
                        'src_ip': packet[IP].src if packet.haslayer(IP) else 'unknown',
                        'size': len(packet),
                        'method': method,
                        'path': path
                    })
            
            elif packet.haslayer(HTTPResponse):
                stats['total_responses'] += 1
                response = packet[HTTPResponse]
                
                # Status code analysis
                if hasattr(response, 'Status_Code'):
                    status_code = response.Status_Code.decode()
                    stats['status_codes'][status_code] += 1
                    
                    # Check for error responses
                    if status_code.startswith('4') or status_code.startswith('5'):
                        stats['error_responses'].append({
                            'timestamp': packet.time if hasattr(packet, 'time') else datetime.now(),
                            'src_ip': packet[IP].src if packet.haslayer(IP) else 'unknown',
                            'status_code': status_code,
                            'size': len(packet)
                        })
        
        except Exception as e:
            self.logger.error(f"Lỗi khi phân tích HTTP packet: {e}")
    
    def _check_suspicious_http(self, request, path):
        """Kiểm tra HTTP request có suspicious không"""
        full_request = f"{path} {request.fields}"
        
        for attack_type, patterns in self.suspicious_patterns.items():
            for pattern in patterns:
                if re.search(pattern, full_request, re.IGNORECASE):
                    return attack_type
        
        return None
    
    def analyze_dns_traffic(self, packets):
        """
        Phân tích DNS traffic
        
        Args:
            packets: List of packets
            
        Returns:
            dict: DNS analysis results
        """
        dns_stats = {
            'total_queries': 0,
            'total_responses': 0,
            'query_types': defaultdict(int),
            'response_codes': defaultdict(int),
            'domains': defaultdict(int),
            'suspicious_domains': [],
            'large_responses': [],
            'failed_queries': []
        }
        
        for packet in packets:
            if packet.haslayer(DNS):
                self._analyze_dns_packet(packet, dns_stats)
        
        return dns_stats
    
    def _analyze_dns_packet(self, packet, stats):
        """Phân tích một DNS packet"""
        try:
            dns = packet[DNS]
            
            if dns.qr == 0:  # Query
                stats['total_queries'] += 1
                
                if dns.qd:  # Question section
                    query = dns.qd
                    domain = query.qname.decode().rstrip('.')
                    qtype = query.qtype
                    
                    stats['domains'][domain] += 1
                    stats['query_types'][qtype] += 1
                    
                    # Check for suspicious domains
                    if self._is_suspicious_domain(domain):
                        stats['suspicious_domains'].append({
                            'timestamp': packet.time if hasattr(packet, 'time') else datetime.now(),
                            'src_ip': packet[IP].src if packet.haslayer(IP) else 'unknown',
                            'domain': domain,
                            'query_type': qtype
                        })
            
            else:  # Response
                stats['total_responses'] += 1
                stats['response_codes'][dns.rcode] += 1
                
                # Check for failed queries
                if dns.rcode != 0:
                    stats['failed_queries'].append({
                        'timestamp': packet.time if hasattr(packet, 'time') else datetime.now(),
                        'src_ip': packet[IP].src if packet.haslayer(IP) else 'unknown',
                        'rcode': dns.rcode,
                        'domain': dns.qd.qname.decode().rstrip('.') if dns.qd else 'unknown'
                    })
                
                # Check for large responses
                if len(packet) > 512:  # Standard DNS packet size
                    stats['large_responses'].append({
                        'timestamp': packet.time if hasattr(packet, 'time') else datetime.now(),
                        'src_ip': packet[IP].src if packet.haslayer(IP) else 'unknown',
                        'size': len(packet),
                        'answer_count': dns.ancount
                    })
        
        except Exception as e:
            self.logger.error(f"Lỗi khi phân tích DNS packet: {e}")
    
    def _is_suspicious_domain(self, domain):
        """Kiểm tra domain có suspicious không"""
        suspicious_indicators = [
            # DGA domains
            len(domain) > 20,
            domain.count('.') > 3,
            # Random character patterns
            re.search(r'[0-9]{3,}', domain),
            # Suspicious TLDs
            any(domain.endswith(tld) for tld in ['.tk', '.ml', '.ga', '.cf']),
            # Homograph attacks
            any(char in domain for char in ['xn--', 'рус', 'عرب'])
        ]
        
        return any(suspicious_indicators)
    
    def analyze_tcp_connections(self, packets):
        """
        Phân tích TCP connections
        
        Args:
            packets: List of packets
            
        Returns:
            dict: TCP analysis results
        """
        tcp_stats = {
            'total_packets': 0,
            'connections': defaultdict(dict),
            'port_distribution': defaultdict(int),
            'flag_distribution': defaultdict(int),
            'suspicious_connections': [],
            'port_scans': [],
            'connection_attempts': defaultdict(int)
        }
        
        for packet in packets:
            if packet.haslayer(TCP):
                self._analyze_tcp_packet(packet, tcp_stats)
        
        # Detect port scanning
        self._detect_port_scanning(tcp_stats)
        
        return tcp_stats
    
    def _analyze_tcp_packet(self, packet, stats):
        """Phân tích một TCP packet"""
        try:
            tcp = packet[TCP]
            ip = packet[IP]
            
            stats['total_packets'] += 1
            
            # Port analysis
            stats['port_distribution'][tcp.dport] += 1
            
            # Flag analysis
            flag_names = []
            if tcp.flags & 0x01: flag_names.append('FIN')
            if tcp.flags & 0x02: flag_names.append('SYN')
            if tcp.flags & 0x04: flag_names.append('RST')
            if tcp.flags & 0x08: flag_names.append('PSH')
            if tcp.flags & 0x10: flag_names.append('ACK')
            if tcp.flags & 0x20: flag_names.append('URG')
            
            flag_combo = '+'.join(flag_names) if flag_names else 'NONE'
            stats['flag_distribution'][flag_combo] += 1
            
            # Connection tracking
            conn_key = f"{ip.src}:{tcp.sport}-{ip.dst}:{tcp.dport}"
            
            if tcp.flags & 0x02:  # SYN flag
                stats['connection_attempts'][ip.src] += 1
                
                # Check for suspicious connection patterns
                if stats['connection_attempts'][ip.src] > 100:  # Threshold
                    stats['suspicious_connections'].append({
                        'timestamp': packet.time if hasattr(packet, 'time') else datetime.now(),
                        'src_ip': ip.src,
                        'dst_ip': ip.dst,
                        'dst_port': tcp.dport,
                        'reason': 'high_connection_rate'
                    })
        
        except Exception as e:
            self.logger.error(f"Lỗi khi phân tích TCP packet: {e}")
    
    def _detect_port_scanning(self, stats):
        """Detect port scanning activities"""
        # Simple port scan detection based on connection attempts
        for src_ip, attempts in stats['connection_attempts'].items():
            if attempts > 50:  # Threshold for port scan
                unique_ports = set()
                for conn_key in stats['connections']:
                    if conn_key.startswith(src_ip):
                        port = conn_key.split('-')[1].split(':')[1]
                        unique_ports.add(port)
                
                if len(unique_ports) > 10:  # Scanning multiple ports
                    stats['port_scans'].append({
                        'src_ip': src_ip,
                        'unique_ports': len(unique_ports),
                        'total_attempts': attempts,
                        'scan_type': 'horizontal' if len(unique_ports) > attempts/2 else 'vertical'
                    })
    
    def analyze_icmp_traffic(self, packets):
        """
        Phân tích ICMP traffic
        
        Args:
            packets: List of packets
            
        Returns:
            dict: ICMP analysis results
        """
        icmp_stats = {
            'total_packets': 0,
            'type_distribution': defaultdict(int),
            'code_distribution': defaultdict(int),
            'suspicious_icmp': [],
            'large_icmp': []
        }
        
        for packet in packets:
            if packet.haslayer(ICMP):
                self._analyze_icmp_packet(packet, icmp_stats)
        
        return icmp_stats
    
    def _analyze_icmp_packet(self, packet, stats):
        """Phân tích một ICMP packet"""
        try:
            icmp = packet[ICMP]
            ip = packet[IP]
            
            stats['total_packets'] += 1
            stats['type_distribution'][icmp.type] += 1
            stats['code_distribution'][icmp.code] += 1
            
            # Check for suspicious ICMP
            if icmp.type in [13, 14, 15, 16, 17, 18]:  # Information request types
                stats['suspicious_icmp'].append({
                    'timestamp': packet.time if hasattr(packet, 'time') else datetime.now(),
                    'src_ip': ip.src,
                    'dst_ip': ip.dst,
                    'type': icmp.type,
                    'code': icmp.code,
                    'reason': 'information_gathering'
                })
            
            # Check for large ICMP packets (potential covert channel)
            if len(packet) > 84:  # Standard ICMP echo is 84 bytes
                stats['large_icmp'].append({
                    'timestamp': packet.time if hasattr(packet, 'time') else datetime.now(),
                    'src_ip': ip.src,
                    'dst_ip': ip.dst,
                    'size': len(packet),
                    'type': icmp.type
                })
        
        except Exception as e:
            self.logger.error(f"Lỗi khi phân tích ICMP packet: {e}")
    
    def generate_protocol_report(self, packets):
        """
        Tạo báo cáo tổng hợp về protocols
        
        Args:
            packets: List of packets
            
        Returns:
            dict: Protocol analysis report
        """
        report = {
            'timestamp': datetime.now().isoformat(),
            'total_packets': len(packets),
            'http_analysis': self.analyze_http_traffic(packets),
            'dns_analysis': self.analyze_dns_traffic(packets),
            'tcp_analysis': self.analyze_tcp_connections(packets),
            'icmp_analysis': self.analyze_icmp_traffic(packets)
        }
        
        # Calculate protocol distribution
        protocol_dist = defaultdict(int)
        for packet in packets:
            if packet.haslayer(IP):
                proto = packet[IP].proto
                if proto == 1:
                    protocol_dist['ICMP'] += 1
                elif proto == 6:
                    protocol_dist['TCP'] += 1
                elif proto == 17:
                    protocol_dist['UDP'] += 1
                else:
                    protocol_dist[f'PROTO_{proto}'] += 1
        
        report['protocol_distribution'] = dict(protocol_dist)
        
        return report


if __name__ == "__main__":
    # Test protocol analyzer
    logging.basicConfig(level=logging.INFO)
    
    analyzer = ProtocolAnalyzer()
    
    print("🔍 Protocol Analyzer ready!")
    print("💡 Sử dụng với captured packets để phân tích protocols")
