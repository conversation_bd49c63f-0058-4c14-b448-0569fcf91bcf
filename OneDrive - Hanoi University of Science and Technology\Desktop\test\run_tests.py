"""
Test Runner
Script để chạy tất cả unit tests cho AI Malware Detection System
"""

import unittest
import sys
import os
from pathlib import Path
import time
import argparse

# Add src to path
sys.path.append(str(Path(__file__).parent / 'src'))

# Import test modules
from tests.test_pe_analyzer import TestPEAnalyzer, TestPEAnalyzerEdgeCases, TestPEAnalyzerPerformance
from tests.test_api import TestAPIEndpoints, TestAPIIntegration, TestAPIPerformance


def run_all_tests(verbosity=2, pattern=None):
    """
    Chạy tất cả unit tests
    
    Args:
        verbosity (int): <PERSON><PERSON><PERSON> độ chi tiết output (0-2)
        pattern (str): Pattern để filter tests
        
    Returns:
        bool: True nếu tất cả tests pass
    """
    print("=" * 70)
    print("AI MALWARE DETECTION SYSTEM - TEST SUITE")
    print("=" * 70)
    
    # Create test suite
    suite = unittest.TestSuite()
    
    # PE Analyzer Tests
    print("\n📁 Adding PE Analyzer Tests...")
    suite.addTest(unittest.makeSuite(TestPEAnalyzer))
    suite.addTest(unittest.makeSuite(TestPEAnalyzerEdgeCases))
    suite.addTest(unittest.makeSuite(TestPEAnalyzerPerformance))
    
    # API Tests
    print("🌐 Adding API Tests...")
    suite.addTest(unittest.makeSuite(TestAPIEndpoints))
    suite.addTest(unittest.makeSuite(TestAPIIntegration))
    suite.addTest(unittest.makeSuite(TestAPIPerformance))
    
    # Filter tests by pattern if provided
    if pattern:
        filtered_suite = unittest.TestSuite()
        for test_group in suite:
            for test_case in test_group:
                if pattern.lower() in str(test_case).lower():
                    filtered_suite.addTest(test_case)
        suite = filtered_suite
        print(f"🔍 Filtered tests by pattern: '{pattern}'")
    
    print(f"\n🚀 Running {suite.countTestCases()} tests...\n")
    
    # Run tests
    start_time = time.time()
    runner = unittest.TextTestRunner(verbosity=verbosity, stream=sys.stdout)
    result = runner.run(suite)
    end_time = time.time()
    
    # Print detailed summary
    print("\n" + "=" * 70)
    print("TEST RESULTS SUMMARY")
    print("=" * 70)
    
    total_time = end_time - start_time
    print(f"⏱️  Total execution time: {total_time:.2f} seconds")
    print(f"📊 Tests run: {result.testsRun}")
    print(f"✅ Successes: {result.testsRun - len(result.failures) - len(result.errors)}")
    print(f"❌ Failures: {len(result.failures)}")
    print(f"💥 Errors: {len(result.errors)}")
    print(f"⏭️  Skipped: {len(getattr(result, 'skipped', []))}")
    
    # Success rate
    if result.testsRun > 0:
        success_rate = (result.testsRun - len(result.failures) - len(result.errors)) / result.testsRun * 100
        print(f"📈 Success rate: {success_rate:.1f}%")
    
    # Detailed failure/error information
    if result.failures:
        print(f"\n❌ FAILURES ({len(result.failures)}):")
        print("-" * 50)
        for i, (test, traceback) in enumerate(result.failures, 1):
            print(f"{i}. {test}")
            if verbosity > 1:
                print(f"   {traceback.split('AssertionError:')[-1].strip()}")
    
    if result.errors:
        print(f"\n💥 ERRORS ({len(result.errors)}):")
        print("-" * 50)
        for i, (test, traceback) in enumerate(result.errors, 1):
            print(f"{i}. {test}")
            if verbosity > 1:
                # Extract the main error message
                error_lines = traceback.strip().split('\n')
                for line in error_lines:
                    if 'Error:' in line or 'Exception:' in line:
                        print(f"   {line.strip()}")
                        break
    
    # Recommendations
    print(f"\n💡 RECOMMENDATIONS:")
    if result.failures or result.errors:
        print("   - Review failed tests and fix underlying issues")
        print("   - Run individual test files for more detailed debugging")
        print("   - Check system requirements and dependencies")
    else:
        print("   - All tests passed! System is ready for deployment")
        print("   - Consider adding more edge case tests")
        print("   - Monitor performance metrics in production")
    
    return result.wasSuccessful()


def run_specific_test(test_name, verbosity=2):
    """
    Chạy một test cụ thể
    
    Args:
        test_name (str): Tên test class hoặc method
        verbosity (int): Mức độ chi tiết output
        
    Returns:
        bool: True nếu test pass
    """
    print(f"🎯 Running specific test: {test_name}")
    
    # Try to find and run the specific test
    loader = unittest.TestLoader()
    suite = unittest.TestSuite()
    
    # Map of available test classes
    test_classes = {
        'pe_analyzer': [TestPEAnalyzer, TestPEAnalyzerEdgeCases, TestPEAnalyzerPerformance],
        'api': [TestAPIEndpoints, TestAPIIntegration, TestAPIPerformance],
        'TestPEAnalyzer': [TestPEAnalyzer],
        'TestAPIEndpoints': [TestAPIEndpoints],
        'TestAPIIntegration': [TestAPIIntegration],
        'TestAPIPerformance': [TestAPIPerformance],
        'TestPEAnalyzerEdgeCases': [TestPEAnalyzerEdgeCases],
        'TestPEAnalyzerPerformance': [TestPEAnalyzerPerformance]
    }
    
    if test_name in test_classes:
        for test_class in test_classes[test_name]:
            suite.addTest(unittest.makeSuite(test_class))
    else:
        print(f"❌ Test '{test_name}' not found!")
        print("Available tests:")
        for name in test_classes.keys():
            print(f"   - {name}")
        return False
    
    # Run the specific test
    runner = unittest.TextTestRunner(verbosity=verbosity)
    result = runner.run(suite)
    
    return result.wasSuccessful()


def check_system_requirements():
    """Kiểm tra system requirements cho tests"""
    print("🔍 Checking system requirements...")
    
    requirements = {
        'Python version': sys.version_info >= (3, 7),
        'Windows OS': os.name == 'nt',
        'Sample PE file': os.path.exists("C:\\Windows\\System32\\notepad.exe")
    }
    
    all_ok = True
    for requirement, status in requirements.items():
        status_icon = "✅" if status else "❌"
        print(f"   {status_icon} {requirement}")
        if not status:
            all_ok = False
    
    if not all_ok:
        print("\n⚠️  Some requirements are not met. Some tests may be skipped.")
    
    return all_ok


def main():
    """Main function"""
    parser = argparse.ArgumentParser(description='Run AI Malware Detection System Tests')
    parser.add_argument('--test', type=str, help='Run specific test (e.g., pe_analyzer, api)')
    parser.add_argument('--pattern', type=str, help='Filter tests by pattern')
    parser.add_argument('--verbosity', type=int, default=2, choices=[0, 1, 2],
                       help='Test output verbosity (0=quiet, 1=normal, 2=verbose)')
    parser.add_argument('--no-check', action='store_true',
                       help='Skip system requirements check')
    
    args = parser.parse_args()
    
    # Check system requirements
    if not args.no_check:
        check_system_requirements()
        print()
    
    try:
        if args.test:
            # Run specific test
            success = run_specific_test(args.test, args.verbosity)
        else:
            # Run all tests
            success = run_all_tests(args.verbosity, args.pattern)
        
        # Exit with appropriate code
        exit_code = 0 if success else 1
        
        if success:
            print(f"\n🎉 All tests completed successfully!")
        else:
            print(f"\n💔 Some tests failed. Please review the output above.")
        
        sys.exit(exit_code)
        
    except KeyboardInterrupt:
        print(f"\n⏹️  Tests interrupted by user")
        sys.exit(1)
    except Exception as e:
        print(f"\n💥 Unexpected error running tests: {e}")
        sys.exit(1)


if __name__ == '__main__':
    main()
