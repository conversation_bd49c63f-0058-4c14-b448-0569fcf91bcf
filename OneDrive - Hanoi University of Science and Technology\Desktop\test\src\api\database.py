"""
Database Models
SQLite database models cho AI Malware Detection System
"""

import os
import sys
from pathlib import Path
from datetime import datetime
from sqlalchemy import create_engine, Column, Integer, String, Float, DateTime, Boolean, Text
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker
import logging

# Add src to path
sys.path.append(str(Path(__file__).parent.parent))

import config

# Database setup
Base = declarative_base()
engine = None
SessionLocal = None


class AnalysisResult(Base):
    """Model lưu kết quả phân tích file"""
    __tablename__ = 'analysis_results'
    
    id = Column(Integer, primary_key=True, index=True)
    
    # File information
    filename = Column(String(255), nullable=False)
    file_path = Column(String(500))
    file_size = Column(Integer)
    md5_hash = Column(String(32), index=True)
    sha1_hash = Column(String(40))
    sha256_hash = Column(String(64), index=True)
    
    # Analysis results
    prediction = Column(String(20), nullable=False)  # 'malware' or 'benign'
    confidence = Column(Float)
    malware_probability = Column(Float)
    benign_probability = Column(Float)
    
    # Model information
    model_type = Column(String(50))
    model_version = Column(String(20))
    
    # Features (JSON string)
    features_json = Column(Text)
    
    # Timing
    analysis_time = Column(Float)  # seconds
    created_at = Column(DateTime, default=datetime.utcnow)
    
    # Client information
    client_ip = Column(String(45))
    user_agent = Column(String(500))
    
    def __repr__(self):
        return f"<AnalysisResult(id={self.id}, filename='{self.filename}', prediction='{self.prediction}')>"


class ModelTraining(Base):
    """Model lưu thông tin training"""
    __tablename__ = 'model_training'
    
    id = Column(Integer, primary_key=True, index=True)
    
    # Model information
    model_type = Column(String(50), nullable=False)
    model_version = Column(String(20))
    model_path = Column(String(500))
    scaler_path = Column(String(500))
    
    # Training data
    dataset_path = Column(String(500))
    total_samples = Column(Integer)
    malware_samples = Column(Integer)
    benign_samples = Column(Integer)
    
    # Training results
    train_accuracy = Column(Float)
    test_accuracy = Column(Float)
    precision_score = Column(Float)
    recall_score = Column(Float)
    f1_score = Column(Float)
    roc_auc_score = Column(Float)
    
    # Cross validation
    cv_mean_accuracy = Column(Float)
    cv_std_accuracy = Column(Float)
    
    # Training parameters (JSON string)
    training_params = Column(Text)
    
    # Timing
    training_time = Column(Float)  # seconds
    created_at = Column(DateTime, default=datetime.utcnow)
    
    # Status
    is_active = Column(Boolean, default=False)  # Current active model
    
    def __repr__(self):
        return f"<ModelTraining(id={self.id}, model_type='{self.model_type}', test_accuracy={self.test_accuracy})>"


class SystemLog(Base):
    """Model lưu system logs"""
    __tablename__ = 'system_logs'
    
    id = Column(Integer, primary_key=True, index=True)
    
    # Log information
    level = Column(String(20), nullable=False)  # DEBUG, INFO, WARNING, ERROR, CRITICAL
    message = Column(Text, nullable=False)
    module = Column(String(100))
    function = Column(String(100))
    line_number = Column(Integer)
    
    # Context
    client_ip = Column(String(45))
    user_agent = Column(String(500))
    request_id = Column(String(50))
    
    # Timing
    created_at = Column(DateTime, default=datetime.utcnow)
    
    def __repr__(self):
        return f"<SystemLog(id={self.id}, level='{self.level}', message='{self.message[:50]}...')>"


def init_database():
    """Initialize database connection and create tables"""
    global engine, SessionLocal
    
    try:
        # Create database directory if not exists
        db_path = Path(config.DATABASE_URL.replace('sqlite:///', ''))
        db_path.parent.mkdir(parents=True, exist_ok=True)
        
        # Create engine
        engine = create_engine(
            config.DATABASE_URL,
            connect_args={"check_same_thread": False}  # For SQLite
        )
        
        # Create session factory
        SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
        
        # Create tables
        Base.metadata.create_all(bind=engine)
        
        logging.info("Database initialized successfully")
        return True
        
    except Exception as e:
        logging.error(f"Failed to initialize database: {e}")
        return False


def get_db():
    """Get database session"""
    if SessionLocal is None:
        init_database()
    
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()


class DatabaseManager:
    """Database manager class"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        if not engine:
            init_database()
    
    def save_analysis_result(self, result_data):
        """
        Lưu kết quả phân tích vào database
        
        Args:
            result_data (dict): Dữ liệu kết quả phân tích
            
        Returns:
            int: ID của record đã lưu
        """
        try:
            db = SessionLocal()
            
            # Create analysis result record
            analysis = AnalysisResult(
                filename=result_data.get('filename'),
                file_path=result_data.get('file_path'),
                file_size=result_data.get('file_size'),
                md5_hash=result_data.get('md5'),
                sha1_hash=result_data.get('sha1'),
                sha256_hash=result_data.get('sha256'),
                prediction=result_data.get('prediction'),
                confidence=result_data.get('confidence'),
                malware_probability=result_data.get('malware_probability'),
                benign_probability=result_data.get('benign_probability'),
                model_type=result_data.get('model_type'),
                model_version=result_data.get('model_version', '1.0'),
                features_json=result_data.get('features_json'),
                analysis_time=result_data.get('analysis_time'),
                client_ip=result_data.get('client_ip'),
                user_agent=result_data.get('user_agent')
            )
            
            db.add(analysis)
            db.commit()
            db.refresh(analysis)
            
            self.logger.info(f"Saved analysis result with ID: {analysis.id}")
            return analysis.id
            
        except Exception as e:
            self.logger.error(f"Failed to save analysis result: {e}")
            db.rollback()
            return None
        finally:
            db.close()
    
    def save_training_result(self, training_data):
        """
        Lưu kết quả training vào database
        
        Args:
            training_data (dict): Dữ liệu training
            
        Returns:
            int: ID của record đã lưu
        """
        try:
            db = SessionLocal()
            
            # Deactivate previous models
            db.query(ModelTraining).filter(
                ModelTraining.model_type == training_data.get('model_type')
            ).update({'is_active': False})
            
            # Create training record
            training = ModelTraining(
                model_type=training_data.get('model_type'),
                model_version=training_data.get('model_version', '1.0'),
                model_path=training_data.get('model_path'),
                scaler_path=training_data.get('scaler_path'),
                dataset_path=training_data.get('dataset_path'),
                total_samples=training_data.get('total_samples'),
                malware_samples=training_data.get('malware_samples'),
                benign_samples=training_data.get('benign_samples'),
                train_accuracy=training_data.get('train_accuracy'),
                test_accuracy=training_data.get('test_accuracy'),
                precision_score=training_data.get('precision_score'),
                recall_score=training_data.get('recall_score'),
                f1_score=training_data.get('f1_score'),
                roc_auc_score=training_data.get('roc_auc_score'),
                cv_mean_accuracy=training_data.get('cv_mean_accuracy'),
                cv_std_accuracy=training_data.get('cv_std_accuracy'),
                training_params=training_data.get('training_params'),
                training_time=training_data.get('training_time'),
                is_active=True
            )
            
            db.add(training)
            db.commit()
            db.refresh(training)
            
            self.logger.info(f"Saved training result with ID: {training.id}")
            return training.id
            
        except Exception as e:
            self.logger.error(f"Failed to save training result: {e}")
            db.rollback()
            return None
        finally:
            db.close()
    
    def get_analysis_history(self, limit=100):
        """
        Lấy lịch sử phân tích
        
        Args:
            limit (int): Số lượng records tối đa
            
        Returns:
            list: Danh sách kết quả phân tích
        """
        try:
            db = SessionLocal()
            
            results = db.query(AnalysisResult)\
                       .order_by(AnalysisResult.created_at.desc())\
                       .limit(limit)\
                       .all()
            
            return results
            
        except Exception as e:
            self.logger.error(f"Failed to get analysis history: {e}")
            return []
        finally:
            db.close()
    
    def get_statistics(self):
        """
        Lấy thống kê hệ thống
        
        Returns:
            dict: Thống kê
        """
        try:
            db = SessionLocal()
            
            # Analysis statistics
            total_analyses = db.query(AnalysisResult).count()
            malware_detected = db.query(AnalysisResult)\
                                .filter(AnalysisResult.prediction == 'malware')\
                                .count()
            benign_detected = db.query(AnalysisResult)\
                               .filter(AnalysisResult.prediction == 'benign')\
                               .count()
            
            # Model statistics
            active_model = db.query(ModelTraining)\
                            .filter(ModelTraining.is_active == True)\
                            .first()
            
            stats = {
                'total_analyses': total_analyses,
                'malware_detected': malware_detected,
                'benign_detected': benign_detected,
                'detection_rate': (malware_detected / total_analyses * 100) if total_analyses > 0 else 0,
                'active_model': {
                    'type': active_model.model_type if active_model else None,
                    'accuracy': active_model.test_accuracy if active_model else None,
                    'created_at': active_model.created_at if active_model else None
                }
            }
            
            return stats
            
        except Exception as e:
            self.logger.error(f"Failed to get statistics: {e}")
            return {}
        finally:
            db.close()
    
    def log_system_event(self, level, message, module=None, function=None, 
                        line_number=None, client_ip=None, user_agent=None, request_id=None):
        """
        Lưu system log
        
        Args:
            level (str): Log level
            message (str): Log message
            module (str): Module name
            function (str): Function name
            line_number (int): Line number
            client_ip (str): Client IP
            user_agent (str): User agent
            request_id (str): Request ID
        """
        try:
            db = SessionLocal()
            
            log_entry = SystemLog(
                level=level,
                message=message,
                module=module,
                function=function,
                line_number=line_number,
                client_ip=client_ip,
                user_agent=user_agent,
                request_id=request_id
            )
            
            db.add(log_entry)
            db.commit()
            
        except Exception as e:
            # Don't log errors in logging to avoid recursion
            print(f"Failed to save system log: {e}")
        finally:
            db.close()


# Global database manager instance
db_manager = DatabaseManager()


if __name__ == "__main__":
    # Test database
    init_database()
    
    # Test saving analysis result
    test_result = {
        'filename': 'test.exe',
        'file_size': 1024,
        'md5': 'test_md5_hash',
        'sha256': 'test_sha256_hash',
        'prediction': 'benign',
        'confidence': 0.95,
        'model_type': 'random_forest',
        'analysis_time': 1.5
    }
    
    result_id = db_manager.save_analysis_result(test_result)
    print(f"Saved test result with ID: {result_id}")
    
    # Test getting statistics
    stats = db_manager.get_statistics()
    print(f"Statistics: {stats}")
    
    print("Database test completed!")
