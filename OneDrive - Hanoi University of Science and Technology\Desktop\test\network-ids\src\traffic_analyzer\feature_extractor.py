"""
Feature Extractor Module
Module trích xuất đặc trưng từ network packets và flows
"""

import sys
import time
import math
import numpy as np
import pandas as pd
from pathlib import Path
from collections import defaultdict, Counter
from datetime import datetime, timedelta
import logging

# Add parent directory to path
sys.path.append(str(Path(__file__).parent.parent.parent))

try:
    from scapy.all import *
    from scapy.layers.inet import IP, TCP, UDP, ICMP
    from scapy.layers.http import HTTP
    from scapy.layers.dns import DNS
except ImportError as e:
    print(f"❌ Import error: {e}")
    print("💡 Cài đặt: pip install scapy")
    sys.exit(1)

import config


class NetworkFlow:
    """Class đại diện cho một network flow"""
    
    def __init__(self, src_ip, dst_ip, src_port, dst_port, protocol):
        self.src_ip = src_ip
        self.dst_ip = dst_ip
        self.src_port = src_port
        self.dst_port = dst_port
        self.protocol = protocol
        
        # Flow statistics
        self.start_time = None
        self.end_time = None
        self.packets = []
        self.packet_sizes = []
        self.inter_arrival_times = []
        self.tcp_flags = []
        self.total_bytes = 0
        self.packet_count = 0
        
        # Direction statistics
        self.forward_packets = 0
        self.backward_packets = 0
        self.forward_bytes = 0
        self.backward_bytes = 0
    
    def add_packet(self, packet, timestamp):
        """Thêm packet vào flow"""
        if self.start_time is None:
            self.start_time = timestamp
        
        self.end_time = timestamp
        self.packets.append(packet)
        packet_size = len(packet)
        self.packet_sizes.append(packet_size)
        self.total_bytes += packet_size
        self.packet_count += 1
        
        # Calculate inter-arrival time
        if len(self.packets) > 1:
            prev_time = self.packets[-2].time if hasattr(self.packets[-2], 'time') else timestamp
            inter_arrival = timestamp - prev_time
            self.inter_arrival_times.append(inter_arrival)
        
        # TCP flags
        if packet.haslayer(TCP):
            flags = packet[TCP].flags
            self.tcp_flags.append(flags)
        
        # Direction analysis
        if packet.haslayer(IP):
            if packet[IP].src == self.src_ip:
                self.forward_packets += 1
                self.forward_bytes += packet_size
            else:
                self.backward_packets += 1
                self.backward_bytes += packet_size
    
    def get_duration(self):
        """Lấy duration của flow"""
        if self.start_time and self.end_time:
            return self.end_time - self.start_time
        return 0
    
    def get_flow_id(self):
        """Lấy unique ID của flow"""
        return f"{self.src_ip}:{self.src_port}-{self.dst_ip}:{self.dst_port}-{self.protocol}"


class FeatureExtractor:
    """Class trích xuất đặc trưng từ network traffic"""
    
    def __init__(self):
        self.flows = {}
        self.flow_timeout = config.TRAFFIC_CONFIG['flow_timeout']
        self.logger = logging.getLogger(__name__)
        
    def extract_packet_features(self, packet):
        """
        Trích xuất đặc trưng từ một packet đơn lẻ
        
        Args:
            packet: Scapy packet object
            
        Returns:
            dict: Dictionary chứa features
        """
        features = {}
        
        try:
            # Basic packet features
            features['packet_size'] = len(packet)
            features['timestamp'] = packet.time if hasattr(packet, 'time') else time.time()
            
            # Layer 2 features
            if packet.haslayer(Ether):
                features['eth_src'] = packet[Ether].src
                features['eth_dst'] = packet[Ether].dst
                features['eth_type'] = packet[Ether].type
            
            # Layer 3 features (IP)
            if packet.haslayer(IP):
                ip_layer = packet[IP]
                features['src_ip'] = ip_layer.src
                features['dst_ip'] = ip_layer.dst
                features['ip_version'] = ip_layer.version
                features['ip_ihl'] = ip_layer.ihl
                features['ip_tos'] = ip_layer.tos
                features['ip_len'] = ip_layer.len
                features['ip_id'] = ip_layer.id
                features['ip_flags'] = ip_layer.flags
                features['ip_frag'] = ip_layer.frag
                features['ip_ttl'] = ip_layer.ttl
                features['ip_proto'] = ip_layer.proto
                features['ip_chksum'] = ip_layer.chksum
                
                # Protocol type
                if ip_layer.proto == 1:
                    features['protocol'] = 'ICMP'
                elif ip_layer.proto == 6:
                    features['protocol'] = 'TCP'
                elif ip_layer.proto == 17:
                    features['protocol'] = 'UDP'
                else:
                    features['protocol'] = f'PROTO_{ip_layer.proto}'
            
            # Layer 4 features (TCP/UDP)
            if packet.haslayer(TCP):
                tcp_layer = packet[TCP]
                features['src_port'] = tcp_layer.sport
                features['dst_port'] = tcp_layer.dport
                features['tcp_seq'] = tcp_layer.seq
                features['tcp_ack'] = tcp_layer.ack
                features['tcp_dataofs'] = tcp_layer.dataofs
                features['tcp_reserved'] = tcp_layer.reserved
                features['tcp_flags'] = tcp_layer.flags
                features['tcp_window'] = tcp_layer.window
                features['tcp_chksum'] = tcp_layer.chksum
                features['tcp_urgptr'] = tcp_layer.urgptr
                
                # TCP flags breakdown
                features['tcp_flag_fin'] = 1 if tcp_layer.flags & 0x01 else 0
                features['tcp_flag_syn'] = 1 if tcp_layer.flags & 0x02 else 0
                features['tcp_flag_rst'] = 1 if tcp_layer.flags & 0x04 else 0
                features['tcp_flag_psh'] = 1 if tcp_layer.flags & 0x08 else 0
                features['tcp_flag_ack'] = 1 if tcp_layer.flags & 0x10 else 0
                features['tcp_flag_urg'] = 1 if tcp_layer.flags & 0x20 else 0
                
            elif packet.haslayer(UDP):
                udp_layer = packet[UDP]
                features['src_port'] = udp_layer.sport
                features['dst_port'] = udp_layer.dport
                features['udp_len'] = udp_layer.len
                features['udp_chksum'] = udp_layer.chksum
            
            elif packet.haslayer(ICMP):
                icmp_layer = packet[ICMP]
                features['icmp_type'] = icmp_layer.type
                features['icmp_code'] = icmp_layer.code
                features['icmp_chksum'] = icmp_layer.chksum
                features['icmp_id'] = icmp_layer.id if hasattr(icmp_layer, 'id') else 0
                features['icmp_seq'] = icmp_layer.seq if hasattr(icmp_layer, 'seq') else 0
            
            # Application layer features
            if packet.haslayer(HTTP):
                features['has_http'] = 1
                # HTTP method, status code, etc. có thể thêm vào đây
            else:
                features['has_http'] = 0
            
            if packet.haslayer(DNS):
                features['has_dns'] = 1
                dns_layer = packet[DNS]
                features['dns_id'] = dns_layer.id
                features['dns_qr'] = dns_layer.qr
                features['dns_opcode'] = dns_layer.opcode
                features['dns_rcode'] = dns_layer.rcode
            else:
                features['has_dns'] = 0
            
            # Payload features
            if packet.haslayer(Raw):
                payload = packet[Raw].load
                features['payload_size'] = len(payload)
                features['payload_entropy'] = self._calculate_entropy(payload)
            else:
                features['payload_size'] = 0
                features['payload_entropy'] = 0
            
        except Exception as e:
            self.logger.error(f"Lỗi khi trích xuất packet features: {e}")
        
        return features
    
    def extract_flow_features(self, packets, window_size=60):
        """
        Trích xuất đặc trưng từ flow của packets
        
        Args:
            packets: List of packets
            window_size: Time window in seconds
            
        Returns:
            list: List of flow feature dictionaries
        """
        flows = self._create_flows(packets)
        flow_features = []
        
        for flow_id, flow in flows.items():
            features = self._extract_single_flow_features(flow)
            flow_features.append(features)
        
        return flow_features
    
    def _create_flows(self, packets):
        """Tạo flows từ danh sách packets"""
        flows = {}
        
        for packet in packets:
            if not packet.haslayer(IP):
                continue
            
            # Extract flow identifiers
            src_ip = packet[IP].src
            dst_ip = packet[IP].dst
            protocol = packet[IP].proto
            
            src_port = 0
            dst_port = 0
            
            if packet.haslayer(TCP):
                src_port = packet[TCP].sport
                dst_port = packet[TCP].dport
            elif packet.haslayer(UDP):
                src_port = packet[UDP].sport
                dst_port = packet[UDP].dport
            
            # Create flow ID (bidirectional)
            flow_id1 = f"{src_ip}:{src_port}-{dst_ip}:{dst_port}-{protocol}"
            flow_id2 = f"{dst_ip}:{dst_port}-{src_ip}:{src_port}-{protocol}"
            
            # Check if flow exists
            flow_id = flow_id1
            if flow_id2 in flows:
                flow_id = flow_id2
            
            if flow_id not in flows:
                flows[flow_id] = NetworkFlow(src_ip, dst_ip, src_port, dst_port, protocol)
            
            # Add packet to flow
            timestamp = packet.time if hasattr(packet, 'time') else time.time()
            flows[flow_id].add_packet(packet, timestamp)
        
        return flows
    
    def _extract_single_flow_features(self, flow):
        """Trích xuất features từ một flow"""
        features = {}
        
        # Basic flow information
        features['src_ip'] = flow.src_ip
        features['dst_ip'] = flow.dst_ip
        features['src_port'] = flow.src_port
        features['dst_port'] = flow.dst_port
        features['protocol'] = flow.protocol
        
        # Flow statistics
        features['duration'] = flow.get_duration()
        features['total_packets'] = flow.packet_count
        features['total_bytes'] = flow.total_bytes
        features['forward_packets'] = flow.forward_packets
        features['backward_packets'] = flow.backward_packets
        features['forward_bytes'] = flow.forward_bytes
        features['backward_bytes'] = flow.backward_bytes
        
        # Packet size statistics
        if flow.packet_sizes:
            features['min_packet_size'] = min(flow.packet_sizes)
            features['max_packet_size'] = max(flow.packet_sizes)
            features['mean_packet_size'] = np.mean(flow.packet_sizes)
            features['std_packet_size'] = np.std(flow.packet_sizes)
            features['median_packet_size'] = np.median(flow.packet_sizes)
        else:
            features.update({
                'min_packet_size': 0, 'max_packet_size': 0,
                'mean_packet_size': 0, 'std_packet_size': 0,
                'median_packet_size': 0
            })
        
        # Inter-arrival time statistics
        if flow.inter_arrival_times:
            features['min_iat'] = min(flow.inter_arrival_times)
            features['max_iat'] = max(flow.inter_arrival_times)
            features['mean_iat'] = np.mean(flow.inter_arrival_times)
            features['std_iat'] = np.std(flow.inter_arrival_times)
        else:
            features.update({
                'min_iat': 0, 'max_iat': 0,
                'mean_iat': 0, 'std_iat': 0
            })
        
        # Rate features
        if features['duration'] > 0:
            features['packets_per_second'] = features['total_packets'] / features['duration']
            features['bytes_per_second'] = features['total_bytes'] / features['duration']
        else:
            features['packets_per_second'] = 0
            features['bytes_per_second'] = 0
        
        # TCP specific features
        if flow.tcp_flags:
            flag_counts = Counter(flow.tcp_flags)
            features['tcp_flag_variety'] = len(flag_counts)
            features['syn_count'] = sum(1 for flags in flow.tcp_flags if flags & 0x02)
            features['fin_count'] = sum(1 for flags in flow.tcp_flags if flags & 0x01)
            features['rst_count'] = sum(1 for flags in flow.tcp_flags if flags & 0x04)
        else:
            features.update({
                'tcp_flag_variety': 0, 'syn_count': 0,
                'fin_count': 0, 'rst_count': 0
            })
        
        return features
    
    def _calculate_entropy(self, data):
        """Tính entropy của dữ liệu"""
        if not data:
            return 0
        
        # Count byte frequencies
        byte_counts = Counter(data)
        data_len = len(data)
        
        # Calculate entropy
        entropy = 0
        for count in byte_counts.values():
            probability = count / data_len
            if probability > 0:
                entropy -= probability * math.log2(probability)
        
        return entropy
    
    def extract_statistical_features(self, packets, time_window=60):
        """
        Trích xuất statistical features từ packets trong time window
        
        Args:
            packets: List of packets
            time_window: Time window in seconds
            
        Returns:
            dict: Statistical features
        """
        if not packets:
            return {}
        
        features = {}
        
        # Time-based grouping
        packet_times = [p.time if hasattr(p, 'time') else time.time() for p in packets]
        packet_sizes = [len(p) for p in packets]
        
        # Basic statistics
        features['packet_count'] = len(packets)
        features['total_bytes'] = sum(packet_sizes)
        features['time_span'] = max(packet_times) - min(packet_times) if len(packet_times) > 1 else 0
        
        # Size statistics
        features['min_size'] = min(packet_sizes) if packet_sizes else 0
        features['max_size'] = max(packet_sizes) if packet_sizes else 0
        features['mean_size'] = np.mean(packet_sizes) if packet_sizes else 0
        features['std_size'] = np.std(packet_sizes) if packet_sizes else 0
        
        # Rate statistics
        if features['time_span'] > 0:
            features['packet_rate'] = features['packet_count'] / features['time_span']
            features['byte_rate'] = features['total_bytes'] / features['time_span']
        else:
            features['packet_rate'] = 0
            features['byte_rate'] = 0
        
        # Protocol distribution
        protocol_counts = defaultdict(int)
        for packet in packets:
            if packet.haslayer(IP):
                proto = packet[IP].proto
                protocol_counts[proto] += 1
        
        total_packets = len(packets)
        features['tcp_ratio'] = protocol_counts[6] / total_packets if total_packets > 0 else 0
        features['udp_ratio'] = protocol_counts[17] / total_packets if total_packets > 0 else 0
        features['icmp_ratio'] = protocol_counts[1] / total_packets if total_packets > 0 else 0
        
        return features


if __name__ == "__main__":
    # Test feature extraction
    logging.basicConfig(level=logging.INFO)
    
    extractor = FeatureExtractor()
    
    # Test với sample packets (cần có PCAP file để test thực tế)
    print("🧪 Feature Extractor ready for testing!")
    print("💡 Sử dụng với packet capture để test thực tế")
