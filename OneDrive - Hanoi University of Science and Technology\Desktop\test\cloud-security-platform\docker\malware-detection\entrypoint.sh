#!/bin/bash
set -e

echo "🚀 Starting AI Malware Detection Service..."

# Wait for database if DATABASE_URL is provided
if [ ! -z "$DATABASE_URL" ]; then
    echo "⏳ Waiting for database connection..."
    python -c "
import time
import sys
import os
from sqlalchemy import create_engine
from sqlalchemy.exc import OperationalError

database_url = os.environ.get('DATABASE_URL')
if database_url:
    engine = create_engine(database_url)
    for i in range(30):
        try:
            engine.execute('SELECT 1')
            print('✅ Database connection successful')
            break
        except OperationalError:
            print(f'⏳ Waiting for database... ({i+1}/30)')
            time.sleep(2)
    else:
        print('❌ Database connection failed')
        sys.exit(1)
"
fi

# Initialize database if needed
echo "🔧 Initializing database..."
python -c "
try:
    from src.database.database import init_db
    init_db()
    print('✅ Database initialized')
except Exception as e:
    print(f'⚠️  Database initialization: {e}')
"

# Check if models exist, if not create sample data and train
echo "🧠 Checking ML models..."
python -c "
import os
from pathlib import Path

model_dir = Path('data/models')
if not model_dir.exists() or not list(model_dir.glob('*.joblib')):
    print('⚠️  No models found, creating sample data and training...')
    try:
        from src.models.train import main as train_main
        import sys
        sys.argv = ['train.py', '--create-sample']
        train_main()
        print('✅ Models trained successfully')
    except Exception as e:
        print(f'❌ Model training failed: {e}')
else:
    print('✅ Models found')
"

# Set default values for environment variables
export FLASK_HOST=${FLASK_HOST:-0.0.0.0}
export FLASK_PORT=${FLASK_PORT:-5000}
export FLASK_DEBUG=${FLASK_DEBUG:-false}

echo "🌐 Starting Flask application on ${FLASK_HOST}:${FLASK_PORT}"
echo "🔧 Debug mode: ${FLASK_DEBUG}"

# Execute the main command
exec "$@"
