# Hướng dẫn Triển khai Cloud Security Platform

Tài liệu này cung cấp hướng dẫn chi tiết về cách triển khai Cloud Security Platform trên Google Cloud Platform (GCP).

## M<PERSON><PERSON> lục

1. [<PERSON><PERSON><PERSON> cầu hệ thống](#yêu-cầu-hệ-thống)
2. [Thi<PERSON><PERSON> lập môi trường](#thiết-lập-môi-trường)
3. [Triển khai cơ sở hạ tầng](#triển-khai-cơ-sở-hạ-tầng)
4. [Triển khai ứng dụng](#triển-khai-ứng-dụng)
5. [Cấu hình bảo mật](#cấu-hình-bảo-mật)
6. [Giám sát và cảnh báo](#giám-sát-và-cảnh-báo)
7. [<PERSON><PERSON><PERSON><PERSON> ph<PERSON><PERSON> sự cố](#khắc-phục-sự-cố)
8. [<PERSON><PERSON><PERSON> trì và cập nhật](#bảo-trì-và-cập-nhật)

## <PERSON><PERSON><PERSON> cầ<PERSON> hệ thống

### Google Cloud Platform

- Tài khoản GCP với quyền quản trị viên
- Dự án GCP đã được tạo và kích hoạt billing
- Các API đã được kích hoạt:
  - Kubernetes Engine API
  - Container Registry API
  - Cloud SQL API
  - Cloud Storage API
  - Cloud Monitoring API
  - Cloud Logging API
  - Cloud Build API
  - Secret Manager API
  - IAM API

### Công cụ phát triển

- **Docker**: 20.10+
- **kubectl**: 1.24+
- **gcloud CLI**: 400.0+
- **Terraform**: 1.5+
- **Helm**: 3.10+

## Thiết lập môi trường

### 1. Cài đặt công cụ

```bash
# Cài đặt gcloud CLI
curl https://sdk.cloud.google.com | bash
gcloud init

# Cài đặt kubectl
gcloud components install kubectl

# Cài đặt Terraform
# Tải từ: https://www.terraform.io/downloads.html

# Cài đặt Helm
curl https://raw.githubusercontent.com/helm/helm/main/scripts/get-helm-3 | bash
```

### 2. Xác thực và cấu hình

```bash
# Đăng nhập vào GCP
gcloud auth login

# Cấu hình dự án
gcloud config set project YOUR_PROJECT_ID

# Kích hoạt xác thực cho Docker
gcloud auth configure-docker
```

### 3. Thiết lập dự án GCP

Chạy script thiết lập tự động:

```bash
# Clone repository
git clone <repository-url>
cd cloud-security-platform

# Chạy script thiết lập
./scripts/setup-gcp.sh
```

Hoặc thiết lập thủ công:

```bash
# Tạo dự án (nếu chưa có)
gcloud projects create security-platform-prod

# Liên kết billing account
gcloud billing projects link security-platform-prod --billing-account=YOUR_BILLING_ACCOUNT_ID

# Kích hoạt các API cần thiết
gcloud services enable container.googleapis.com \
    compute.googleapis.com \
    sqladmin.googleapis.com \
    storage.googleapis.com \
    monitoring.googleapis.com \
    logging.googleapis.com \
    cloudbuild.googleapis.com \
    secretmanager.googleapis.com \
    iam.googleapis.com
```

## Triển khai cơ sở hạ tầng

### 1. Cấu hình Terraform

```bash
# Di chuyển đến thư mục Terraform
cd terraform

# Khởi tạo Terraform
terraform init

# Kiểm tra kế hoạch triển khai
terraform plan -var="project_id=YOUR_PROJECT_ID"

# Áp dụng cấu hình
terraform apply -var="project_id=YOUR_PROJECT_ID"
```

### 2. Cấu hình kubectl

```bash
# Lấy thông tin xác thực cho GKE cluster
gcloud container clusters get-credentials security-platform-cluster \
    --region asia-southeast1 \
    --project YOUR_PROJECT_ID
```

### 3. Tạo Secret Manager secrets

```bash
# Tạo secret cho database password
echo -n "your-secure-password" | gcloud secrets create security-platform-db-password --data-file=-

# Tạo secret cho application
cat > app-secrets.json << EOF
{
  "malware_secret_key": "$(openssl rand -base64 32)",
  "ids_secret_key": "$(openssl rand -base64 32)",
  "dashboard_secret_key": "$(openssl rand -base64 32)",
  "api_key": "$(openssl rand -base64 32)",
  "jwt_secret": "$(openssl rand -base64 32)"
}
EOF

gcloud secrets create security-platform-app-secrets --data-file=app-secrets.json
rm app-secrets.json
```

## Triển khai ứng dụng

### 1. Build Docker images

```bash
# Build và push Docker images
./scripts/build-images.sh --project-id YOUR_PROJECT_ID
```

### 2. Triển khai ứng dụng lên Kubernetes

```bash
# Triển khai ứng dụng
./scripts/deploy.sh production
```

### 3. Kiểm tra trạng thái triển khai

```bash
# Kiểm tra pods
kubectl get pods -n security-platform

# Kiểm tra services
kubectl get services -n security-platform

# Kiểm tra ingress
kubectl get ingress -n security-platform
```

### 4. Truy cập ứng dụng

```bash
# Lấy External IP
kubectl get service api-gateway-service -n security-platform

# Truy cập ứng dụng tại:
# - Dashboard: http://<EXTERNAL_IP>/
# - Malware Detection API: http://<EXTERNAL_IP>/api/malware/
# - Network IDS API: http://<EXTERNAL_IP>/api/ids/
```

## Cấu hình bảo mật

### 1. Cấu hình SSL/TLS

```bash
# Tạo certificate
kubectl apply -f kubernetes/ingress/ssl-certificate.yaml

# Kiểm tra trạng thái certificate
kubectl get managedcertificate -n security-platform
```

### 2. Cấu hình IAM

```bash
# Tạo service account cho ứng dụng
gcloud iam service-accounts create security-platform-app \
    --display-name="Security Platform Application"

# Gán roles
gcloud projects add-iam-policy-binding YOUR_PROJECT_ID \
    --member="serviceAccount:security-platform-app@YOUR_PROJECT_ID.iam.gserviceaccount.com" \
    --role="roles/cloudsql.client"

gcloud projects add-iam-policy-binding YOUR_PROJECT_ID \
    --member="serviceAccount:security-platform-app@YOUR_PROJECT_ID.iam.gserviceaccount.com" \
    --role="roles/storage.objectAdmin"
```

### 3. Cấu hình Network Security

```bash
# Áp dụng network policies
kubectl apply -f kubernetes/security/network-policies.yaml

# Cấu hình Cloud Armor
gcloud compute security-policies create security-platform-policy \
    --description="Security policy for Security Platform"

# Thêm rule chặn các IP độc hại
gcloud compute security-policies rules create 1000 \
    --security-policy=security-platform-policy \
    --description="Block known malicious IPs" \
    --src-ip-ranges="*********/24,************/24" \
    --action=deny-403
```

## Giám sát và cảnh báo

### 1. Triển khai Prometheus và Grafana

```bash
# Triển khai Prometheus
kubectl apply -f monitoring/prometheus/prometheus-config.yaml

# Triển khai Grafana
kubectl apply -f monitoring/grafana/grafana-deployment.yaml

# Kiểm tra trạng thái
kubectl get pods -n security-platform -l component=monitoring
```

### 2. Cấu hình cảnh báo

```bash
# Áp dụng alert rules
kubectl apply -f monitoring/alerting/alert-rules.yaml

# Cấu hình notification channels
kubectl apply -f monitoring/alerting/notification-channels.yaml
```

### 3. Truy cập Grafana Dashboard

```bash
# Port forwarding cho Grafana
kubectl port-forward svc/grafana-service 3000:3000 -n security-platform

# Truy cập Grafana tại: http://localhost:3000
# Đăng nhập với:
# - Username: admin
# - Password: <từ monitoring-secrets>
```

## Khắc phục sự cố

### Kiểm tra logs

```bash
# Logs của Malware Detection
kubectl logs -f deployment/malware-detection -n security-platform

# Logs của Network IDS
kubectl logs -f deployment/network-ids -n security-platform

# Logs của Dashboard
kubectl logs -f deployment/security-dashboard -n security-platform
```

### Kiểm tra trạng thái pods

```bash
# Kiểm tra trạng thái chi tiết của pods
kubectl describe pods -n security-platform

# Kiểm tra events
kubectl get events -n security-platform
```

### Khởi động lại services

```bash
# Khởi động lại Malware Detection
kubectl rollout restart deployment/malware-detection -n security-platform

# Khởi động lại Network IDS
kubectl rollout restart deployment/network-ids -n security-platform

# Khởi động lại Dashboard
kubectl rollout restart deployment/security-dashboard -n security-platform
```

## Bảo trì và cập nhật

### 1. Cập nhật ứng dụng

```bash
# Cập nhật Docker images
./scripts/build-images.sh --project-id YOUR_PROJECT_ID --tag v1.1.0

# Cập nhật deployments
kubectl set image deployment/malware-detection malware-detection=gcr.io/YOUR_PROJECT_ID/malware-detection:v1.1.0 -n security-platform
kubectl set image deployment/network-ids network-ids=gcr.io/YOUR_PROJECT_ID/network-ids:v1.1.0 -n security-platform
kubectl set image deployment/security-dashboard security-dashboard=gcr.io/YOUR_PROJECT_ID/security-dashboard:v1.1.0 -n security-platform
```

### 2. Backup dữ liệu

```bash
# Backup Cloud SQL
gcloud sql export sql security-platform-db gs://YOUR_PROJECT_ID-backups/db-backup-$(date +%Y%m%d).sql \
    --database=security_platform

# Backup models
gsutil -m cp -r gs://YOUR_PROJECT_ID-models/* gs://YOUR_PROJECT_ID-backups/models-backup-$(date +%Y%m%d)/
```

### 3. Cập nhật cơ sở hạ tầng

```bash
# Cập nhật cấu hình Terraform
cd terraform
terraform plan -var="project_id=YOUR_PROJECT_ID"
terraform apply -var="project_id=YOUR_PROJECT_ID"
```

### 4. Cập nhật GKE cluster

```bash
# Cập nhật GKE cluster
gcloud container clusters upgrade security-platform-cluster \
    --region asia-southeast1 \
    --project YOUR_PROJECT_ID
```

## Tài liệu tham khảo

- [Google Kubernetes Engine Documentation](https://cloud.google.com/kubernetes-engine/docs)
- [Terraform Google Provider Documentation](https://registry.terraform.io/providers/hashicorp/google/latest/docs)
- [Cloud SQL Documentation](https://cloud.google.com/sql/docs)
- [Cloud Storage Documentation](https://cloud.google.com/storage/docs)
- [Cloud Monitoring Documentation](https://cloud.google.com/monitoring/docs)
- [Cloud Logging Documentation](https://cloud.google.com/logging/docs)
- [Cloud Build Documentation](https://cloud.google.com/build/docs)
- [Secret Manager Documentation](https://cloud.google.com/secret-manager/docs)
