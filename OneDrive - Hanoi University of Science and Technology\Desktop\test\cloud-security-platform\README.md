# ☁️ Cloud Security Platform

Nền tảng bảo mật đám mây tích hợp AI Malware Detection và Network IDS trên Google Cloud Platform.

![GCP](https://img.shields.io/badge/Google%20Cloud-4285F4?style=for-the-badge&logo=google-cloud&logoColor=white)
![Kubernetes](https://img.shields.io/badge/Kubernetes-326CE5?style=for-the-badge&logo=kubernetes&logoColor=white)
![Docker](https://img.shields.io/badge/Docker-2496ED?style=for-the-badge&logo=docker&logoColor=white)
![Terraform](https://img.shields.io/badge/Terraform-623CE4?style=for-the-badge&logo=terraform&logoColor=white)

## 🏗️ Kiến trúc hệ thống

```
┌─────────────────────────────────────────────────────────────┐
│                    Google Cloud Platform                    │
├─────────────────────────────────────────────────────────────┤
│  ┌─────────────────┐    ┌─────────────────┐                │
│  │   Cloud Load    │    │   Cloud CDN     │                │
│  │   Balancer      │    │                 │                │
│  └─────────────────┘    └─────────────────┘                │
├─────────────────────────────────────────────────────────────┤
│                Google Kubernetes Engine (GKE)              │
│  ┌─────────────────┐    ┌─────────────────┐                │
│  │  AI Malware     │    │   Network IDS   │                │
│  │  Detection      │    │   Service       │                │
│  │  Service        │    │                 │                │
│  └─────────────────┘    └─────────────────┘                │
│  ┌─────────────────┐    ┌─────────────────┐                │
│  │   Dashboard     │    │   Integration   │                │
│  │   Service       │    │   API Gateway   │                │
│  └─────────────────┘    └─────────────────┘                │
├─────────────────────────────────────────────────────────────┤
│  ┌─────────────────┐    ┌─────────────────┐                │
│  │   Cloud SQL     │    │  Cloud Storage  │                │
│  │   (PostgreSQL)  │    │  (Models/Logs)  │                │
│  └─────────────────┘    └─────────────────┘                │
├─────────────────────────────────────────────────────────────┤
│  ┌─────────────────┐    ┌─────────────────┐                │
│  │ Cloud Monitoring│    │  Cloud Logging  │                │
│  │   & Alerting    │    │   & Security    │                │
│  └─────────────────┘    └─────────────────┘                │
└─────────────────────────────────────────────────────────────┘
```

## 🚀 Tính năng chính

### 🔒 Bảo mật toàn diện
- **AI Malware Detection**: Phát hiện malware sử dụng Machine Learning
- **Network IDS**: Giám sát và phát hiện xâm nhập mạng real-time
- **Integrated Dashboard**: Dashboard tích hợp hiển thị cả hai hệ thống
- **Threat Intelligence**: Phân tích và correlation các mối đe dọa

### ☁️ Cloud-native Architecture
- **Microservices**: Kiến trúc microservices trên Kubernetes
- **Auto-scaling**: Tự động scale theo tải
- **High Availability**: Triển khai multi-zone với failover
- **Load Balancing**: Phân tải thông minh với Cloud Load Balancer

### 🔧 DevOps & Automation
- **CI/CD Pipeline**: Tự động build, test và deploy với Cloud Build
- **Infrastructure as Code**: Quản lý hạ tầng với Terraform
- **Container Registry**: Quản lý Docker images với Container Registry
- **Monitoring**: Giám sát toàn diện với Cloud Monitoring

## 📁 Cấu trúc dự án

```
cloud-security-platform/
├── docker/                     # Docker configurations
│   ├── malware-detection/      # Malware detection service
│   ├── network-ids/           # Network IDS service
│   ├── dashboard/             # Integrated dashboard
│   └── api-gateway/           # API Gateway service
├── kubernetes/                # Kubernetes manifests
│   ├── namespaces/           # Namespace definitions
│   ├── deployments/          # Deployment configurations
│   ├── services/             # Service definitions
│   ├── ingress/              # Ingress configurations
│   └── configmaps/           # ConfigMaps and Secrets
├── terraform/                # Infrastructure as Code
│   ├── modules/              # Terraform modules
│   ├── environments/         # Environment-specific configs
│   └── main.tf              # Main Terraform configuration
├── scripts/                  # Deployment and utility scripts
│   ├── deploy.sh            # Main deployment script
│   ├── setup-gcp.sh         # GCP setup script
│   └── monitoring-setup.sh  # Monitoring setup
├── monitoring/               # Monitoring configurations
│   ├── prometheus/          # Prometheus configs
│   ├── grafana/             # Grafana dashboards
│   └── alerting/            # Alert rules
├── security/                 # Security configurations
│   ├── iam/                 # IAM policies
│   ├── network/             # Network security
│   └── secrets/             # Secret management
├── ci-cd/                   # CI/CD configurations
│   ├── cloudbuild.yaml      # Cloud Build configuration
│   ├── triggers/            # Build triggers
│   └── pipelines/           # Deployment pipelines
├── configs/                 # Application configurations
│   ├── production/          # Production configs
│   ├── staging/             # Staging configs
│   └── development/         # Development configs
└── docs/                    # Documentation
    ├── deployment.md        # Deployment guide
    ├── architecture.md      # Architecture documentation
    └── troubleshooting.md   # Troubleshooting guide
```

## 🛠️ Prerequisites

### Google Cloud Platform
- GCP Project với billing enabled
- Các APIs được enable:
  - Kubernetes Engine API
  - Container Registry API
  - Cloud SQL API
  - Cloud Storage API
  - Cloud Monitoring API
  - Cloud Logging API
  - Cloud Build API

### Local Development
- **Docker**: 20.10+
- **kubectl**: 1.24+
- **gcloud CLI**: 400.0+
- **Terraform**: 1.5+
- **Helm**: 3.10+

## 🚀 Quick Start

### 1. Setup GCP Environment

```bash
# Clone repository
git clone <repository-url>
cd cloud-security-platform

# Setup GCP project
./scripts/setup-gcp.sh

# Configure authentication
gcloud auth login
gcloud config set project YOUR_PROJECT_ID
```

### 2. Deploy Infrastructure

```bash
# Initialize Terraform
cd terraform
terraform init

# Plan deployment
terraform plan -var="project_id=YOUR_PROJECT_ID"

# Apply infrastructure
terraform apply -var="project_id=YOUR_PROJECT_ID"
```

### 3. Deploy Applications

```bash
# Build and push Docker images
./scripts/build-images.sh

# Deploy to Kubernetes
./scripts/deploy.sh production
```

### 4. Access Services

```bash
# Get external IP
kubectl get ingress -n security-platform

# Access services
# - Malware Detection: https://malware.your-domain.com
# - Network IDS: https://ids.your-domain.com
# - Dashboard: https://dashboard.your-domain.com
```

## 🔧 Configuration

### Environment Variables

```bash
# GCP Configuration
export PROJECT_ID="your-gcp-project"
export REGION="asia-southeast1"
export ZONE="asia-southeast1-a"
export CLUSTER_NAME="security-platform-cluster"

# Application Configuration
export MALWARE_API_URL="https://malware-api.your-domain.com"
export IDS_API_URL="https://ids-api.your-domain.com"
export DATABASE_URL="********************************/db"
```

### Kubernetes Secrets

```bash
# Create secrets
kubectl create secret generic app-secrets \
  --from-literal=database-url="postgresql://..." \
  --from-literal=api-key="your-api-key" \
  -n security-platform
```

## 📊 Monitoring & Observability

### Metrics
- **Application Metrics**: Custom metrics từ các services
- **Infrastructure Metrics**: CPU, Memory, Network, Disk
- **Business Metrics**: Detection rates, Alert counts, Response times

### Logging
- **Structured Logging**: JSON format với correlation IDs
- **Centralized Logs**: Tất cả logs được gửi đến Cloud Logging
- **Log Analysis**: BigQuery integration cho log analysis

### Alerting
- **SLA Monitoring**: Uptime và performance alerts
- **Security Alerts**: Malware detection và network intrusion alerts
- **Infrastructure Alerts**: Resource utilization và health checks

## 🔒 Security

### Network Security
- **VPC**: Isolated Virtual Private Cloud
- **Firewall Rules**: Restrictive ingress/egress rules
- **Private Clusters**: GKE private cluster với authorized networks

### Identity & Access Management
- **Service Accounts**: Least privilege principle
- **Workload Identity**: Secure pod-to-GCP service authentication
- **RBAC**: Kubernetes Role-Based Access Control

### Data Security
- **Encryption**: Data encrypted at rest và in transit
- **Secret Management**: Google Secret Manager integration
- **Audit Logging**: Comprehensive audit trails

## 🚀 Scaling & Performance

### Auto-scaling
- **Horizontal Pod Autoscaler**: Scale pods based on CPU/memory
- **Vertical Pod Autoscaler**: Optimize resource requests
- **Cluster Autoscaler**: Scale nodes based on demand

### Performance Optimization
- **Resource Limits**: Proper CPU/memory limits
- **Caching**: Redis caching layer
- **CDN**: Cloud CDN for static assets

## 🔄 CI/CD Pipeline

### Build Process
1. **Code Commit**: Push to Git repository
2. **Trigger Build**: Cloud Build automatically triggered
3. **Run Tests**: Unit tests, integration tests, security scans
4. **Build Images**: Docker images built và pushed to Container Registry
5. **Deploy**: Automatic deployment to staging/production

### Deployment Strategy
- **Blue-Green Deployment**: Zero-downtime deployments
- **Canary Releases**: Gradual rollout với monitoring
- **Rollback**: Automatic rollback on failure detection

## 📚 Documentation

- **[Architecture Guide](docs/architecture.md)**: Chi tiết về kiến trúc hệ thống
- **[Deployment Guide](docs/deployment.md)**: Hướng dẫn triển khai từng bước
- **[API Documentation](docs/api.md)**: Tài liệu API endpoints
- **[Monitoring Guide](docs/monitoring.md)**: Setup monitoring và alerting
- **[Security Guide](docs/security.md)**: Best practices bảo mật
- **[Troubleshooting](docs/troubleshooting.md)**: Xử lý sự cố thường gặp

## 🤝 Contributing

1. Fork repository
2. Tạo feature branch (`git checkout -b feature/amazing-feature`)
3. Commit changes (`git commit -m 'Add amazing feature'`)
4. Push to branch (`git push origin feature/amazing-feature`)
5. Create Pull Request

## 📄 License

Distributed under the MIT License. See `LICENSE` for more information.

## 👥 Team

- **Cloud Architecture**: Cloud Infrastructure Team
- **Security Engineering**: Security Research Team
- **DevOps**: Platform Engineering Team
- **Development**: Application Development Team

## 🆘 Support

- **Documentation**: [docs/](docs/)
- **Issues**: GitHub Issues
- **Slack**: #security-platform
- **Email**: <EMAIL>

---

⭐ **Nếu project này hữu ích, hãy cho chúng tôi một star!** ⭐
