#!/bin/bash

# Build and Push Docker Images Script
# Script để build và push tất cả Docker images lên Container Registry

set -e

# Configuration
PROJECT_ID=${PROJECT_ID:-"security-platform-prod"}
REGION=${REGION:-"asia-southeast1"}
REGISTRY_HOSTNAME=${REGISTRY_HOSTNAME:-"gcr.io"}
IMAGE_TAG=${IMAGE_TAG:-"latest"}

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check prerequisites
check_prerequisites() {
    log_info "Checking prerequisites..."
    
    # Check if Docker is installed and running
    if ! command -v docker &> /dev/null; then
        log_error "Docker is not installed"
        exit 1
    fi
    
    if ! docker info &> /dev/null; then
        log_error "Docker daemon is not running"
        exit 1
    fi
    
    # Check if gcloud is installed and authenticated
    if ! command -v gcloud &> /dev/null; then
        log_error "gcloud CLI is not installed"
        exit 1
    fi
    
    if ! gcloud auth list --filter=status:ACTIVE --format="value(account)" | head -n1 &> /dev/null; then
        log_error "gcloud is not authenticated. Run 'gcloud auth login'"
        exit 1
    fi
    
    # Configure Docker to use gcloud as credential helper
    gcloud auth configure-docker --quiet
    
    log_success "Prerequisites check passed"
}

# Build function for each service
build_service() {
    local service_name=$1
    local dockerfile_path=$2
    local context_path=$3
    local image_name="${REGISTRY_HOSTNAME}/${PROJECT_ID}/${service_name}:${IMAGE_TAG}"
    
    log_info "Building ${service_name} image..."
    
    # Build the Docker image
    if docker build -t "${image_name}" -f "${dockerfile_path}" "${context_path}"; then
        log_success "Successfully built ${service_name} image"
        
        # Push to Container Registry
        log_info "Pushing ${service_name} image to Container Registry..."
        if docker push "${image_name}"; then
            log_success "Successfully pushed ${service_name} image"
        else
            log_error "Failed to push ${service_name} image"
            return 1
        fi
    else
        log_error "Failed to build ${service_name} image"
        return 1
    fi
}

# Main build process
main() {
    log_info "Starting Docker image build process..."
    log_info "Project ID: ${PROJECT_ID}"
    log_info "Registry: ${REGISTRY_HOSTNAME}"
    log_info "Image Tag: ${IMAGE_TAG}"
    
    # Check prerequisites
    check_prerequisites
    
    # Get script directory
    SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
    PROJECT_ROOT="$(dirname "${SCRIPT_DIR}")"
    
    # Build AI Malware Detection Service
    log_info "=== Building AI Malware Detection Service ==="
    
    # Copy source files to docker context
    MALWARE_CONTEXT="${PROJECT_ROOT}/docker/malware-detection/build-context"
    mkdir -p "${MALWARE_CONTEXT}"
    
    # Copy malware detection source
    if [ -d "${PROJECT_ROOT}/../ai-malware-detection" ]; then
        cp -r "${PROJECT_ROOT}/../ai-malware-detection/src" "${MALWARE_CONTEXT}/"
        cp "${PROJECT_ROOT}/../ai-malware-detection/config.py" "${MALWARE_CONTEXT}/"
        cp "${PROJECT_ROOT}/../ai-malware-detection"/*.py "${MALWARE_CONTEXT}/" 2>/dev/null || true
    else
        log_error "AI Malware Detection source not found"
        exit 1
    fi
    
    # Copy Docker files
    cp "${PROJECT_ROOT}/docker/malware-detection/requirements.txt" "${MALWARE_CONTEXT}/"
    cp -r "${PROJECT_ROOT}/docker/malware-detection" "${MALWARE_CONTEXT}/docker/"
    
    build_service "malware-detection" \
                  "${PROJECT_ROOT}/docker/malware-detection/Dockerfile" \
                  "${MALWARE_CONTEXT}"
    
    # Cleanup
    rm -rf "${MALWARE_CONTEXT}"
    
    # Build Network IDS Service
    log_info "=== Building Network IDS Service ==="
    
    # Copy source files to docker context
    IDS_CONTEXT="${PROJECT_ROOT}/docker/network-ids/build-context"
    mkdir -p "${IDS_CONTEXT}"
    
    # Copy Network IDS source
    if [ -d "${PROJECT_ROOT}/../network-ids" ]; then
        cp -r "${PROJECT_ROOT}/../network-ids/src" "${IDS_CONTEXT}/"
        cp "${PROJECT_ROOT}/../network-ids/config.py" "${IDS_CONTEXT}/"
        cp "${PROJECT_ROOT}/../network-ids"/*.py "${IDS_CONTEXT}/" 2>/dev/null || true
    else
        log_error "Network IDS source not found"
        exit 1
    fi
    
    # Copy Docker files
    cp "${PROJECT_ROOT}/docker/network-ids/requirements.txt" "${IDS_CONTEXT}/"
    cp -r "${PROJECT_ROOT}/docker/network-ids" "${IDS_CONTEXT}/docker/"
    
    build_service "network-ids" \
                  "${PROJECT_ROOT}/docker/network-ids/Dockerfile" \
                  "${IDS_CONTEXT}"
    
    # Cleanup
    rm -rf "${IDS_CONTEXT}"
    
    # Build Dashboard Service
    log_info "=== Building Dashboard Service ==="
    
    # Copy source files to docker context
    DASHBOARD_CONTEXT="${PROJECT_ROOT}/docker/dashboard/build-context"
    mkdir -p "${DASHBOARD_CONTEXT}"
    
    # Copy dashboard source
    cp "${PROJECT_ROOT}/docker/dashboard/dashboard_app.py" "${DASHBOARD_CONTEXT}/"
    cp "${PROJECT_ROOT}/docker/dashboard/requirements.txt" "${DASHBOARD_CONTEXT}/"
    cp -r "${PROJECT_ROOT}/docker/dashboard" "${DASHBOARD_CONTEXT}/docker/"
    
    # Create basic templates directory
    mkdir -p "${DASHBOARD_CONTEXT}/templates"
    if [ -f "${PROJECT_ROOT}/../network-ids/src/dashboard/templates/dashboard.html" ]; then
        cp "${PROJECT_ROOT}/../network-ids/src/dashboard/templates/dashboard.html" "${DASHBOARD_CONTEXT}/templates/"
    fi
    
    build_service "security-dashboard" \
                  "${PROJECT_ROOT}/docker/dashboard/Dockerfile" \
                  "${DASHBOARD_CONTEXT}"
    
    # Cleanup
    rm -rf "${DASHBOARD_CONTEXT}"
    
    # Build API Gateway (Nginx-based)
    log_info "=== Building API Gateway ==="
    
    # Create API Gateway context
    GATEWAY_CONTEXT="${PROJECT_ROOT}/docker/api-gateway/build-context"
    mkdir -p "${GATEWAY_CONTEXT}"
    
    # Create simple Nginx-based API Gateway
    cat > "${GATEWAY_CONTEXT}/nginx.conf" << 'EOF'
events {
    worker_connections 1024;
}

http {
    upstream malware-detection {
        server malware-detection:5000;
    }
    
    upstream network-ids {
        server network-ids:5001;
    }
    
    upstream dashboard {
        server security-dashboard:8080;
    }
    
    server {
        listen 80;
        
        location /api/malware/ {
            proxy_pass http://malware-detection/api/;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
        }
        
        location /api/ids/ {
            proxy_pass http://network-ids/api/;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
        }
        
        location / {
            proxy_pass http://dashboard/;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
        }
    }
}
EOF
    
    # Create API Gateway Dockerfile
    cat > "${GATEWAY_CONTEXT}/Dockerfile" << 'EOF'
FROM nginx:alpine

COPY nginx.conf /etc/nginx/nginx.conf

EXPOSE 80

CMD ["nginx", "-g", "daemon off;"]
EOF
    
    build_service "api-gateway" \
                  "${GATEWAY_CONTEXT}/Dockerfile" \
                  "${GATEWAY_CONTEXT}"
    
    # Cleanup
    rm -rf "${GATEWAY_CONTEXT}"
    
    log_success "All Docker images built and pushed successfully!"
    
    # List built images
    log_info "Built images:"
    echo "  - ${REGISTRY_HOSTNAME}/${PROJECT_ID}/malware-detection:${IMAGE_TAG}"
    echo "  - ${REGISTRY_HOSTNAME}/${PROJECT_ID}/network-ids:${IMAGE_TAG}"
    echo "  - ${REGISTRY_HOSTNAME}/${PROJECT_ID}/security-dashboard:${IMAGE_TAG}"
    echo "  - ${REGISTRY_HOSTNAME}/${PROJECT_ID}/api-gateway:${IMAGE_TAG}"
}

# Handle script arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        --project-id)
            PROJECT_ID="$2"
            shift 2
            ;;
        --tag)
            IMAGE_TAG="$2"
            shift 2
            ;;
        --registry)
            REGISTRY_HOSTNAME="$2"
            shift 2
            ;;
        --help)
            echo "Usage: $0 [OPTIONS]"
            echo ""
            echo "Options:"
            echo "  --project-id    GCP Project ID (default: security-platform-prod)"
            echo "  --tag          Image tag (default: latest)"
            echo "  --registry     Registry hostname (default: gcr.io)"
            echo "  --help         Show this help message"
            exit 0
            ;;
        *)
            log_error "Unknown option: $1"
            exit 1
            ;;
    esac
done

# Run main function
main
