apiVersion: apps/v1
kind: Deployment
metadata:
  name: grafana
  namespace: security-platform
  labels:
    app: grafana
    component: monitoring
spec:
  replicas: 1
  selector:
    matchLabels:
      app: grafana
  template:
    metadata:
      labels:
        app: grafana
        component: monitoring
    spec:
      serviceAccountName: monitoring-sa
      securityContext:
        runAsNonRoot: true
        runAsUser: 472
        fsGroup: 472
      containers:
      - name: grafana
        image: grafana/grafana:10.0.0
        ports:
        - containerPort: 3000
          name: http
        env:
        - name: GF_SECURITY_ADMIN_PASSWORD
          valueFrom:
            secretKeyRef:
              name: monitoring-secrets
              key: GRAFANA_ADMIN_PASSWORD
        - name: GF_INSTALL_PLUGINS
          value: "grafana-piechart-panel,grafana-worldmap-panel,grafana-clock-panel"
        - name: GF_SERVER_ROOT_URL
          value: "http://grafana.security-platform.local"
        - name: GF_ANALYTICS_REPORTING_ENABLED
          value: "false"
        - name: GF_ANALYTICS_CHECK_FOR_UPDATES
          value: "false"
        - name: GF_USERS_ALLOW_SIGN_UP
          value: "false"
        - name: GF_USERS_AUTO_ASSIGN_ORG
          value: "true"
        - name: GF_USERS_AUTO_ASSIGN_ORG_ROLE
          value: "Viewer"
        resources:
          requests:
            cpu: 200m
            memory: 512Mi
          limits:
            cpu: 1000m
            memory: 2Gi
        volumeMounts:
        - name: grafana-storage
          mountPath: /var/lib/grafana
        - name: grafana-config
          mountPath: /etc/grafana/grafana.ini
          subPath: grafana.ini
        - name: grafana-datasources
          mountPath: /etc/grafana/provisioning/datasources
        - name: grafana-dashboards-config
          mountPath: /etc/grafana/provisioning/dashboards
        - name: grafana-dashboards
          mountPath: /var/lib/grafana/dashboards
        livenessProbe:
          httpGet:
            path: /api/health
            port: 3000
          initialDelaySeconds: 60
          timeoutSeconds: 30
        readinessProbe:
          httpGet:
            path: /api/health
            port: 3000
          initialDelaySeconds: 30
          timeoutSeconds: 30
      volumes:
      - name: grafana-storage
        persistentVolumeClaim:
          claimName: grafana-storage-pvc
      - name: grafana-config
        configMap:
          name: grafana-config
      - name: grafana-datasources
        configMap:
          name: grafana-datasources
      - name: grafana-dashboards-config
        configMap:
          name: grafana-dashboards-config
      - name: grafana-dashboards
        configMap:
          name: grafana-dashboards
---
apiVersion: v1
kind: Service
metadata:
  name: grafana-service
  namespace: security-platform
  labels:
    app: grafana
    component: monitoring
spec:
  type: ClusterIP
  ports:
  - port: 3000
    targetPort: 3000
    protocol: TCP
    name: http
  selector:
    app: grafana
---
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: grafana-storage-pvc
  namespace: security-platform
  labels:
    app: grafana
    component: monitoring
spec:
  accessModes:
    - ReadWriteOnce
  resources:
    requests:
      storage: 10Gi
  storageClassName: ssd-retain
---
apiVersion: v1
kind: ConfigMap
metadata:
  name: grafana-config
  namespace: security-platform
  labels:
    app: grafana
    component: monitoring
data:
  grafana.ini: |
    [analytics]
    check_for_updates = false
    reporting_enabled = false
    
    [security]
    admin_user = admin
    cookie_secure = false
    cookie_samesite = lax
    
    [users]
    allow_sign_up = false
    auto_assign_org = true
    auto_assign_org_role = Viewer
    
    [auth.anonymous]
    enabled = false
    
    [log]
    mode = console
    level = info
    
    [paths]
    data = /var/lib/grafana
    logs = /var/log/grafana
    plugins = /var/lib/grafana/plugins
    provisioning = /etc/grafana/provisioning
    
    [server]
    http_port = 3000
    domain = grafana.security-platform.local
    
    [database]
    type = sqlite3
    path = grafana.db
    
    [session]
    provider = file
    provider_config = sessions
    
    [alerting]
    enabled = true
    execute_alerts = true
---
apiVersion: v1
kind: ConfigMap
metadata:
  name: grafana-datasources
  namespace: security-platform
  labels:
    app: grafana
    component: monitoring
data:
  datasources.yaml: |
    apiVersion: 1
    datasources:
    - name: Prometheus
      type: prometheus
      access: proxy
      url: http://prometheus-service:9090
      isDefault: true
      editable: true
      jsonData:
        timeInterval: "15s"
        queryTimeout: "60s"
        httpMethod: "POST"
---
apiVersion: v1
kind: ConfigMap
metadata:
  name: grafana-dashboards-config
  namespace: security-platform
  labels:
    app: grafana
    component: monitoring
data:
  dashboards.yaml: |
    apiVersion: 1
    providers:
    - name: 'security-platform-dashboards'
      orgId: 1
      folder: 'Security Platform'
      type: file
      disableDeletion: false
      updateIntervalSeconds: 10
      allowUiUpdates: true
      options:
        path: /var/lib/grafana/dashboards
---
apiVersion: v1
kind: ConfigMap
metadata:
  name: grafana-dashboards
  namespace: security-platform
  labels:
    app: grafana
    component: monitoring
data:
  security-overview.json: |
    {
      "dashboard": {
        "id": null,
        "title": "Security Platform Overview",
        "tags": ["security", "overview"],
        "timezone": "browser",
        "panels": [
          {
            "id": 1,
            "title": "Service Status",
            "type": "stat",
            "targets": [
              {
                "expr": "up{job=~\"malware-detection|network-ids|security-dashboard\"}",
                "legendFormat": "{{job}}"
              }
            ],
            "fieldConfig": {
              "defaults": {
                "color": {
                  "mode": "thresholds"
                },
                "thresholds": {
                  "steps": [
                    {"color": "red", "value": 0},
                    {"color": "green", "value": 1}
                  ]
                }
              }
            },
            "gridPos": {"h": 8, "w": 12, "x": 0, "y": 0}
          },
          {
            "id": 2,
            "title": "Malware Detections (Last 24h)",
            "type": "stat",
            "targets": [
              {
                "expr": "increase(malware_detections_total[24h])",
                "legendFormat": "Detections"
              }
            ],
            "fieldConfig": {
              "defaults": {
                "color": {
                  "mode": "thresholds"
                },
                "thresholds": {
                  "steps": [
                    {"color": "green", "value": 0},
                    {"color": "yellow", "value": 10},
                    {"color": "red", "value": 50}
                  ]
                }
              }
            },
            "gridPos": {"h": 8, "w": 12, "x": 12, "y": 0}
          },
          {
            "id": 3,
            "title": "Network Intrusions (Last 24h)",
            "type": "stat",
            "targets": [
              {
                "expr": "increase(network_intrusions_total[24h])",
                "legendFormat": "Intrusions"
              }
            ],
            "fieldConfig": {
              "defaults": {
                "color": {
                  "mode": "thresholds"
                },
                "thresholds": {
                  "steps": [
                    {"color": "green", "value": 0},
                    {"color": "red", "value": 1}
                  ]
                }
              }
            },
            "gridPos": {"h": 8, "w": 12, "x": 0, "y": 8}
          },
          {
            "id": 4,
            "title": "System Resource Usage",
            "type": "graph",
            "targets": [
              {
                "expr": "rate(container_cpu_usage_seconds_total{namespace=\"security-platform\"}[5m]) * 100",
                "legendFormat": "CPU % - {{pod}}"
              },
              {
                "expr": "(container_memory_usage_bytes{namespace=\"security-platform\"} / container_spec_memory_limit_bytes) * 100",
                "legendFormat": "Memory % - {{pod}}"
              }
            ],
            "yAxes": [
              {
                "label": "Percentage",
                "max": 100,
                "min": 0
              }
            ],
            "gridPos": {"h": 8, "w": 12, "x": 12, "y": 8}
          }
        ],
        "time": {
          "from": "now-6h",
          "to": "now"
        },
        "refresh": "30s"
      }
    }
