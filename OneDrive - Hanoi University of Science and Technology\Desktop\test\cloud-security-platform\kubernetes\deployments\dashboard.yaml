apiVersion: apps/v1
kind: Deployment
metadata:
  name: security-dashboard
  namespace: security-platform
  labels:
    app: security-dashboard
    component: frontend
    version: v1
spec:
  replicas: 2
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxUnavailable: 1
      maxSurge: 1
  selector:
    matchLabels:
      app: security-dashboard
  template:
    metadata:
      labels:
        app: security-dashboard
        component: frontend
        version: v1
      annotations:
        prometheus.io/scrape: "true"
        prometheus.io/port: "9090"
        prometheus.io/path: "/metrics"
    spec:
      serviceAccountName: security-platform-sa
      securityContext:
        runAsNonRoot: true
        runAsUser: 1000
        fsGroup: 1000
      containers:
      - name: security-dashboard
        image: gcr.io/security-platform-prod/security-dashboard:latest
        imagePullPolicy: Always
        ports:
        - containerPort: 8080
          name: http
          protocol: TCP
        - containerPort: 9090
          name: metrics
          protocol: TCP
        envFrom:
        - configMapRef:
            name: dashboard-config
        - configMapRef:
            name: shared-config
        - secretRef:
            name: app-secrets
        resources:
          requests:
            cpu: 200m
            memory: 512Mi
          limits:
            cpu: 1000m
            memory: 2Gi
        livenessProbe:
          httpGet:
            path: /health
            port: 8080
          initialDelaySeconds: 30
          periodSeconds: 30
          timeoutSeconds: 10
          failureThreshold: 3
        readinessProbe:
          httpGet:
            path: /health
            port: 8080
          initialDelaySeconds: 15
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 3
        volumeMounts:
        - name: logs-storage
          mountPath: /app/logs
        - name: static-content
          mountPath: /app/static
      
      volumes:
      - name: logs-storage
        persistentVolumeClaim:
          claimName: logs-pvc
      - name: static-content
        emptyDir: {}
      
      nodeSelector:
        cloud.google.com/gke-nodepool: security-platform-nodes
      
      affinity:
        podAntiAffinity:
          preferredDuringSchedulingIgnoredDuringExecution:
          - weight: 100
            podAffinityTerm:
              labelSelector:
                matchExpressions:
                - key: app
                  operator: In
                  values:
                  - security-dashboard
              topologyKey: kubernetes.io/hostname
---
apiVersion: v1
kind: Service
metadata:
  name: dashboard-service
  namespace: security-platform
  labels:
    app: security-dashboard
    component: frontend
  annotations:
    prometheus.io/scrape: "true"
    prometheus.io/port: "9090"
spec:
  type: ClusterIP
  ports:
  - port: 8080
    targetPort: 8080
    protocol: TCP
    name: http
  - port: 9090
    targetPort: 9090
    protocol: TCP
    name: metrics
  selector:
    app: security-dashboard
---
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: security-dashboard-hpa
  namespace: security-platform
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: security-dashboard
  minReplicas: 2
  maxReplicas: 5
  metrics:
  - type: Resource
    resource:
      name: cpu
      target:
        type: Utilization
        averageUtilization: 70
  - type: Resource
    resource:
      name: memory
      target:
        type: Utilization
        averageUtilization: 80
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: api-gateway
  namespace: security-platform
  labels:
    app: api-gateway
    component: gateway
    version: v1
spec:
  replicas: 2
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxUnavailable: 1
      maxSurge: 1
  selector:
    matchLabels:
      app: api-gateway
  template:
    metadata:
      labels:
        app: api-gateway
        component: gateway
        version: v1
    spec:
      containers:
      - name: nginx
        image: gcr.io/security-platform-prod/api-gateway:latest
        ports:
        - containerPort: 80
          name: http
          protocol: TCP
        resources:
          requests:
            cpu: 200m
            memory: 512Mi
          limits:
            cpu: 1000m
            memory: 2Gi
        livenessProbe:
          httpGet:
            path: /health
            port: 80
          initialDelaySeconds: 30
          periodSeconds: 30
        readinessProbe:
          httpGet:
            path: /health
            port: 80
          initialDelaySeconds: 5
          periodSeconds: 10
        volumeMounts:
        - name: nginx-config
          mountPath: /etc/nginx/nginx.conf
          subPath: nginx.conf
      
      volumes:
      - name: nginx-config
        configMap:
          name: nginx-config
      
      nodeSelector:
        cloud.google.com/gke-nodepool: security-platform-nodes
---
apiVersion: v1
kind: Service
metadata:
  name: api-gateway-service
  namespace: security-platform
  labels:
    app: api-gateway
    component: gateway
spec:
  type: LoadBalancer
  ports:
  - port: 80
    targetPort: 80
    protocol: TCP
    name: http
  selector:
    app: api-gateway
  loadBalancerSourceRanges:
  - 0.0.0.0/0  # Restrict this in production
